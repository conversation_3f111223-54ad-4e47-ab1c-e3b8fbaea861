//左右滑动筛选
$(function() {
$('.range-slider').jRange({
    from: 0,
    to: 100,
    step: 1,
    scale: [0, 25, 50, 75, 100],
    format: '%s',
    width: 300,
    showLabels: true,
    isRange: true
});
$("#filter").click(function() {
    var rangeVal = $(".range-slider").val(),
        rangeValue = "";
    rangeValue = rangeVal.split(",");
    $("#filterStock").hide();
    //获取父页面传过来的id
    var idPre = window.location.hash.split("?")[0],
        namePre = window.location.hash.split("?")[1],
        idAfter,
        nameAfter;
    idAfter = idPre.lastIndexOf("\/"), nameAfter = namePre.lastIndexOf("\=");
    idPre = idPre.substring(idAfter + 1, idPre.length),
    namePre = namePre.substring(nameAfter + 1, namePre.length);
    namePre = decodeURI(namePre);
    var min = rangeValue[0],
        max = rangeValue[1];
    //    myChart.clear(); //清空图表
    //重置echart图谱
    $(".container").html('<div id="main" style="width: 100%;height:100%;"></div>');
    // myChart = echarts.init(document.getElementById('main'));

    $(document).ready(function() {
        resizeScreen(); //屏幕适应
        myChart = echarts.init(document.getElementById('main'));
        getFilterData(min, max); //获取数据
    });
    var has_root = false;
    var has_touzi = false;
    $(".filterTip .firm").text(namePre);
    $(".filterTip .min").text(min);
    $(".filterTip .max").text(max);
    $(".filterTip").show();
    //请求后台数据
    function getFilterData(min, max) {
        //初始化企业图谱
        $(".container").html('<div id="main" style="width: 100%;height:100%;"></div>');
        myChart = echarts.init(document.getElementById('main'));
        //请求后台接口
        var url = dataUrl + "/api/bizapp/biz/portrait/relationTree";
        var href = window.location.href
        var arr = href.split("?")
        var arr2 = arr[2].split("&")
        let obj = {};
        arr2.forEach(j => {
            let [key, value] = j.split('=');
            obj[key] = value;
        });
        var token = obj.token
        var xhr = new XMLHttpRequest();
        xhr.open('GET', url + '?year=2022&month=2&entityId=20161202135550155675&isCurrentVersion=1', true);
        // xhr.responseType = 'json';
        xhr.overrideMimeType('text/plain')
        xhr.setRequestHeader('token', token)
        xhr.setRequestHeader('accept', 'application/json')
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4) { // 请求已完成
                if (xhr.status === 200) { // 响应成功
                    // 处理响应数据
                    let res = xhr.response ? JSON.parse(xhr.response) : ''
                    const data = {
                        ...res.data,
                        Result: res,
                        Status: res.code,
                    }
                    // const data = {
                    //     Result: res,
                    //     Status: res.code,
                    //     KeyNo: res.data.entityId,
                    //     Name: res.data.simpOrgName,
                    //     Sontotal: res.data.Sontotal,
                    //     touzi: res.data.investList,
                    //     DetailList: res.data.upperList,
                    //     entityId: res.data.entityId,
                    //     loopIds: res.data.loopIds,
                    //     versionId: res.data.versionId
                    // }
                    $('#load_data').hide();
                    if (data.Status === 200) {
                        $('#no_data').hide();
                        if (data.Result != "") {
                            rootData = data;
                        } else {
                            $(".tooltipW").text("没有找到企业关系!")
                            tooltipW();
                            rootData = [];
                            return;
                        }
                        if ((!rootData.DetailList || rootData.DetailList.length == 0) && (!rootData.touzi || rootData.touzi.length == 0)) {
                            $(".tooltipW").text("没有找到企业关系!")
                            tooltipW();
                            return;
                        }
                        if (rootData.DetailList && rootData.DetailList.length > 0)
                            has_root = true;
                        if (rootData.touzi && rootData.touzi.length > 0)
                            has_touzi = true;
                        loopIds = rootData.loopIds;
                        // rootData = data.Result;
                        transTree(rootData, 1);
                        initTree(rootData);
                        transTouzi(rootData);
                        if (touziData) initTree(touziData);
                        drawGuquan(rootData);
                        focusTarget(focusX, focusY);
                    } else {
                        $('#no_data').show();
                    }
                    
                } else {
                    console.error('Request failed with status:', xhr.status);
                }
            }
        };
        xhr.send();
        // $.ajax({
        //     url: url,
        //     type: 'GET',
        //     headers: {
        //         'token': token
        //     },
        //     beforeSend: function(xhr) {
        //         xhr.responseType = 'json';
        //         xhr.setRequestHeader('Accept', 'application/json');
        //     },
        //     data: {
        //         year: '2022',
        //         month: '2',
        //         entityId: '20161202135550155675',
        //         isCurrentVersion: 1,
        //         proportionMin: min,
        //         proportionMax: max
        //     },
        //     dataType: 'JSON',
        //     success: function(page, type, res) {
        //         const data = {
        //             Result: res,
        //             Status: res.status,
        //             KeyNo: res.entityId,
        //             Name: res.simpOrgName,
        //             Sontotal: res.Sontotal,
        //             touzi: res.investList,
        //             DetailList: res.upperList,
        //             entityId: res.entityId,
        //             loopIds: res.loopIds,
        //             versionId: res.versionId
        //         }
        //         $('#load_data').hide();
        //         if (data.Status === 200) {
        //             $('#no_data').hide();
        //             if (data.Result != "") {
        //                 rootData = data;
        //             } else {
        //                 $(".tooltipW").text("没有找到企业关系!")
        //                 tooltipW();
        //                 rootData = [];
        //                 return;
        //             }
        //             if ((!rootData.DetailList || rootData.DetailList.length == 0) && (!rootData.touzi || rootData.touzi.length == 0)) {
        //                 $(".tooltipW").text("没有找到企业关系!")
        //                 tooltipW();
        //                 return;
        //             }
        //             if (rootData.DetailList && rootData.DetailList.length > 0)
        //                 has_root = true;
        //             if (rootData.touzi && rootData.touzi.length > 0)
        //                 has_touzi = true;
        //             // rootData = data.Result;
        //             transTree(rootData, 1);
        //             initTree(rootData);
        //             transTouzi(rootData);
        //             if (touziData) initTree(touziData);
        //             drawGuquan(rootData);
        //             refresh();
        //         } else {
        //             // $('#no_data').show();
        //             $(".tooltipW").text("没有找到企业关系!")
        //             tooltipW();
        //         }
        //     }
        // });
    }
});
});
//确认筛选时，用隐藏筛选区域
function filter() {
    // $("#filterStock").show();
    if ($("#filterStock").css("display") == "none") {
        $("#filterStock").css("display", "block")
    } else {
        $("#filterStock").css("display", "none")
    }
}
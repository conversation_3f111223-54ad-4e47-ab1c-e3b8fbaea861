var myChart;
var showKeyNo;
var ImageShape = require('zrender/shape/Image');
var Rectangle = require('zrender/shape/Rectangle');
var treeId = 1; //树杈的ID，随循环增加
//获取父页面传过来的id
// var idPre = window.location.hash.split("?")[0],
//     namePre = window.location.hash.split("?")[1],
//     idAfter,
//     nameAfter;
// idAfter = idPre.lastIndexOf("\/"), nameAfter = namePre.lastIndexOf("\=");
// idPre = idPre.substring(idAfter + 1, idPre.length),
// namePre = namePre.substring(nameAfter + 1, namePre.length);
// namePre = decodeURI(namePre);

var unfold;
var focusKeyNo;
if (!window.localStorage) {
    console.log('不支持localStorage');
}else {
    var storage = window.localStorage;
    unfold = storage.getItem('unfold');
    storage.removeItem('unfold');
    focusKeyNo = storage.getItem('focusKeyNo');
    storage.removeItem('focusKeyNo');
}
var focusTreeId;//跳转节点的treeId
var focusX;
var focusY;
var loopIds;
//一开始加载页面时执行的方法
$(document).ready(function() {
    resizeScreen(); //屏幕适应
    myChart = echarts.init(document.getElementById('main'));
    getData(); //获取数据
});
window.onresize = function() {
resizeScreen();
}
var rootData; //根数据，包含投资与被投资
var touziData; //投资部分数据
var has_root = false;
var has_touzi = false
//请求后台数据
var idPre = ''
function getData(min, max) {
    $(".container").html('<div id="main" style="width: 100%;height:100%;"></div>');
    myChart = echarts.init(document.getElementById('main'));
    // 测试环境 10.76.3.74:8081
    // var url = "http://10.36.44.79:9003/api/bizapp/biz/portrait/relationTree";
    var url = dataUrl + "/api/bizapp/biz/portrait/relationTree";
    var href = window.location.href
	var arr = href.split("?")
	var arr2 = arr[1].split("&")
    let obj = {};
    arr2.forEach(j => {
        let [key, value] = j.split('=');
        obj[key] = value;
    });
    idPre = obj.entityId
	var token = obj.token

    var xhr = new XMLHttpRequest();
    xhr.open('GET', url + '?year='+ obj.year +'&month='+ obj.month +'&entityId='+ obj.entityId +'&isCurrentVersion='+ obj.isCurrentVersion, true);
    // xhr.responseType = 'json';
    xhr.overrideMimeType('text/plain')
    xhr.setRequestHeader('token', token)
    xhr.setRequestHeader('accept', 'application/json')
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4) { // 请求已完成
            if (xhr.status === 200) { // 响应成功
                // 处理响应数据
                let res = xhr.response ? JSON.parse(xhr.response) : ''
                const data = {
                    ...res.data,
                    Result: res,
                    Status: res.code,
                }
                // const data = {
                //     Result: res,
                //     Status: res.code,
                //     KeyNo: res.data.entityId,
                //     Name: res.data.simpOrgName,
                //     Sontotal: res.data.Sontotal,
                //     touzi: res.data.investList,
                //     DetailList: res.data.upperList,
                //     entityId: res.data.entityId,
                //     loopIds: res.data.loopIds,
                //     versionId: res.data.versionId
                // }
                $('#load_data').hide();
                if (data.Status === 200) {
                    $('#no_data').hide();
                    if (data.Result != "") {
                        rootData = data;
                    } else {
                        $(".tooltipW").text("没有找到企业关系!")
                        tooltipW();
                        rootData = [];
                        return;
                    }
                    if ((!rootData.DetailList || rootData.DetailList.length == 0) && (!rootData.touzi || rootData.touzi.length == 0)) {
                        $(".tooltipW").text("没有找到企业关系!")
                        tooltipW();
                        return;
                    }
                    if (rootData.DetailList && rootData.DetailList.length > 0)
                        has_root = true;
                    if (rootData.touzi && rootData.touzi.length > 0)
                        has_touzi = true;
                    loopIds = rootData.loopIds;
                    // rootData = data.Result;
                    transTree(rootData, 1);
                    initTree(rootData);
                    transTouzi(rootData);
                    if (touziData) initTree(touziData);
                    drawGuquan(rootData);
                    focusTarget(focusX, focusY);
                } else {
                    $('#no_data').show();
                }
                
            } else {
                console.error('Request failed with status:', xhr.status);
            }
        }
    };
    xhr.send();
    // $.ajax({
    //     url: url,
    //     type: 'GET',
    //     headers: {
    //         'token': token
    //     },
    //     beforeSend: function(xhr) {
    //         xhr.responseType = 'json';
    //         xhr.setRequestHeader('Accept', 'application/json')
    //     },
    //     data: {
    //         year: '2022',
	// 		month: '2',
	// 		entityId: '20161202135550155675',
	// 		isCurrentVersion: 1
    //         // enterpriseId: idPre,
    //         // enterpriseName: namePre,
    //         // proportionMin: min,
    //         // proportionMax: max
    //     },
    //     success: function(page, type, res) {
    //         //console.log(data)
    //         const data = {
    //             Result: res,
    //             Status: res.status,
    //             KeyNo: res.entityId,
    //             Name: res.simpOrgName,
    //             Sontotal: res.Sontotal,
    //             touzi: res.investList,
    //             DetailList: res.upperList,
    //             entityId: res.entityId,
    //             loopIds: res.loopIds,
    //             versionId: res.versionId
    //         }
    //         $('#load_data').hide();
    //         if (data.Status === 200) {
    //             $('#no_data').hide();
    //             if (data.Result != "") {
    //                 rootData = data;
    //             } else {
    //                 $(".tooltipW").text("没有找到企业关系!")
    //                 tooltipW();
    //                 rootData = [];
    //                 return;
    //             }
    //             if ((!rootData.DetailList || rootData.DetailList.length == 0) && (!rootData.touzi || rootData.touzi.length == 0)) {
    //                 $(".tooltipW").text("没有找到企业关系!")
    //                 tooltipW();
    //                 return;
    //             }
    //             if (rootData.DetailList && rootData.DetailList.length > 0)
    //                 has_root = true;
    //             if (rootData.touzi && rootData.touzi.length > 0)
    //                 has_touzi = true;
    //             loopIds = rootData.loopIds;
    //             // rootData = data.Result;
    //             transTree(rootData, 1);
    //             initTree(rootData);
    //             transTouzi(rootData);
    //             if (touziData) initTree(touziData);
    //             drawGuquan(rootData);
    //             focusTarget(focusX, focusY);
    //         } else {
    //             $('#no_data').show();
    //         }
    //     }
    // });
}

function maoRefresh() {
    //刷新
    refresh();
}
function refresh() {
    //刷新事件，不怎么看得懂，看来是调度了RESTORE这个方法，没找到相关文档
    var ecConfig = require('echarts/config');
    myChart._messageCenter.dispatch(ecConfig.EVENT.RESTORE, null, null, myChart);
}

function maoScale(type) {
    //放大缩小
    var centerX = myChart.getZrender().getWidth() / 2;
    var centerY = myChart.getZrender().getHeight() / 2;
    var layer = myChart.getZrender().painter._layers[1];
    var x = layer.scale[0] * centerX + layer.position[0];
    var y = layer.scale[1] * centerY + layer.position[1];
    var scale = layer.scale[0];
    if (type == 1) {
        scale += 0.3;
    } else if (type == 2) {
        scale -= 0.3;
    }
    if (scale >= 0.3 && scale <= 2) {
        layer.scale[0] = scale;
        layer.scale[1] = scale;
        myChart.getZrender().render();
        layer.position[0] = x - layer.scale[0] * centerX
        layer.position[1] = y - layer.scale[1] * centerY
        myChart.getZrender().render();
    }
}
//封装提示框
function tooltipW() {
    $(".tooltipW").show();
    setTimeout(function() {
        $(".tooltipW").fadeOut("slow");;
    }, 800)
}
// 只显示一级
function show_one(group) {
    //只列上一级/下一级;group=1/2
    if (group == 1 && has_root) {
        if (has_root) getNode1(rootData);
        if (has_touzi) getNode1(touziData);
        getNode2(rootData);
    } else if (group == 1 && !has_root) {
        $(".tooltipW").text("警告哦，没有上级了!")
        tooltipW();
    } else if (group == 2 && has_touzi) {
        if (has_root) getNode1(rootData);
        if (has_touzi) getNode1(touziData);
        getNode2(touziData);
    } else if (group == 2 && !has_touzi) {
        $(".tooltipW").text("警告哦，没有下级了!")
        tooltipW();
    }
    //深度遍历-收起
    function getNode1(data) {
        if (data.children) {
            for (var i = 0; i < data.children.length; i++) {
                getNode1(data.children[i]); //对子集遍历
            }
        }
        if (data._children) {
            for (var j = 0; j < data._children.length; j++) {
                getNode1(data._children[j]); //对子集遍历
            }
        }
        if (data.children && data.children.length > 0 && data.KeyNo) {
            //收起全部的树杈
            data._children = data.children;
            data.children = null;
            data.extend = 1;
        }
    }
    //单层遍历-展开
    function getNode2(data) {
        //展开单层的树杈
        data.children = data._children;
        data._children = null;
        data.extend = 2;
        if (data.children && data.children.length > 0) {
            for (var i = 0; i < data.children.length; i++) {
                if (data.children[i]._children) {
                    data.children[i].children = data.children[i]._children;
                    data.children[i]._children = null;
                }
            }
        }
    }
    // if (rootData.children == null) rootData.children = [];
    // if (touziData.children == null) touziData.children = [];
    // myChart.clear();//清空图表
    // myChart.setOption(option); //配置图表
    // initZrender();//配置展开按钮
    // if (rootData.children.length == 0) rootData.children == null;
    // if (touziData.children.length == 0) touziData.children == null;

    //如果一个系列数据全部收起在配置图表时会报错，所以先赋空集后赋空值
    if (rootData && rootData.children == null)
        rootData.children = [];
    if (touziData && touziData.children == null)
        touziData.children = [];
    myChart.clear(); //清空图表
    myChart.setOption(option); //配置图表
    initZrender(); //配置展开按钮
    if (rootData && rootData.children && rootData.children.length == 0) rootData.children == null;
    if (touziData && touziData.children && touziData.children.length == 0) touziData.children == null;
}
function show_all(group) {
    //全部上级/全部下级/全部展开;group=1/2/0
    if (group == 1 && has_root) {
        if (has_touzi) getNode1(touziData);
        getNode2(rootData);
    } else if (group == 1 && !has_root) {
        $(".tooltipW").text("警告哦，没有全部上级了!")
        tooltipW();
    } else if (group == 2 && has_touzi) {
        if (has_root) getNode1(rootData);
        getNode2(touziData);
    } else if (group == 2 && !has_touzi) {
        $(".tooltipW").text("警告哦，没有全部下级了!")
        tooltipW();
    } else if (group == 0 && (has_root || has_touzi)) {
        if (has_root) getNode2(rootData);
        if (has_touzi) getNode2(touziData);
    } else {
        $(".tooltipW").text("警告哦，无可展开的了!")
        tooltipW();
    }
    ;
    //深度遍历-收起
    function getNode1(data) {
        if (data.children && data.children.length > 0) {
            for (var i = 0; i < data.children.length; i++) {
                getNode1(data.children[i]); //对子集遍历
            }
        }
        if (data._children && data._children.length > 0) {
            for (var j = 0; j < data._children.length; j++) {
                getNode1(data._children[j]); //对子集遍历
            }
        }
        if (data.children && data.children.length > 0 && data.KeyNo) {
            //收起全部的树杈
            data._children = data.children;
            data.children = null;
            data.extend = 1;
        }
    }
    //深度遍历-展开
    function getNode2(data) {
        if (data._children) {
            for (var i = 0; i < data._children.length; i++) {
                getNode2(data._children[i]); //对子集遍历
            }
        }
        if (data.children) {
            for (var j = 0; j < data.children.length; j++) {
                getNode2(data.children[j]); //对子集遍历
            }
        }
        if (data._children && data._children.length > 0 && data.KeyNo) {
            //展开全部的树杈
            data.children = data._children;
            data._children = null;
            data.extend = 2;
        }
    }
    if (rootData && rootData.children == null)
        rootData.children = [];
    if (touziData && touziData.children == null)
        touziData.children = [];
    myChart.clear(); //清空图表
    myChart.setOption(option); //配置图表
    initZrender(); //配置展开按钮
    if (rootData && rootData.children && rootData.children.length == 0) rootData.children == null;
    if (touziData && touziData.children && touziData.children.length == 0) touziData.children == null;
}
function transTouzi(rootData) {
    //对数据的下级进行处理
    if (rootData.touzi && rootData.touzi.length > 0) {
        touziData = {};
        touziData.Name = rootData.Name;
        touziData.DetailList = rootData.touzi; //将下级赋值到DetailList
        //同样将中心公司作为touziData的父项
        transTree(touziData, 2); //对下级进行如同上级的深度遍历
        touziData.name = rootData.Name; //命名
        touziData.Sontotal = rootData.Sontotal; //下级总数
        touziData.labelClick = false; //文字不可点击
        touziData.clickable = false; //块不可点击
        touziData.symbolSize = [touziData.name.length * 16 + 50, 50]; //size
        touziData.symbol = 'rectangle';
        touziData.itemStyle = {
            normal: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
                label: {
                    show: true,
                    position: "inside",
                    textStyle: {
                        color: '#000',
                        fontFamily: "MicroSoft YaHei",
                        fontSize: 16,
                        fontStyle: "normal",
                    },
                }
            },
            emphasis: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
            }
        };
    }
}
function transTree(data, group) {
    //group为分组标识，1为上级，2为下级
    //深度遍历循环，对数据每一层做如下操作
    //将DetailList赋值到children并将DetailList清空
    data.children = data.DetailList;
    data.DetailList = undefined;
    data.treeId = treeId;
    var fontSize = 14;
    //字体大小、换行符和省略符
    if (parseInt(data.Sontotal) > 0) {
        data.name = (data.Name + "\n直属下级：" + data.Sontotal).replace(/(.{7})(?=.)/g, '$1\n');
    } else {
        data.name = data.Name.replace(/(.{7})(?=.)/g, '$1\n');
    }
    // data.name = (data.Name+"\n直属下级："+data.Sontotal).replace(/(.{7})(?=.)/g, '$1\n');
    // data.name = data.Name.replace(/(.{7})(?=.)/g, '$1\n');
    //正则判断是否有中文
    var reg = /[\u4E00-\u9FA5\uF900-\uFA2D]/;
    if (reg.test(data.Name)) {
        if (data.Name.length > 36) {
            var fontSize = 12;
            data.name = (data.Name.substr(0, 35) + '…').replace(/(.{9})(?=.)/g, '$1\n');
        } else if (data.Name.length > 21 && data.Name.length <= 36) {
            var fontSize = 12;
            data.name = data.Name.replace(/(.{9})(?=.)/g, '$1\n');
        }
    }else{
        if (data.Name.length > 36) {
            var fontSize = 12;
            if(parseInt(data.Sontotal) > 0){
                data.name = (data.Name.substr(0, 20) +'…'+ "\n直属下级：" + data.Sontotal).replace(/(.{16})(?=.)/g, '$1\n');
            }else{
                data.name = (data.Name.substr(0, 20) + '…').replace(/(.{16})(?=.)/g, '$1\n');
            }
            // data.name = (data.Name.substr(0, 20) + '…').replace(/(.{16})(?=.)/g, '$1\n');
        } else if (data.Name.length > 21 && data.Name.length <= 36) {
            var fontSize = 12;
            if(parseInt(data.Sontotal) > 0){
                data.name = (data.Name + "\n直属下级：" + data.Sontotal ).replace(/(.{18})(?=.)/g, '$1\n');
            }else{
                data.name = data.Name.replace(/(.{18})(?=.)/g, '$1\n');
            }
        }
    }
    data.group = group; //分组赋值
    var children = data.children;
    if (!children) {
        //无子集项样式
        data.symbol = 'rectangle';
        data.symbolSize = [130, 62],
        data.labelClick = true;
        data.itemStyle = {
            normal: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
                label: {
                    show: true,
                    position: "inside",
                    textStyle: {
                        fontFamily: "MicroSoft YaHei",
                        fontSize: fontSize,
                        color: '#333',
                        fontStyle: "normal",
                    },
                }
            },
            emphasis: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
            }
        };
    } else {
        //有子集项样式
        data.symbol = 'rectangle';
        data.symbolSize = [130, 62],
        data.labelClick = true;
        data.itemStyle = {
            normal: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
                label: {
                    show: true,
                    position: "inside",
                    textStyle: {
                        fontFamily: "MicroSoft YaHei",
                        fontSize: fontSize,
                        color: '#333',
                        fontStyle: "normal",
                    },
                }
            },
            emphasis: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
            }
        };
        //获取聚焦节点的treeId，并修改背景色
        if (focusKeyNo && data.KeyNo == focusKeyNo) {
            focusTreeId = treeId;
            // console.log("focusTreeId====>>>>>>>>>" + focusTreeId);
            data.itemStyle = {
                normal: {
                    color: "#fd485e",
                    borderWidth: "1",
                    borderColor: "#fd485e",
                    label: {
                        show: true,
                        position: "inside",
                        textStyle: {
                            fontFamily: "MicroSoft YaHei",
                            fontSize: fontSize,
                            color: '#fff',
                            fontStyle: "normal",
                        },
                    }
                },
                emphasis: {
                    color: "#fd485e",
                    borderWidth: "1",
                    borderColor: "#fd485e",
                }
            };
        }

        for (var i = 0; i < children.length; i++) {
            transTree(children[i], group); //对子集内所有项循环
            var tamp = children[i]; //层级增加，将原自身写入children
            var Ratio = (!children[i].Percent || children[i].Percent == '0%') ? '' : children[i].Percent;
            if (Ratio) {
                if (Ratio < 0.01) {
                    Ratio = '<0.01%';
                } else {
                    Ratio = (parseFloat(children[i].Percent.substr(0, children[i].Percent.length - 1)).toFixed(4)) * 10000 / 10000 + '%'; //用以为线命名；很搞笑的格式化不知道谁写的
                }
            }
            children[i] = {
                name: Ratio,
                symbol: "arrowdown",
                symbolSize: [12, 12],
                tooltip: {
                    show: false
                },
                clickable: false,
                hoverable: false,
                itemStyle: {
                    normal: {
                        color: "#333",
                        borderWidth: 0,
                        label: {
                            show: !0,
                            position: "right",
                            textStyle: {
                                fontFamily: "MicroSoft YaHei",
                                fontSize: fontSize,
                                fontStyle: "normal",
                            },
                        }
                    },
                    emphasis: {
                        color: "#333",
                        borderWidth: 0,
                        label: {
                            show: !0,
                            position: "right",
                            textStyle: {
                                fontFamily: "MicroSoft YaHei",
                                fontSize: fontSize,
                                fontStyle: "normal",
                            },
                        }
                    }
                },
                lineStyle: {
                    width: 1,
                    color: "#333"
                },
                children: [tamp]
            }
        }
    }
    if (data.Org == 2) {
        //Org：0为法人，2为自然人
        //此为自然人特定样式
        data.symbol = 'rectangle';
        data.clickable = true;
        data.labelClick = false;
        data.symbolSize = [75, 35],
        data.itemStyle = {
            normal: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#d6000f",
                label: {
                    show: !0,
                    position: "inside",
                    textStyle: {
                        color: '#d6000f',
                        fontFamily: "MicroSoft YaHei",
                        fontSize: 14,
                        fontStyle: "normal",
                    },
                }
            },
            emphasis: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#d6000f",
            }
        };
    }
    //为存在投资关系死循环的公司加背景色
    if ( !(loopIds === undefined || loopIds.length == 0)&& loopIds.indexOf(data.KeyNo) != -1) {
        data.itemStyle = {
            normal: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#333",
                label: {
                    show: true,
                    position: "inside",
                    textStyle: {
                        fontFamily: "MicroSoft YaHei",
                        fontSize: fontSize,
                        color: '#333',
                        fontStyle: "normal",
                    },
                }
            },
            emphasis: {
                color: "#fff",
                borderWidth: "1",
                borderColor: "#fff",
            }
        };
    }
    treeId++;
}
//中心公司
function initTree(data) {
    //中心公司样式二次处理
    data.name = data.Name + "\n直属下级：" + data.Sontotal; //不需要百分比
    data.labelClick = false; //文字不可点击
    data.clickable = true; //块不可点击
    data.symbolSize = [data.Name.length * 16 + 40, 50]; //size
    data.symbol = 'rectangle';
    data.KeyNo = idPre; //中心公司id
    //data.treeId = 0;//中心树杈编0
    data.itemStyle = {
        normal: {
            color: "#d6000f",
            borderWidth: "1",
            borderColor: "#d6000f",
            label: {
                show: true,
                position: "inside",
                textStyle: {
                    color: '#ffffff',
                    fontFamily: "MicroSoft YaHei",
                    fontSize: 16,
                    fontStyle: "normal"
                },
            }
        },
        emphasis: {
            color: "#d6000f",
            borderWidth: "1",
            borderColor: "#d6000f"
        }
    };
    getNode(data);
    function getNode(data) {
        //标识出作为公司而非线的子集
        if (data.children) {
            for (var i = 0; i < data.children.length; i++) {
                getNode(data.children[i]); //再次深度遍历
            }
        }
        if (data.children && data.children.length > 0 && data.KeyNo && data.KeyNo != idPre) {
            //如果该对象不是中心公司且且不是连接线且有子集
            data._children = data.children; //children换个名称
            data.children = null;
            data.extend = 1; //含义为按钮处在待展开状态
        }
    }
}
//股权结构图
function drawGuquan(rootData) {
    myChart.clear(); //清空图表
    option = {
        title: {
            text: '',
        },
        series: [
            {
                type: "tree",
                orient: "vertical", //纵向分布
                nodePadding: 50, //节点间距
                layerPadding: 40, //层级间距
                symbol: "circle",
                roam: true, //滚轮缩放和拖动
                rootLocation: {
                    //根节点坐标
                    "x": "50%",
                    "y": "55%"
                },
                direction: "inverse", //反向
                data: [rootData] //上级数据
            }
        ]
    };
    if (touziData) {
        option.series.push({
            type: "tree",
            orient: "vertical",
            nodePadding: 50,
            layerPadding: 40,
            symbol: "circle",
            roam: true,
            rootLocation: {
                //根节点坐标
                "x": "50%",
                "y": "55%"
            },
            //direction:"inverse",//不设置则为正向
            data: [touziData] //下级数据
        })
    }
    myChart.setOption(option); //配置图表
    initZrender(); //配置展开按钮

    var flag = unfold ==="true" ? true : false;
    if (flag && focusKeyNo != ""){
        troggleTree(focusKeyNo,focusTreeId,2);//展开聚焦公司的子公司
    }

    animatieChart(myChart); //来自custom.js，缩放动画
    myChart.on('click', function(e) {
        //所有公司限时弹窗
        showKeyNo = e.data.KeyNo;
        var desState = e.data.desState;
        showDetail(showKeyNo,desState, 'company_guquan'); //来自custom.js，弹窗规则，可迁移

    //显示弹窗
    // if (e.data.KeyNo && e.data.KeyNo != idPre){
    //     if (e.data.KeyNo[0] == 'p') {
    //         //如果是p开头则跳转
    //         window.open('/pl_'+e.data.KeyNo+'.html');
    //     } else {
    //         //显示弹窗
    //         showKeyNo = e.data.KeyNo;
    //         showDetail(showKeyNo,'company_guquan');//来自custom.js，弹窗规则，可迁移
    //     }
    // }
    });
    myChart.getZrender().on('click', function(e) {
        //点击展开按钮事件
        //console.log(e);
        if (e.target && e.target.clickcom) {
            if (e.target.children || e.target._children) {
                //如果有这两个children中的一个；待展开有_chilrden展开后有chilrden
                troggleTree(e.target.keyNo, e.target.treeId, e.target.group); //展开或收起树杈
            }
        }
    });

    myChart.on('restore', function(param) {
        //刷新事件
        getNode(rootData); //又是深度遍历
        if (touziData) getNode(touziData);
        function getNode(data) {
            if (data.children) {
                for (var i = 0; i < data.children.length; i++) {
                    getNode(data.children[i]); //对子集遍历
                }
            }
            if (data.children && data.children.length > 0 && data.KeyNo && data.KeyNo != idPre) {
                //收起全部的树杈
                data._children = data.children;
                data.children = null;
                data.extend = 1;
            }
        }
        myChart.clear(); //清空图表 
        myChart.setOption(option); //配置图表
        initZrender(); //配置展开按钮
        animatieChart(myChart); //进入动画
    });
}
//设置所有点击展开的按钮
function initZrender() {
    //设置所有点击展开的按钮
    //180902：有关于Zrender的内容网络上缺乏文档，很多内容靠猜测
    var shapeList = myChart.getZrender().storage.getShapeList();
    //可能是所有已有形状列表，包括线和块
    for (var i = 0; i < shapeList.length; i++) {
        if (shapeList[i].clickcom) {
            myChart.getZrender().delShape(shapeList[i].id); //不理解作用
        }
    }
    for (var i = 0; i < shapeList.length; i++) {
        if (shapeList[i]._echartsData && shapeList[i]._echartsData._data.extend) {
            //对所有为非边且有子集且非中心公司的项执行
            //extend：1为未展开，2为已展开
            var iconImg = ''; //小加号或小减号的路径
            var isIE11 = (!!window.ActiveXObject || "ActiveXObject" in window);
            if (shapeList[i]._echartsData._data.extend == 1) {
                if (isIE11) {
                    iconImg = '/enterprise_atlas/img/tupu_cross.png';
                } else {
                    iconImg = '/enterprise_atlas/img/tupu_cross.svg';
                }
            } else if (shapeList[i]._echartsData._data.extend == 2) {
                if (isIE11) {
                    iconImg = '/enterprise_atlas/img/tupu_minus.png';
                } else {
                    iconImg = '/enterprise_atlas/img/tupu_minus.svg';
                }
            }

            if (focusKeyNo && shapeList[i]._echartsData._data.KeyNo == focusKeyNo){
                //获取聚焦公司偏移量
                focusTreeId = shapeList[i]._echartsData._data.treeId;//聚焦treeId再次赋值
                focusX = shapeList[i].rotation[1];
                focusY = shapeList[i].rotation[2];
            }

            var offset_x = -12; //左偏移
            var offset_y = 0; //上下偏移
            //上级数据和下级数据当不同赋值
            if (shapeList[i]._echartsData._data.group == 1)
                offset_y = -54;
            else if (shapeList[i]._echartsData._data.group == 2)
                offset_y = 30;
            var shape = new ImageShape({
                style: {
                    image: iconImg,
                    //rotation[1][2]分别指代横坐标和纵坐标，初始是块中心点，也就是线起始点
                    x: shapeList[i].rotation[1] + offset_x,
                    y: shapeList[i].rotation[2] + offset_y,
                    width: 24,
                    height: 24
                }
            });
            shape.keyNo = shapeList[i]._echartsData._data.KeyNo; //为按钮添加相关公司keyNo
            shape.treeId = shapeList[i]._echartsData._data.treeId; //树杈编号
            shape.children = shapeList[i]._echartsData._data.children; //出现的子集
            shape._children = shapeList[i]._echartsData._data._children; //未出现的子集
            shape.group = shapeList[i]._echartsData._data.group;
            shape.zlevel = 1;
            shape.z = 4;
            shape.clickcom = true;
            shape.hoverable = true;
            shape.clickable = true;
            if (shapeList[i]._echartsData._data.KeyNo != idPre) myChart.getZrender().addShape(shape);
        }
    }
//可在此处添加水印、衬字
}

function troggleTree(KeyNo, tid, group) {
    //if (KeyNo == '066922e9c1e094b57da351e05a334b3d') {
    //中心公司处不可收起
    //return;
    //}
    if (group == 1) getNode(rootData);
    else if (group == 2) getNode(touziData);
    function getNode(data) {
        //对数组深度遍历，遇到对应的记录则操作并返回
        if (data.KeyNo && data.KeyNo == KeyNo && data.treeId == tid) {

            if (data.children) {
                //展开的情况下变成收起
                data._children = data.children;
                data.children = null;
                data.extend = 1; //赋值为待展开
            //data.symbol = 'circleCross';
            } else if (data._children) {
                //收起的情况下展开
                data.children = data._children;
                data._children = null;
                data.extend = 2; //赋值为已展开
            //data.symbol = 'circleMinus';
            }
            return;
        }
        if (data.children) {
            for (var i = 0; i < data.children.length; i++) {
                getNode(data.children[i]); //对子集遍历
            }
        }
    }
    //如果一个系列数据全部收起在配置图表时会报错，所以先赋空集后赋空值
    if (rootData && rootData.children && rootData.children == null)
        rootData.children = [];
    if (touziData && touziData.children && touziData.children == null)
        touziData.children = [];
    myChart.clear(); //清空图表
    myChart.setOption(option); //配置图表
    initZrender(); //配置展开按钮
    if (rootData && rootData.children && rootData.children.length == 0) rootData.children == null;
    if (touziData && touziData.children && touziData.children.length == 0) touziData.children == null;
}
function resizeScreen() {
    //图表dom大小适应设置
    // if (document.documentElement.clientHeight > 700) {
    // } else {
    //     $('#screenArea').height(620);
    // }

    $(window.document).find(".firm_stockInfo_content").height( $(window.document).find(".firm_stockInfo_bg").height() );
    $('#screenArea').height($(window.document).find(".firm_stockInfo_bg").height());
}
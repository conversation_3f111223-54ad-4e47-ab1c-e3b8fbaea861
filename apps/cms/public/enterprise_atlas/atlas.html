﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>股权结构图谱</title>
    <link rel="stylesheet" href="css/bootstrap.css" type="text/css"/>
    <link rel="stylesheet" href="css/app.css" type="text/css"/>
    <link rel="stylesheet" href="css/companyDetail.css" type="text/css"/>
    <link rel="stylesheet" href="css/jquery.range.css">
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/jquery.slimscroll.min.js"></script>
    <script type="text/javascript" src="js/bootstrap.js"></script>
    <script src="js/config.js"></script>
    <script type="text/javascript" src="js/custom.js"></script>
    <script type="text/javascript" src="js/echarts2.js"></script>
    <script src="js/jquery.range.js"></script>
</head>
<body>
	<div id="screenArea">
		<div class="container">
			<div id="main"></div>
		</div>
        <!--<div class="filterTip">
            <span class="firm"></span>持股在<span class="min"></span>% - <span class="max"></span>%的股权关系如下
        </div>-->
		<div class="case">
			<input type="text" placeholder="请输入最小值" value="0" id="min">-<input type="text" placeholder="请输入最大值" id="max" value="100">
            <a href="javascript:;" onclick="maxVal()">搜索</a>
		</div>
		<div class="tupu-toolbar">
            <ul>
                <!-- <li onclick="filter()">
                    <span class="save">
                    </span>
                    筛选
                </li> -->
                <li onclick="maoScale(1)">
                    <span class="big">
                    </span>
                    放大
                </li>
                <li onclick="maoScale(2)">
                    <span class="small">
                    </span>
                    缩小
                </li>
                <li onclick="maoRefresh()">
                    <span class="refresh">
                    </span>
                    刷新
                </li>
                <li onclick="show_all(1)">
                    <span class="tupu_allUp">
                    </span>
                    全部上级
                </li>
                <li onclick="show_all(2)">
                    <span class="tupu_allDown">
                    </span>
                    全部下级
                </li>
                <li onclick="show_one(1)">
                    <span class="tupu_onlyOneUp">
                    </span>
                    只列上一级
                </li>
                <li onclick="show_one(2)">
                    <span class="tupu_onlyOneDown">
                    </span>
                    只列下一级
                </li>
                <li onclick="show_all(0)">
                    <span class="tupu_allFold">
                    </span>
                    全部展开
                </li>
                <!-- <li onclick="show_legend()" style="border-bottom:none">
                    <span class="tupu_legend">
                    </span>
                    提示信息
                </li> -->
            </ul>
		</div>
		<div id="company-detail" class="company-detail">
			<div class="m-t-sm" style="min-height: 28px;">
				<span class="mao-company-name" href="javascript:;"></span>
                <!--<span class="mao-company-status"></span>-->
                <span class="close"></span>
			</div>
            <div class="m-t-sm" style="clear: both;min-height: 28px;margin-top: 5px;">
                <div style="min-height: 28px;">
                    <div style="float: left;">
                        <span class="mao-light-grey">直属下级：</span>
                        <span class="mao-grey mao-sonCount">0家</span>
                    </div>
                    <div style="float: right;">
                        <span class="mao-light-grey" style="margin-left: 10PX;">全部下级：</span>
                        <span class="mao-grey mao-allIdList">0家</span>
                    </div>
                </div>
            </div>
            <div style="clear: both;"></div>
			<div class="m-t" style="margin-top: 0px;">
				<div style="clear: both;">
					<span class="mao-light-grey">法定代表人：</span>
                    <span class="mao-grey mao-oper"></span>
                    <!--<a class="btn mao-tupu-link" target="_blank"><span></span></a>-->
				</div>
				<div style="clear: both;">
					<span class="mao-light-grey">注册资本：</span><span class="mao-grey mao-ziben"></span>
				</div>
				<div>
					<span class="mao-light-grey">成立日期：</span><span class="mao-grey mao-date"></span>
				</div>
			</div>
			<!-- <div class="m-t">
				<a class="btn mao-tupu-link" target="_blank">查看图谱</a>
			</div> -->
			<div class="m-t mao-content m-b-lg">
				<ul class="nav nav-tabs mao-tabs">
                    <li class="active">
                        <a href="#deparment" aria-controls="deparment" role="tab" data-toggle="tab" style="padding: 8px;">内部股东</a>
                    </li>
					<!--<li>
                        <a href="#gudong" aria-controls="gudong" role="tab" data-toggle="tab" style="padding: 8px;">外部股东</a>
                    </li>-->
					<li>
                        <a href="#touzi" aria-controls="touzi" role="tab" data-toggle="tab" style="padding: 8px;">直属下级</a>
                    </li>
					<!--<li>
                        <a href="#member" aria-controls="member" role="tab" data-toggle="tab" style="padding: 8px 6px;margin-right: 0px;">高管信息333</a>
                    </li>-->
                    
				</ul>
				<div class="tab-content mao-tab-content">
                    <div role="tabpanel" class="deparment-list tab-pane fade in active" id="deparment"></div>
					<div role="tabpanel" class="gudong-list tab-pane fade" id="gudong"></div>
					<div role="tabpanel" class="touzi-list tab-pane fade" id="touzi"></div>
					<div role="tabpanel" class="member-list tab-pane fade" id="member"></div>
				</div>
			</div>
		</div>
        <div class="filterStock" id="filterStock">
            <div class="stock">股权比例（%）</div>
            <div class="demo">
            <input class="range-slider" type="hidden" value="0,100"/>
            <button id="filter">确定筛选</button>
          </div>
        </div>
        <div class="tooltipW">警告哦，没有上级了!</div>
        <div class="no-data" id="no-data">没有找到企业关系,请重新筛选！</div>
        <div class="legend-info" id="legend-info">
            <div class="legend-bar" >
                <div class="legend-color" style="background: #d6000f;"></div>
                <div style="padding: 0 10px;">本公司</div>
            </div>
            <div class="legend-bar">
                <div class="legend-color" style="background: #fd485e;"></div>
                <span style="padding: 0 10px;">图谱页点击的公司</span>
            </div>
        </div>
    <script src="js/range.js"></script>
    <script src="js/index.js"></script>
</body>
</html>
.company-detail{background:#707070;width:320px;min-height:450px;position:absolute;left:10px;top:5px;padding:8px 15px;-webkit-box-shadow:0 0 10px rgba(0,0,0,.5);-moz-box-shadow:0 0 10px rgba(0,0,0,.5);box-shadow:0 0 10px rgba(0,0,0,.5);display:none;border-radius:5px}.mao-content{background:#444}.mao-divider{display:inline-block;height:14px;border:1px solid #fe0000;position:relative;top:2px}.close{width:24px;height:24px;background-size:100% 100%;background:url(../img/close.png) no-repeat;float:right}.mao-company-name{font-size:16px;color:#fff;display:inline-block;float:left;width:200px;}.mao-company-name:hover{font-size:16px}.mao-oper a{color:#d6000f}.mao-oper a:hover{color:#007add}.mao-company-status{float:left;display:inline;padding:5px 8px;margin-top:3px;font-size:12px;font-weight:bold;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:4px;background:#1cc30f}.mao-light-grey{color:#ccc}.mao-grey{color:#fff}.mao-tupu-link{width:60px;height:25px;text-align:center;display:block;float:right;padding:0;line-height:25px;border-radius:16px;background:#555;color:#fff}.mao-tupu-link span{width:18px;height:18px;margin-top:3px;display:inline-block;background:url(../img/tupu_onlyOneDown.png) no-repeat;background-size:100% 100%}.mao-tupu-link:hover,.mao-tupu-link:focus,.mao-tupu-link:active{color:#fff}.mao-tabs{background:#707070;border:0}.table-bordered{border-top:1px solid #444;border-left:1px solid #444;border-right:1px solid #444}.mao-tab-content{margin-top:0;height:300px;font-size:12px}.mao-list{margin-top:5px;margin-bottom:0}.mao-list li{border-bottom:1px solid #ddd;position:relative;display:block;padding:5px 8px}.mao-list .badge{float:right;font-weight:normal;background-color:#fff;margin-left:5px}.mao-table{font-size:12px;overflow-y:scroll;touch-action: pan-y;-webkit-overflow-scrolling:touch;}.mao-table thead{background:#444;color:#999}.mao-table>thead>tr>th{border-bottom-width:1px}.mao-table tbody{color:#666;overflow-y:scroll;touch-action: pan-y;-webkit-overflow-scrolling:touch;}.mao-table-pane{height:300px;overflow:auto;-webkit-overflow-scrolling:touch}.mao-noresult{text-align:center;padding-top:60px}.mao-noresult p{text-align:center}.mao-noresult p img{width:80px}.mao-table a{color:#333}.mao-table a:hover{color:#d6000f}.mao-toolbar{position:absolute;width:50px;right:30px;bottom:50px;font-size:18px;color:#5e89d7;z-index:20;background:#fff}.mao-toolbar ul{width:50px;margin:0;padding:0;box-sizing:border-box;border:1px solid #5e89d7;-webkit-box-shadow:0 0 10px rgba(94,137,215,.5);-moz-box-shadow:0 0 10px rgba(94,137,215,.5);box-shadow:0 0 10px rgba(94,137,215,.5)}.mao-toolbar ul:nth-child(1){margin-bottom:8px}.mao-toolbar ul>li{width:50px;height:50px;text-align:center;line-height:50px;box-sizing:border-box}.mao-toolbar ul>li:not(:first-child){border-top:1px solid #5e89d7}.mao-head{position:absolute;top:60px;width:100%;z-index:10}.mao-head .mao-title{float:left;font-size:14px;color:#333;margin-left:-10px;margin-top:10px}.mao-head .mao-nav{float:left;font-size:16px;color:#666;margin-left:10px;margin-top:15px}.mao-nav li{margin-left:20px;display:inline}.mao-nav a{line-height:1.8;padding:3px 0}.mao-nav a:hover{color:#d6000f}.mao-nav .active>a{color:#d6000f;border-bottom:2px solid #d6000f}.mao-main{width:100%;height:100%}.mao-main svg{width:100%;height:100%}.mao-loading{width:100%;height:100%;position:fixed;z-index:20;display:none}.mao-loading img{width:120px;position:absolute;left:50%;top:50%;margin-left:-60px;margin-top:-110px}.mao-nodata{text-align:center;margin-top:200px}.mao-nodata img{width:100px}.mao-screen-area{width:100%;height:100%;background:#fff;position:relative}:-webkit-full-screen.mao-screen-area{width:100%;height:100%!important;background:#fff}:-moz-full-screen.mao-screen-area{width:100%;height:100%!important;background:#fff}:-ms-full-screen.mao-screen-area{width:100%;height:100%!important;background:#fff}:-o-full-screen.mao-screen-area{width:100%;height:100%!important;background:#fff}:full-screen.mao-screen-area{width:100%;height:100%!important;background:#fff}#screenArea{width:100%;height:500px}.container{width:100%;height:100%;padding-left:0;padding-right:0;position:relative}#main{width:100%;height:100%}.filterTip{position:absolute;top:20px;width:500px;text-align:center;left:50%;margin-left:-250px;height:30px;line-height:30px;display:none}#screenArea .case{position:relative;top:-100px;width:40px;height:40px;display:none}
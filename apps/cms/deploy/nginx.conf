user  root;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    client_max_body_size   512m;
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip  on;

    server {
		listen		80;

		location ^~ /api/ {
		   rewrite ^/(.*) /$1 break;
		   proxy_pass http://gateway:9003/;
		   proxy_redirect off;
		   proxy_set_header X-Real-IP $remote_addr;
		   proxy_set_header Host $host;
		   proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		   proxy_read_timeout 300;
		   proxy_send_timeout 300;
		   add_header Content-Security-Policy "default-src 'self'; img-src 'self' data:; script-src 'self' ;" always;
		}

		location / {
		  root  /usr/share/nginx/html;
		  index index.html index.htm;

		  if ( $uri = '/index.html' ) { # disabled index.html cache
			add_header Cache-Control "no-cache, no-store, must-revalidate";
		  }

		  try_files $uri $uri/ /index.html;
		}

	}
}

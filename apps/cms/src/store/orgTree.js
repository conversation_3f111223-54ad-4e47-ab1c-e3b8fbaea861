import { defineStore } from 'pinia'

export const useOrgTreeStore = defineStore('orgTree', {
	state: () => ({
		orgTree: [],
		selectTreeOrg: [],
		selectTreeOrgFilling: []
	}),
	actions: {
		setOrgTree(data) {
			this.orgTree = data
		},
		clearOrgTree() {
			this.orgTree = []
		},
		setSelectTreeOrg(data) {
			this.selectTreeOrg = data
		},
		clearSelectTreeOrg() {
			this.orgTree = []
		},
		setSelectTreeOrgFilling(data) {
			this.selectTreeOrgFilling = data
		},
		clearSelectTreeOrgFilling() {
			this.orgTree = []
		},
		clearAll() {
			this.orgTree = []
			this.selectTreeOrg = []
			this.selectTreeOrgFilling = []
		}
	}
})

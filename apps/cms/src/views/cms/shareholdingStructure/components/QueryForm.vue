<template>
	<a-form :model="formData" layout="horizontal">
		<a-row :gutter="24">
			<a-col :span="6">
				<a-date-picker
					@change="handleDateChange"
					v-model:value="formData.yearMonth"
					value-format="YYYY-M"
					picker="month"
					class="w-full"
					:disabled-date="disabledDate"
				/>
			</a-col>
			<a-col :span="3">
				<a-button
					type="primary"
					@click="handleQueryCurrentVersion"
					v-if="currentVersionLabel"
				>
					{{ currentVersionLabel }}
				</a-button>
			</a-col>
			<a-col :span="9">
				<a-form-item label="机构" name="entityId">
					<a-tree-select
						popupClassName="z-999"
						v-model:value="formData.entityId"
						v-model:searchValue="formData.searchValue"
						show-search
						style="width: 100%"
						:dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
						placeholder="请选择机构"
						allow-clear
						:loading="treeProps.loading"
						:treeDefaultExpandedKeys="treeProps.expanded"
						:tree-data="orgTreeData"
						tree-node-filter-prop="simpOrgName"
						@select="handleChange"
						:field-names="{ children: 'children', label: 'simpOrgName', value: 'id' }"
					>
						<template #title="{ simpOrgName }">
							<template
								v-for="(fragment, i) in simpOrgName
									.toString()
									.split(new RegExp(`(?<=${formData.searchValue})|(?=${formData.searchValue})`, 'i'))"
								:key="i"
							>
								<span
										v-if="containsIgnoreCase(fragment,formData.searchValue) && formData.searchValue.length > 0 "
										style="color: #f9906b"
									>
									{{ fragment }}
								</span>

								<template v-else>{{ fragment }}</template>
							</template>
						</template>
					</a-tree-select>
				</a-form-item>
			</a-col>
			<a-col :span="6">
				<a-form-item
					v-if="shouldShowCheckbox"
					label=""
					name="type"
					@change="handleChange"
				>
					<a-checkbox v-model:checked="formData.type">是否显示全部内部股东</a-checkbox>
				</a-form-item>
			</a-col>
		</a-row>
	</a-form>
</template>

<script setup>
	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		currentVersionLabel: {
			type: String,
			default: ''
		},
		orgTreeData: {
			type: Array,
			default: () => []
		},
		treeProps: {
			type: Object,
			required: true
		},
		disabledDate: {
			type: Function,
			required: true
		}
	})

	const emit = defineEmits([
		'update:formData',
		'dateChange',
		'queryCurrentVersion',
		'change'
	])

	function containsIgnoreCase(a, b) {
		const regex = new RegExp(`(?=.*${b})`, 'i');
		return regex.test(a);
	}

	const shouldShowCheckbox = computed(() => {
		return props.formData.entityId && props.formData.entityId !== '20140824164901061871'
	})

	const handleDateChange = (date, yearMonth) => {
		emit('dateChange', date, yearMonth)
	}

	const handleQueryCurrentVersion = () => {
		emit('queryCurrentVersion')
	}

	const handleChange = (value) => {
		emit('change', value)
	}
</script>

<template>
	<div
		class="node-tooltip-panel"
		:style="{
			left: position.x + 'px',
			top: position.y + 'px',
			zIndex: 1000,
			padding: '10px',
			backgroundColor: '#ffffff',
			border: '#eeeeee solid 1px',
			boxShadow: '0px 0px 8px #cccccc',
			position: 'fixed',
			maxWidth: '500px',
			maxHeight: '600px',
			overflow: 'auto'
		}"
	>
		<a-descriptions
			bordered
			:title="selectedNode.text"
			size="small"
			layout="vertical"
		>
			<template #extra>
				<CloseOutlined @click="handleClose" />
			</template>
			<a-descriptions-item label="所属管理主体">
				{{ selectedNode.manageCompany }}
			</a-descriptions-item>
			<a-descriptions-item label="是否上市">
				{{ formatDictData('WHETHER', selectedNode.isListed) }}
			</a-descriptions-item>
			<a-descriptions-item label="注册资本（万元）">
				{{ selectedNode.registeredCapital }}
			</a-descriptions-item>
			<a-descriptions-item label="管理层级">
				{{ selectedNode.managementLevel }}
			</a-descriptions-item>
			<a-descriptions-item label="法定代表人">
				{{ selectedNode.legalPersonName }}
			</a-descriptions-item>
			<a-descriptions-item label="法人层级">
				{{ selectedNode.legalEntityLevel }}
			</a-descriptions-item>
			<a-descriptions-item label="机构类型" :span="3">
				{{ formatDictData('filling_standards', selectedNode.fillingStandards) }}
			</a-descriptions-item>
			<a-descriptions-item label="上市信息" :span="3" v-if="selectedNode.isListed === 'Y'">
				<a-table
					:columns="listedColumns"
					:data-source="selectedNode.listedVoList"
					:pagination="false"
					size="middle"
				/>
			</a-descriptions-item>
			<a-descriptions-item label="内部股东" :span="3">
				<template v-for="item in sortedInvestmentList" :key="item.id">
					股东名称: {{ item.investmentName }}
					<br />
					持股比例: {{ (item.shareRate * 100).toFixed(2) }}%
					<br />
				</template>
			</a-descriptions-item>
		</a-descriptions>
	</div>
</template>

<script setup>
	import { computed } from 'vue'
	import { CloseOutlined } from '@ant-design/icons-vue'
	import { sortBy } from 'lodash-es'
	import tool from '@/utils/tool'

	const props = defineProps({
		position: {
			type: Object,
			required: true,
			validator: (value) => {
				return typeof value.x === 'number' && typeof value.y === 'number'
			}
		},
		selectedNode: {
			type: Object,
			required: true
		},
		listedColumns: {
			type: Array,
			required: true
		}
	})

	const emit = defineEmits(['close'])

	const sortedInvestmentList = computed(() => {
		return sortBy(props.selectedNode.investmentVoList || [])
	})

	const formatDictData = (type, value) => {
		return tool.dictTypeData(type, value)
	}

	const handleClose = () => {
		emit('close')
	}
</script>

<style scoped>
.node-tooltip-panel {
	border-radius: 4px;
	min-width: 300px;
}

.node-tooltip-panel :deep(.ant-descriptions-title) {
	font-weight: bold;
	margin-bottom: 8px;
}

.node-tooltip-panel :deep(.ant-descriptions-item-label) {
	font-weight: 500;
}
</style>

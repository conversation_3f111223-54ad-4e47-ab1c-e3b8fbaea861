<template>
	<div
		class="context-menu"
		:style="{ top: `${position.y}px`, left: `${position.x}px` }"
	>
		<ul>
			<li
				v-if="selectedNode?.data?.isClickable"
				class="text-red-600"
				@click="handleExecute('DETAILS')"
			>
				详情
			</li>
			<li
				v-if="selectedNode?.data?.isClickable"
				class="text-red-600"
				@click="handleExecute('FOCUS')"
			>
				聚焦
			</li>
			<li
				v-if="selectedNode?.data?.isClickable"
				class="text-red-600"
				@click="handleExecute('PORTRAIT')"
			>
				画像
			</li>
			<li v-if="!rootNodeIds.includes(selectedNode?.data?.entityId)" @click="handleExecute('UNEXPAND_ALL')">全部收起</li>
			<li v-if="!rootNodeIds.includes(selectedNode?.data?.entityId)" @click="handleExecute('EXPAND_ALL')">全部展开</li>
		</ul>
	</div>
</template>

<script setup>
	const props = defineProps({
		position: {
			type: Object,
			required: true,
			validator: (value) => {
				return typeof value.x === 'number' && typeof value.y === 'number'
			}
		},
		selectedNode: {
			type: Object,
			default: () => ({})
		},
		rootNodeIds: {
			type: Array,
			default: () => []
		}
	})

	const emit = defineEmits(['execute'])

	const handleExecute = (command) => {
		emit('execute', command)
	}
</script>

<style scoped>
	.context-menu {
		position: fixed;
		background-color: white;
		border: 1px solid #ccc;
		box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
		z-index: 1000;
		padding: 10px;
		border-radius: 4px;
	}

	.context-menu ul {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.context-menu li {
		padding: 8px 12px;
		cursor: pointer;
	}

	.context-menu li:hover {
		background-color: #f0f0f0;
	}
</style>

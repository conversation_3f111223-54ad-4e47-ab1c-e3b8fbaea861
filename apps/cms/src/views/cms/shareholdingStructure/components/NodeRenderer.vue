<template>
	<div class="my-industy-node cursor-pointer relative">
		<a-tooltip>
			<template #title>{{ node.text }}</template>
			<div class="my-card-body line-clamp-1">{{ node.text }}</div>
		</a-tooltip>
	</div>
	<div
		class="absolute -left-[1rem] top-[1rem]"
		v-if="shouldShowEyeIcon"
	>
		<EyeFilled
			:style="{ color:'#333'}"
			@click="handleShowRootRelation"
		/>
	</div>
</template>

<script setup>
	import { EyeFilled } from '@ant-design/icons-vue'

	const props = defineProps({
		node: {
			type: Object,
			required: true
		},
		isTop: {
			type: Boolean,
			required: true
		},
		rootNodeIds: {
			type: Array,
			required: true
		},
		formModel: {
			type: Object,
			required: true
		},
		loading: {
			type: Boolean,
			required: true
		}
	})

	const emit = defineEmits(['showRootRelation'])

	const shouldShowEyeIcon = computed(() => {
    const [group, stock, limit] = props.node.id === props.rootNodeIds[0] ? props.node.lot.parent.targetNodes: []
		return props.isTop &&
			   props.node.id === props.rootNodeIds[0] &&
			   props.formModel.entityId === props.rootNodeIds[0]  && !limit?.expanded &&
			   !props.loading
	})

	const handleShowRootRelation = (event) => {
		emit('showRootRelation', event)
	}
</script>

<style scoped>
	.my-industy-node {
		width: 200px;
		border-radius: 5px;
		background-color: #ffffff;
		overflow: clip;
	}

	.my-card-body {
		line-height: 30px;
	}
</style>

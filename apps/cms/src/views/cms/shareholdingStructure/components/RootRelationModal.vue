<template>
	<xn-form-container
		:width="700"
		:visible="visible"
		:destroy-on-close="true"
		@close="handleClose"
		:footer="null"
	>
		<a-spin :spinning="loading">
			<div style="height: 500px; width: 650px">
				<RelationGraph ref="graphInstanceRef" :options="graphOptions">
					<template #node="{ node }">
						<div class="my-industy-node cursor-pointer relative">
							<a-tooltip>
								<template #title>{{ node.text }}</template>
								<div class="my-card-body line-clamp-1">{{ node.text }}</div>
							</a-tooltip>
						</div>
					</template>
				</RelationGraph>
			</div>
		</a-spin>
	</xn-form-container>
</template>

<script setup>
	import RelationGraph from 'relation-graph-vue3'

	const props = defineProps({
		visible: {
			type: Boolean,
			required: true
		},
		loading: {
			type: Boolean,
			default: false
		},
		graphOptions: {
			type: Object,
			required: true
		}
	})

	const emit = defineEmits(['update:visible'])

	const graphInstanceRef = ref()

	const handleClose = () => {
		emit('update:visible', false)
	}

	// 暴露图表实例给父组件
	defineExpose({
		getInstance: () => graphInstanceRef.value?.getInstance()
	})
</script>
<style lang="scss" scoped>
	.my-industy-node {
		width: 200px;
		border-radius: 5px;
		background-color: #ffffff;
		overflow: clip;
	}

	.my-card-body {
		line-height: 30px;
	}
	/* 全局样式，用于图表节点 */
	:deep(.c-btn-open-close) {
		& span {
			background-color: #D20000 !important;
		}
	}

	:deep(.my-industy-node-level-group-0) {
		.my-industy-node {
			.my-card-header {
				background-color: #fff;
				color: #D20000;
			}
			.my-card-body {
				color: #fff;
				background: #D20000;
			}
		}
	}

	:deep(.my-industy-node-level-stock-0) {
		.my-industy-node {
			.my-card-header {
				background-color: #fff;
				color: #D45100;
			}
			.my-card-body {
				color: #fff;
				background: #D45100;
			}
		}
	}

	:deep(.my-industy-node-level-limit-0) {
		.my-industy-node {
			.my-card-header {
				background-color: #fff;
				color: #485563;
			}
			.my-card-body {
				color: #fff;
				background: #485563;
			}
		}
	}

	//:deep(.my-industy-node-level-1.Y) {
	//	.my-industy-node {
	//		border: #ddedf8 solid 1px;
	//		color: #333333;
	//	}
	//}
	//
	//:deep(.my-industy-node-level-1.N) {
	//	.my-industy-node {
	//		border: #333 solid 1px;
	//		color: #333;
	//		.my-card-body {
	//			color: #fff;
	//			background: #333;
	//		}
	//	}
	//}

	:deep(.my-industy-node-level-1.Y) {
		.my-industy-node {
			border: #ddedf8 solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #ddedf8;
			}
		}
	}

	:deep(.my-industy-node-level-1.N) {
		.my-industy-node {
			border: #7f7f7f solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #333;
			}
			.my-card-body {
				color: #fff;
				background: #7f7f7f;
			}
		}
	}

	:deep(.my-industy-node-level-2.Y) {
		.my-industy-node {
			border: #e1f0da solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #e1f0da;
			}
		}
	}

	:deep(.my-industy-node-level-2.N) {
		.my-industy-node {
			border: #a6a6a6 solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #333;
			}
			.my-card-body {
				color: #fff;
				background: #a6a6a6;
			}
		}
	}

	:deep(.my-industy-node-level-3.Y) {
		.my-industy-node {
			border: #ffeadf solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #ffeadf;
			}
		}
	}

	:deep(.my-industy-node-level-3.N) {
		.my-industy-node {
			border: #bfbfbf solid 1px;
			.my-card-header {
				color: #333;
				background-color: #fff;
			}
			.my-card-body {
				color: #fff;
				background: #bfbfbf;
			}
		}
	}

	//:deep(.my-industy-node-level-5.Y) {
	//	.my-industy-node {
	//		border: #fff7dc solid 1px;
	//		color: #333333;
	//		.my-card-header {
	//			background: #fff;
	//		}
	//		.my-card-body {
	//			background: #fff7dc;
	//		}
	//	}
	//}
	//
	//:deep(.my-industy-node-level-5.N) {
	//	.my-industy-node {
	//		border: #d9d9d9 solid 1px;
	//		color: #999999;
	//		.my-card-header {
	//			background-color: #fff;
	//			color: #333;
	//		}
	//		.my-card-body {
	//			color: #fff;
	//			background: #d9d9d9;
	//		}
	//	}
	//}

	:deep(.my-industy-node-level-fixed-0) {
		.my-industy-node {
			border: #7f7f7f solid 1px;
			.my-card-body {
				color: #fff;
				background: #7f7f7f;
			}
		}
	}
</style>

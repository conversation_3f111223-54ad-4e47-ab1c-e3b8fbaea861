import { computed, nextTick, ref } from 'vue'
import { cloneDeep, debounce, isNil } from 'lodash-es'
import portraitApi from '@/api/biz/portraitApi'
import internalinvestmentApi from '@/api/biz/bizEntityinternalinvestmentApi'
import { useDeviceOptimization } from './useDeviceOptimization'
import { usePerformanceMonitor } from './usePerformanceMonitor'
import { useErrorHandler } from './useErrorHandler'
import LZString from 'lz-string'

// 常量定义
const DISCORD_NODES = [
	{ entityId: '20140829153600000689' },
	{ entityId: '20140829153600000687' },
	{ entityId: '20220119154215029860' }
]

const ROOT_NODE_LEVEL_MAP = {
	"20140824164901061871": 'group-0',
	"20140822152314001513": 'stock-0',
	"20140824164901061873": 'limit-0'
}

export function useGraphData() {
	// 使用优化组合函数
	const { optimizationConfig, getGraphConfig, monitorMemoryUsage, deviceInfo } = useDeviceOptimization()
	const { measureTime } = usePerformanceMonitor()
	const { handleDataError, handleGraphError } = useErrorHandler()

	// 响应式数据
	const loading = ref(false)
	const rootLoading = ref(false)
	const originData = ref({})
	const rootPercent = ref(0)
	const isTransitioning = ref(false)
	const loadingText = ref('加载中...')
	const loadingProgress = ref(0)

	// 图表实例引用
	const graphInstanceRef = ref()
	const graphInstanceRootRef = ref()

	// 常量
	const rootNodeIds = Object.keys(ROOT_NODE_LEVEL_MAP)
	let rootData = null

	// 缓存机制 - 使用sessionStorage优化性能
	const CACHE_PREFIX = 'shareholding_graph_'
	const CACHE_INDEX_KEY = 'shareholding_cache_index'
	const cacheExpireTime = 5 * 60 * 1000 // 5分钟过期
	const maxCacheSize = computed(() => optimizationConfig.value.cacheSize || 20) // 减少缓存数量，避免sessionStorage溢出

	// 图表配置 - 动态根据设备性能调整
	const graphOptions = computed(() => {
		return {
			reLayoutWhenExpandedOrCollapsed: true,
			moveToCenterWhenRefresh: true,
			disableDragNode: false,
			disableZoom: true,
			allowShowDownloadButton: false,
			allowShowFullscreenMenu: false,
			defaultFocusRootNode: false,
			defaultExpandHolderPosition: 'right',
			defaultNodeShape: 1,
			defaultNodeBorderWidth: 0,
			defaultLineShape: 6,
			defaultLineColor: '#333',
			defaultJunctionPoint: 'lr',
			// 禁用默认节点样式，避免蓝色样式覆盖
			defaultNodeColor: 'transparent',
			defaultNodeBorderColor: 'transparent',
			defaultNodeFontColor: 'inherit',
			...getGraphConfig.value
		}
	})

	const graphRootOptions = {
		reLayoutWhenExpandedOrCollapsed: true,
		moveToCenterWhenRefresh: true,
		performanceMode: true,
		disableZoom: true,
		zoomToFitWhenRefresh: false,
		disableDragNode: true,
		disableLineClickEffect: true,
		allowShowMiniToolBar: false,
		allowShowDownloadButton: false,
		allowShowFullscreenMenu: false,
		defaultFocusRootNode: false,
		defaultNodeShape: 1,
		defaultNodeBorderWidth: 0,
		defaultLineShape: 4,
		defaultLineColor: '#333',
		defaultJunctionPoint: 'tb',
		defaultNodeColor: "rgba(53, 56, 59, 0)",
		defaultNodeFontColor: "rgba(0, 0, 0, 1)",
		layout: {
			layoutName: 'tree',
			from: 'top',
			min_per_width: 230,
			levelDistance: '200,200,200'
		}
	}

	// 计算属性
	const isTop = computed(() => (formModel) => {
		return !formModel.entityId || formModel?.entityId === rootNodeIds[0]
	})

	// 缓存相关函数 - 使用sessionStorage优化性能
	const getCacheKey = (params) => {
		// 优化：只序列化关键字段，减少存储空间
		const keyParams = {
			entityId: params.entityId || '',
			year: params.year || '',
			month: params.month || '',
			day: params.day || '',
			type: params.type || false,
			isQueryHighestShare: params.isQueryHighestShare || ''
		}

		// 生成日期字符串，确保一致性
		const dateStr = `${keyParams.year}-${keyParams.month}-${keyParams.day}`
		const finalKeyParams = {
			...keyParams,
			dateKey: dateStr // 使用统一的日期键
		}

		const keyString = JSON.stringify(finalKeyParams)
		console.log('生成缓存键参数:', finalKeyParams)

		return CACHE_PREFIX + btoa(keyString) // 使用base64编码减少key长度
	}

	// 获取缓存索引
	const getCacheIndex = () => {
		try {
			const index = sessionStorage.getItem(CACHE_INDEX_KEY)
			return index ? JSON.parse(index) : []
		} catch (error) {
			console.warn('获取缓存索引失败:', error)
			return []
		}
	}

	// 更新缓存索引
	const updateCacheIndex = (key, action = 'add') => {
		try {
			let index = getCacheIndex()

			if (action === 'add') {
				// 添加新的缓存key，保持时间顺序
				index = index.filter(item => item.key !== key) // 移除重复项
				index.push({ key, timestamp: Date.now() })

				// 限制缓存数量
				if (index.length > maxCacheSize.value) {
					// 删除最旧的缓存项
					const oldestItem = index.shift()
					sessionStorage.removeItem(oldestItem.key)
				}
			} else if (action === 'remove') {
				index = index.filter(item => item.key !== key)
			}

			sessionStorage.setItem(CACHE_INDEX_KEY, JSON.stringify(index))
		} catch (error) {
			console.warn('更新缓存索引失败:', error)
		}
	}

	const getFromCache = (key) => {
		try {
			const cached = sessionStorage.getItem(key)
			if (!cached) return null

			const parsedCache = JSON.parse(cached)

			// 检查是否过期
			if (Date.now() - parsedCache.timestamp < cacheExpireTime) {
				// 检查是否为压缩数据
				if (parsedCache.compressed) {
					try {
						const decompressedStr = LZString.decompress(parsedCache.data)
						if (decompressedStr) {
							return JSON.parse(decompressedStr)
						} else {
							// 清理损坏的缓存
							sessionStorage.removeItem(key)
							updateCacheIndex(key, 'remove')
							return null
						}
					} catch (decompressError) {
						console.error('解压缩失败:', decompressError)
						sessionStorage.removeItem(key)
						updateCacheIndex(key, 'remove')
						return null
					}
				} else {
					return parsedCache.data
				}
			} else {
				// 过期则删除
				sessionStorage.removeItem(key)
				updateCacheIndex(key, 'remove')
				return null
			}
		} catch (error) {
			console.warn('缓存读取失败:', error)
			sessionStorage.removeItem(key)
			updateCacheIndex(key, 'remove')
			return null
		}
	}

	const setCache = (key, data) => {
		try {
			// 序列化数据
			const dataStr = JSON.stringify(data)
			const originalSize = new Blob([dataStr]).size

			// 使用LZString压缩数据
			const compressedData = LZString.compress(dataStr)
			const compressedSize = new Blob([compressedData]).size

			// 检查压缩后大小
			if (compressedSize > 5 * 1024 * 1024) {
				console.warn('数据过大，跳过缓存:', (compressedSize / 1024 / 1024).toFixed(2), 'MB')
				return
			}

			const cacheData = {
				compressed: true,
				data: compressedData,
				timestamp: Date.now(),
				originalSize: originalSize,
				compressedSize: compressedSize
			}

			sessionStorage.setItem(key, JSON.stringify(cacheData))
			updateCacheIndex(key, 'add')
		} catch (error) {
			console.error('缓存失败:', error)

			// 如果是存储空间不足，清理旧缓存后重试
			if (error.name === 'QuotaExceededError') {
				clearOldestCache()
				try {
					const simpleData = {
						compressed: false,
						data: data,
						timestamp: Date.now()
					}
					sessionStorage.setItem(key, JSON.stringify(simpleData))
					updateCacheIndex(key, 'add')
				} catch (retryError) {
					console.error('重试缓存失败:', retryError)
				}
			}
		}
	}

	// 清理最旧的缓存项
	const clearOldestCache = () => {
		const index = getCacheIndex()
		if (index.length > 0) {
			// 按时间排序，删除最旧的几个
			index.sort((a, b) => a.timestamp - b.timestamp)
			const toDelete = index.slice(0, Math.ceil(index.length * 0.3)) // 删除30%的旧缓存

			toDelete.forEach(item => {
				sessionStorage.removeItem(item.key)
			})

			const remainingIndex = index.slice(Math.ceil(index.length * 0.3))
			sessionStorage.setItem(CACHE_INDEX_KEY, JSON.stringify(remainingIndex))
		}
	}

	const clearCache = () => {
		try {
			const index = getCacheIndex()
			index.forEach(item => {
				sessionStorage.removeItem(item.key)
			})
			sessionStorage.removeItem(CACHE_INDEX_KEY)
			console.log('已清理所有缓存')
		} catch (error) {
			console.warn('清理缓存失败:', error)
		}
	}

	// 清理过期缓存
	const clearExpiredCache = () => {
		try {
			const index = getCacheIndex()
			const now = Date.now()
			const validIndex = []

			index.forEach(item => {
				try {
					const cached = sessionStorage.getItem(item.key)
					if (cached) {
						const parsedCache = JSON.parse(cached)
						if (now - parsedCache.timestamp < cacheExpireTime) {
							validIndex.push(item)
						} else {
							sessionStorage.removeItem(item.key)
						}
					}
				} catch (error) {
					// 清理损坏的缓存项
					sessionStorage.removeItem(item.key)
				}
			})

			sessionStorage.setItem(CACHE_INDEX_KEY, JSON.stringify(validIndex))
			console.log('已清理过期缓存，剩余:', validIndex.length, '项')
		} catch (error) {
			console.warn('清理过期缓存失败:', error)
		}
	}

	// 定期清理过期缓存
	let cacheCleanupTimer = null
	const startCacheCleanup = () => {
		// 每10分钟清理一次过期缓存
		cacheCleanupTimer = setInterval(() => {
			clearExpiredCache()
		}, 10 * 60 * 1000)
	}

	const stopCacheCleanup = () => {
		if (cacheCleanupTimer) {
			clearInterval(cacheCleanupTimer)
			cacheCleanupTimer = null
		}
	}

	// 启动缓存清理
	startCacheCleanup()

	// 日期变化检测
	let lastQueryDate = null
	const detectDateChange = (params) => {
		const currentDate = `${params.year || ''}-${params.month || ''}-${params.day || ''}`
		const hasDateChanged = lastQueryDate !== null && lastQueryDate !== currentDate

		if (hasDateChanged) {
			console.log('📅 检测到日期变化:', {
				previous: lastQueryDate,
				current: currentDate
			})
		}

		lastQueryDate = currentDate
		return hasDateChanged
	}

	// Loading 状态管理
	const setLoadingState = (isLoading, text = '加载中...', progress = 0) => {
		loading.value = isLoading
		loadingText.value = text
		loadingProgress.value = progress

		if (isLoading) {
			isTransitioning.value = true
		}
	}

	const updateLoadingProgress = (progress, text) => {
		loadingProgress.value = progress
		if (text) {
			loadingText.value = text
		}
	}

	const finishLoading = () => {
		loadingProgress.value = 100

		setTimeout(() => {
			loading.value = false
			setTimeout(() => {
				isTransitioning.value = false
			}, 300)
		}, 200)
	}

	// 工具函数
	const findTopEntityId = (node) => {
		let current = node
		while (current.detailList && current.detailList.length > 0) {
			current = current.detailList[0]
		}
		return current.entityId
	}

	// 显示管理图表 - 优化版本
	const showManageGraph = measureTime(async (data, formModel) => {
		try {
			formModel.versionId = data.versionId
			let __graph_json_data = null

			const isTopLevel = !formModel.entityId || formModel?.entityId === rootNodeIds[0]

			updateLoadingProgress(60, '正在构建架构树...')

			if (isTopLevel) {
				__graph_json_data = {
					rootId: 'root',
					nodes: [
						{
							id: 'root',
							text: 'root',
							opacity: 0
						},
						{
							id: rootNodeIds[1], // '20140822152314001513'
							text: '中国中信股份有限公司',
							expandHolderPosition: 'right',
							expanded: false,
							data: {
								entityId: rootNodeIds[1],
								fillingStandards: 'stock-0',
								hasBusiness: 'Y',
								isClickable: true,
								text: '100%',
								isNeedLoadDataFromRemoteServer: true
							}
						},
						{
							id: rootNodeIds[2], // '20140824164901061873'
							text: '中国中信有限公司',
							expandHolderPosition: 'right',
							expanded: false,
							data: {
								entityId: rootNodeIds[2],
								fillingStandards: 'limit-0',
								hasBusiness: 'Y',
								isClickable: true,
								text: '100%',
								isNeedLoadDataFromRemoteServer: true
							}
						},
					],
					lines: [
						{
							from: 'root',
							to: rootNodeIds[0],
							isHide: true
						},
						{
							from: 'root',
							to: rootNodeIds[1],
							isHide: true
						},
						{
							from: 'root',
							to: rootNodeIds[2],
							isHide: true
						},
					]
				}
			} else {
				let topId = ''
				if (data.upperList && data.upperList.length > 0) {
					topId = findTopEntityId(data.upperList[0])
				} else {
					topId = data.entityId
				}
				__graph_json_data = {
					rootId: topId,
					nodes: [],
					lines: []
				}
			}

			// 添加当前节点
			__graph_json_data.nodes.push({
				id: `${data.entityId}`,
				text: data.name,
				data: {
					entityId: data.entityId,
					fillingStandards: rootNodeIds.includes(data.entityId) ? ROOT_NODE_LEVEL_MAP[data.entityId] :
						data.fillingStandards?.slice(-1),
					hasBusiness: rootNodeIds.includes(data.entityId) ? ROOT_NODE_LEVEL_MAP[data.entityId] : data.hasBusiness,
					isClickable: true
				}
			})

			// 如果是根节点，添加特殊连线
			if (rootNodeIds[0] === data.entityId) {
				__graph_json_data.lines.push({
					from: rootNodeIds[0],
					to: rootNodeIds[1],
					toJunctionPoint: 'tb',
					fromJunctionPoint: 'tb',
					text: (rootPercent.value * 100).toFixed(2) + '%',
					textOffset_x: 25,
				})
				__graph_json_data.lines.push({
					from: rootNodeIds[1],
					to: rootNodeIds[2],
					toJunctionPoint: 'tb',
					fromJunctionPoint: 'tb',
					text: '100%',
					textOffset_x: 25,
				})
			}

			updateLoadingProgress(70, '正在处理关系数据...')

			// 处理投资关系和上级关系 - 使用分块处理
			const totalItems = (data.investList?.length || 0) + (data.upperList?.length || 0)
			if (totalItems > optimizationConfig.value.chunkSize) {
				await processDataInChunks(() => (data, __graph_json_data), [...(data.investList || []), ...(data.upperList || [])])
			} else {
				await processGraphData(data, __graph_json_data)
			}

			updateLoadingProgress(80, '正在优化连线...')
			// 处理兄弟节点关系
			processSiblingConnections(__graph_json_data)

			updateLoadingProgress(90, '正在渲染架构树...')

			// 设置图表数据
			const graphInstance = graphInstanceRef.value?.getInstance()
			if (graphInstance) {
				await graphInstance.setJsonData(__graph_json_data)
				// 等待DOM更新
				await nextTick()

				await graphInstance.doLayout()
				await openByLevel(data.level ?? 1)
				await graphInstance.moveToCenter()

				finishLoading()
			}
		} catch (error) {
			console.error('图表渲染失败:', error)
			handleGraphError(error, data)
			finishLoading()
		}
	}, 'showManageGraph')

	// 分块处理数据，避免长时间阻塞主线程 - 优化版本
	const processDataInChunks = async (processFn, data = null) => {
		return new Promise((resolve) => {
			const config = optimizationConfig.value
			const chunkSize = config.chunkSize
			const delay = deviceInfo.value?.isLowEnd ? config.graphRenderDelay * 2 : config.graphRenderDelay

			// 监控内存使用
			const memoryInfo = monitorMemoryUsage()
			if (memoryInfo?.warning) {
				console.warn('内存使用率过高，建议刷新页面')
				// 在内存不足时减小块大小
				const reducedChunkSize = Math.max(10, Math.floor(chunkSize / 2))
				config.chunkSize = reducedChunkSize
			}

			// 如果没有传入数据或数据量较小，直接处理
			if (!data || (Array.isArray(data) && data.length <= chunkSize)) {
				// 使用 requestIdleCallback 优化性能
				const processTask = () => {
					try {
						const result = processFn()
						resolve(result)
					} catch (error) {
						console.error('数据处理失败:', error)
						handleDataError(error, data)
						resolve(null)
					}
				}

				if (window.requestIdleCallback) {
					requestIdleCallback(processTask, { timeout: 1000 })
				} else {
					setTimeout(processTask, delay)
				}
				return
			}

			// 对大量数据进行分块处理
			if (Array.isArray(data)) {
				const chunks = []
				for (let i = 0; i < data.length; i += chunkSize) {
					chunks.push(data.slice(i, i + chunkSize))
				}

				let processedChunks = []
				let currentChunk = 0

				const processNextChunk = () => {
					if (currentChunk >= chunks.length) {
						// 所有块处理完成，合并结果
						try {
							const result = processFn(processedChunks.flat())
							resolve(result)
						} catch (error) {
							console.error('数据合并失败:', error)
							handleDataError(error, processedChunks)
							resolve(null)
						}
						return
					}

					// 处理当前块
					const processCurrentChunk = () => {
						try {
							const chunk = chunks[currentChunk]
							processedChunks.push(chunk)
							currentChunk++

							// 更新进度
							const progress = Math.round((currentChunk / chunks.length) * 60) + 20
							updateLoadingProgress(progress, `处理数据块 ${currentChunk}/${chunks.length}`)

							// 继续处理下一块
							if (window.requestIdleCallback) {
								requestIdleCallback(processNextChunk, { timeout: 1000 })
							} else {
								setTimeout(processNextChunk, delay)
							}
						} catch (error) {
							console.error(`处理第${currentChunk}块数据失败:`, error)
							currentChunk++
							processNextChunk()
						}
					}

					processCurrentChunk()
				}

				processNextChunk()
			} else {
				// 非数组数据，直接处理
				const processTask = () => {
					try {
						const result = processFn()
						resolve(result)
					} catch (error) {
						console.error('数据处理失败:', error)
						handleDataError(error, data)
						resolve(null)
					}
				}

				if (window.requestIdleCallback) {
					requestIdleCallback(processTask, { timeout: 1000 })
				} else {
					setTimeout(processTask, delay)
				}
			}
		})
	}

	// 获取图表数据 - 优化缓存策略，缓存处理后的数据
	const fetchGraphData = measureTime(async (formModel, options = {}) => {
		const params = cloneDeep(formModel)
		// 确保entityId的一致性
		if (!params.entityId) {
			params.entityId = rootNodeIds[0]
			formModel.entityId = rootNodeIds[0] // 同步更新formModel
		}
		params.isQueryHighestShare = formModel.type ? undefined : '1'
		params.isQueryHighestShare = params.entityId === rootNodeIds[0] ? '1' : params.isQueryHighestShare

		console.log('🔍 查询参数:', {
			entityId: params.entityId,
			year: params.year,
			month: params.month,
			day: params.day,
			type: params.type,
			isQueryHighestShare: params.isQueryHighestShare
		})

		// 检测日期是否变化
		const hasDateChanged = detectDateChange(params)

		// 检查处理后数据的缓存
		const cacheKey = getCacheKey(params)
		const processedCacheKey = cacheKey + '_processed'
		console.log('🔑 缓存键:', processedCacheKey)

		// 检查是否强制刷新或跳过缓存
		const forceRefresh = options.forceRefresh || hasDateChanged
		if (hasDateChanged) {
			console.log('📅 日期已变化，强制刷新缓存')
		}

		const cachedProcessedData = forceRefresh ? null : getFromCache(processedCacheKey)

		if (cachedProcessedData && !forceRefresh) {
			console.log('✅ 找到缓存数据，使用缓存')
			setLoadingState(true, '正在加载缓存数据...', 80)

			try {
				await new Promise(resolve => setTimeout(resolve, 100))
				updateLoadingProgress(90, '正在渲染图表...')

				const graphData = cachedProcessedData.graphData || cachedProcessedData
				const cachedFormModel = cachedProcessedData.formModel || formModel

				// 显示缓存数据的统计信息
				if (cachedProcessedData.stats) {
					console.log('📊 使用缓存数据统计:')
					console.log(`📊 缓存节点数: ${cachedProcessedData.stats.totalNodes}`)
					console.log(`📊 缓存连线数: ${cachedProcessedData.stats.totalLines}`)
					console.log(`📊 缓存根节点数: ${cachedProcessedData.stats.rootNodeCount}`)
				} else {
					// 如果缓存中没有统计信息，现场计算
					const cacheStats = analyzeGraphData(graphData)
					console.log('📊 缓存数据现场统计:')
					console.log(`📊 节点数: ${cacheStats.totalNodes}`)
					console.log(`📊 连线数: ${cacheStats.totalLines}`)
				}

				await renderGraphData(graphData, cachedFormModel)
				return
			} catch (cacheError) {
				console.warn('缓存数据失败，重新处理:', cacheError)
				sessionStorage.removeItem(processedCacheKey)
				updateCacheIndex(processedCacheKey, 'remove')
			}
		} else {
			if (forceRefresh) {
				console.log('🔄 强制刷新，跳过缓存，重新请求接口')
			} else {
				console.log('❌ 未找到缓存数据，重新请求接口')
			}
		}

		setLoadingState(true, '正在获取图表数据...', 10)

		try {
			updateLoadingProgress(20, '正在请求数据...')
			const data = await portraitApi.relationTree(params)

			updateLoadingProgress(40, '正在处理数据...')

			// 尝试使用新的缓存机制，如果失败则回退到原来的方式
			try {
				const processedData = await processAndCacheGraphData(data, formModel, processedCacheKey)
				updateLoadingProgress(80, '正在渲染图表...')
				await renderGraphData(processedData, formModel)
			} catch (processError) {
				console.warn('新的处理方式失败，回退到原来的方式:', processError)
				// 回退到原来的showManageGraph方式
				await showManageGraph(data, formModel)
			}
		} catch (error) {
			console.error('获取图表数据失败:', error)
			handleDataError(error, params)
			finishLoading()
		}
	}, 'fetchGraphData')

	// 处理数据并缓存 - 完整复制showManageGraph的处理逻辑
	const processAndCacheGraphData = async (data, formModel, cacheKey) => {
		try {
			// 完全复用原有的showManageGraph逻辑
			formModel.versionId = data.versionId
			formModel.managementLevel = data.managementLevel // 保存managementLevel到formModel
			formModel.level = data.level // 保存level到formModel
			let __graph_json_data = null

			const isTopLevel = !formModel.entityId || formModel?.entityId === rootNodeIds[0]

			updateLoadingProgress(60, '正在构建架构树...')

			if (isTopLevel) {
				__graph_json_data = {
					rootId: 'root',
					nodes: [
						{
							id: 'root',
							text: 'root',
							opacity: 0
						},
						{
							id: rootNodeIds[1], // '20140822152314001513'
							text: '中国中信股份有限公司',
							expandHolderPosition: data.investList.length > 0 ? 'right' : 'hide',
							expanded: false,
							data: {
								entityId: rootNodeIds[1],
								fillingStandards: 'stock-0',
								hasBusiness: 'Y',
								isClickable: true,
								text: '100%',
								isNeedLoadDataFromRemoteServer: true
							}
						},
						{
							id: rootNodeIds[2], // '20140824164901061873'
							text: '中国中信有限公司',
							expandHolderPosition: data.investList.length > 0 ? 'right' : 'hide',
							expanded: false,
							data: {
								entityId: rootNodeIds[2],
								fillingStandards: 'limit-0',
								hasBusiness: 'Y',
								isClickable: true,
								text: '100%',
								isNeedLoadDataFromRemoteServer: true
							}
						},
					],
					lines: [
						{
							from: 'root',
							to: rootNodeIds[0],
							isHide: true
						},
						{
							from: 'root',
							to: rootNodeIds[1],
							isHide: true
						},
						{
							from: 'root',
							to: rootNodeIds[2],
							isHide: true
						},
					]
				}
			} else {
				let topId = ''
				if (data.upperList && data.upperList.length > 0) {
					topId = findTopEntityId(data.upperList[0])
				} else {
					topId = data.entityId
				}
				__graph_json_data = {
					rootId: 'root',
					nodes: [
						{
							id: 'root',
							text: 'root',
							opacity: 0
						},
					],
					lines: [
						{
							from: 'root',
							to: topId,
							isHide: true
						}
					]
				}
			}

			// 添加当前节点
			__graph_json_data.nodes.push({
				id: `${data.entityId}`,
				text: data.name,
				expandHolderPosition: data.investList.length > 0 ? 'right' : 'hide',
				expanded: false,
				data: {
					entityId: data.entityId,
					fillingStandards: rootNodeIds.includes(data.entityId) ? ROOT_NODE_LEVEL_MAP[data.entityId] :
						data.fillingStandards?.slice(-1),
					hasBusiness: rootNodeIds.includes(data.entityId) ? ROOT_NODE_LEVEL_MAP[data.entityId] : data.hasBusiness,
					isClickable: true,
					isNeedLoadDataFromRemoteServer: rootNodeIds[0] === data.entityId,
				}
			})

			// 如果是根节点，添加特殊连线
			if (rootNodeIds[0] === data.entityId) {
				//处理顶级连线写死
				__graph_json_data.lines.push({
					from: rootNodeIds[0],
					to: rootNodeIds[1],
					lineShape: 1,
					toJunctionPoint: 'tb',
					fromJunctionPoint: 'tb',
					text: (rootPercent.value * 100).toFixed(2) + '%',
					textOffset_x: 25,
				})
				__graph_json_data.lines.push({
					from: rootNodeIds[1],
					to: rootNodeIds[2],
					lineShape: 1,
					toJunctionPoint: 'tb',
					fromJunctionPoint: 'tb',
					text: '100%',
					textOffset_x: 25,
				})
				//处理stock和limit的下级数据
				// 优化：先快速查找目标节点（三层内查找）
				let stock = null
				let limit = null
				let foundTargets = 0
				const targetCount = 2

				// 快速查找函数 - 限制在三层内
				function findTargetNodes(nodes, depth = 0) {
					if (!nodes || foundTargets >= targetCount || depth >= 3) return

					for (const node of nodes) {
						if (node.entityId === rootNodeIds[1]) {
							stock = node
							foundTargets++
						} else if (node.entityId === rootNodeIds[2]) {
							limit = node
							foundTargets++
						}

						// 如果找到所有目标节点，提前退出
						if (foundTargets >= targetCount) return

						// 继续在子节点中查找
						if (node.detailList && depth < 2) {
							findTargetNodes(node.detailList, depth + 1)
						}
					}
				}

				// 先快速找到目标节点
				findTargetNodes(data.investList)

				// 批量处理stock的子节点
				if (stock?.detailList?.length > 0) {
					const stockChildren = stock.detailList.filter(s => s.entityId !== rootNodeIds[2])
					__graph_json_data.nodes[1].data.childrenData = stockChildren
				}

				//批量处理limit的子节点
				if (limit?.detailList?.length > 0) {
					__graph_json_data.nodes[2].data.childrenData = limit.detailList
				}
			}

			updateLoadingProgress(70, '正在处理关系数据...')

			// 处理投资关系和上级关系 - 使用分块处理
			const totalItems = (data.investList?.length || 0) + (data.upperList?.length || 0)
			if (totalItems > optimizationConfig.value.chunkSize) {
				await processDataInChunks(() => processGraphData(data, __graph_json_data), [...(data.investList || []), ...(data.upperList || [])])
			} else {
				await processGraphData(data, __graph_json_data)
			}

			updateLoadingProgress(80, '正在优化连线...')
			// 处理兄弟节点关系
			// processSiblingConnections(__graph_json_data)

			// 统计处理后的数据
			console.log("🚀 ~ useGraphData.js:500 ~ useGraphData ~ __graph_json_data:", __graph_json_data)
			// 缓存处理后的完整数据
			const cacheData = {
				graphData: __graph_json_data,
				formModel: cloneDeep(formModel),
				timestamp: Date.now(),
			}

			setCache(cacheKey, cacheData)

			return __graph_json_data
		} catch (error) {
			console.error('处理图表数据失败:', error)
			throw error
		}
	}

	// 统计节点信息
	const analyzeGraphData = (graphData) => {
		const stats = {
			totalNodes: 0,
			totalLines: 0,
			nodesByType: {},
			nodesByLevel: {},
			rootNodeCount: 0,
			businessNodeCount: 0
		}

		if (graphData?.nodes) {
			stats.totalNodes = graphData.nodes.length

			graphData.nodes.forEach(node => {
				// 按类型统计
				const nodeType = node.data?.fillingStandards || 'unknown'
				stats.nodesByType[nodeType] = (stats.nodesByType[nodeType] || 0) + 1

				// 按层级统计
				const level = node.data?.level || 'unknown'
				stats.nodesByLevel[level] = (stats.nodesByLevel[level] || 0) + 1

				// 根节点统计
				if (rootNodeIds.includes(node.data?.entityId)) {
					stats.rootNodeCount++
				}

				// 业务节点统计
				if (node.data?.hasBusiness === 'Y') {
					stats.businessNodeCount++
				}
			})
		}

		if (graphData?.lines) {
			stats.totalLines = graphData.lines.length
		}

		return stats
	}

	// 渲染图表数据 - 完整复制showManageGraph的渲染逻辑
	const renderGraphData = async (graphData, formModel) => {
		try {
			updateLoadingProgress(90, '正在渲染架构树...')

			// 设置图表数据
			const graphInstance = graphInstanceRef.value?.getInstance()
			if (graphInstance) {
				// const opts = graphInstance.getGraphJsonOptions()
			  // opts.debug = false
				// if (formModel.type && formModel.entityId !== rootNodeIds[0]) {
				// 	opts.layouts[0].min_per_height = 100
				// } else {
				// 	opts.layouts[0].min_per_height = 40
				// }
				// await graphInstance.setOptions(JSON.parse(JSON.stringify(opts)))
				await graphInstance.setJsonData(graphData)
				await nextTick()
				await graphInstance.doLayout()
				await openByLevel(formModel.level  < 1 ? 1: formModel.level)
				await graphInstance.moveToCenter()
				finishLoading()
			} else {
				console.error('图表实例不存在')
				finishLoading()
			}
		} catch (error) {
			console.error('渲染图表失败:', error)
			handleGraphError(error, formModel)
			finishLoading()
		}
	}


	// 获取根节点数据 - 优化版本
	const getRootNodeData = async (formModel) => {
		const params = cloneDeep(formModel)
		params.entityId = rootNodeIds[1] // '20140822152314001513'

		rootLoading.value = true
		try {
			const data = await internalinvestmentApi.getPage(params)

			// 优化：限制并发请求数量，避免浏览器连接数限制
			const maxConcurrent = 5
			const chunks = []
			for (let i = 0; i < data.records.length; i += maxConcurrent) {
				chunks.push(data.records.slice(i, i + maxConcurrent))
			}

			rootData = []
			let totalPercent = 0

			// 分批处理请求
			for (const chunk of chunks) {
				const chunkPromises = chunk.map(async (r) => {
					const res = await portraitApi.relationTree({
						...params,
						entityId: r.investorId
					})
					return {
						...res,
						shareRate: r.shareRate
					}
				})

				const chunkResults = await Promise.all(chunkPromises)
				rootData.push(...chunkResults)

				// 优化：边处理边计算，减少后续遍历
				chunkResults.forEach((item) => {
					if (item.upperList && item.upperList[0]) {
						const topGroup = item.upperList[0]
						const num = parseFloat(topGroup.percent.replace('%', '')) / 100
						const accTotal = item.shareRate * num
						totalPercent += accTotal
					}
				})
			}

			rootPercent.value = totalPercent
		} catch (error) {
			console.error('获取根节点数据失败:', error)
		} finally {
			rootLoading.value = false
		}
	}

	// 处理图表数据
	const processGraphData = async (data, __graph_json_data) => {

		function pushNodeAndChildren(node, parentId, isUpper = false, graphData, rootNodeIds, rootNodeLevelMap) {
			graphData.nodes.push({
				id: `${node.entityId}`,
				text: node.name,
				data: {
					entityId: node.entityId,
					fillingStandards: rootNodeIds.includes(node.entityId) ? rootNodeLevelMap[node.entityId] : node.fillingStandards.slice(-1),
					hasBusiness: rootNodeIds.includes(node.entityId) ? 'Y' : node.hasBusiness,
					isClickable: isUpper ? node.isClickable === '1' : true,
					text: node.percent
				}
			})

			if (parentId) {
				const exceptNode = `${data.entityId}` !== DISCORD_NODES[0].entityId &&
								  `${data.entityId}` !== DISCORD_NODES[1].entityId &&
								  `${data.entityId}` !== rootNodeIds[1]
				const isCurTop = rootNodeIds.includes(node.entityId) && exceptNode
				const linsShape = isUpper && node.detailList.length < 1 ? 2 : 4

				graphData.lines.push(
					isUpper
						? {
							from: `${node.entityId}`,
							to: parentId,
							text: node.percent,
						  useTextPath: true,
							// textOffset_x: isCurTop ? 60 : 0,
							// textOffset_y: -3,
							// linsShape
						}
						: {
							from: parentId,
							to: `${node.entityId}`,
							text: node.percent,
							useTextPath: true,
							// textOffset_x: isCurTop ? 25 : 60,
							// textOffset_y: -3,
							// linsShape,
							fromJunctionPoint: isCurTop ? 'tb' : 'lr',
							toJunctionPoint: isCurTop ? 'tb' : 'lr'
						}
				)
			}

			if (node.detailList && node.detailList.length > 0) {
				node.detailList.forEach(child => {
					pushNodeAndChildren(child, `${node.entityId}`, isUpper, graphData, rootNodeIds, ROOT_NODE_LEVEL_MAP)
				})
			}
		}

		// 优化：使用Map缓存和迭代替代递归，避免栈溢出
		const entityCache = new Map()

		function deepFindByEntityId(data, entityId) {
			// 缓存查找
			if (entityCache.has(entityId)) {
				return entityCache.get(entityId)
			}

			// 使用队列进行广度优先搜索，避免深度递归
			const queue = [...data]

			while (queue.length > 0) {
				const item = queue.shift()

				if (item.entityId === entityId) {
					entityCache.set(entityId, item)
					return item
				}

				// 将子节点加入队列
				if (item.detailList && item.detailList.length > 0) {
					queue.push(...item.detailList)
				}
			}

			entityCache.set(entityId, undefined)
			return undefined
		}

		// 清理实体缓存
		const clearEntityCache = () => {
			entityCache.clear()
		}

		// 迭代过滤树形结构 - 针对三层结构优化
		function filterTreeNodesIterative(nodes, filterFn) {
			if (!nodes || !Array.isArray(nodes)) return []

			const result = []

			// 第一层
			for (const node1 of nodes) {
				if (filterFn(node1)) {
					const filteredNode1 = { ...node1 }

					// 第二层
					if (node1.detailList && node1.detailList.length > 0) {
						const level2Nodes = []
						for (const node2 of node1.detailList) {
							if (filterFn(node2)) {
								const filteredNode2 = { ...node2 }

								// 第三层
								if (node2.detailList && node2.detailList.length > 0) {
									filteredNode2.detailList = node2.detailList.filter(filterFn)
								}
								level2Nodes.push(filteredNode2)
							}
						}
						filteredNode1.detailList = level2Nodes
					}
					result.push(filteredNode1)
				}
			}

			return result
		}

		// 处理下级投资关系 - 优化版本（修正树形结构处理）
		if (data?.investList?.length > 0) {
			let investNodes = data?.investList
			if (rootNodeIds[0] === data?.entityId) {
				// 优化：使用Set提高查找性能
				const discordNodeIds = new Set(DISCORD_NODES.map(n => n.entityId))
				// 使用优化的迭代过滤（针对三层结构）
				investNodes = filterTreeNodesIterative(data.investList, node => {
					return !discordNodeIds.has(node.entityId)
				})
				// 挂载中信集团数据
				__graph_json_data.nodes[3].data.childrenData = investNodes
				return
			}

			// 批量处理投资节点
			investNodes.forEach(v => {
				pushNodeAndChildren(v, `${data.entityId}`, false, __graph_json_data, rootNodeIds, ROOT_NODE_LEVEL_MAP)
			})
		}

		// 处理上级关系 - 优化版本
		if (data?.upperList?.length > 0) {
			data.upperList.forEach(v => {
				pushNodeAndChildren(v, `${data.entityId}`, true, __graph_json_data, rootNodeIds, ROOT_NODE_LEVEL_MAP)
			})
		}

		console.log('Graph data processed:', __graph_json_data)
		originData.value = cloneDeep(__graph_json_data)

		// 清理实体缓存，释放内存
		clearEntityCache()
	}


	// 处理兄弟节点连线的特殊样式 - 优化版本，增加父连线相邻排列逻辑
	const processSiblingConnections = (__graph_json_data) => {
		console.log('Processing sibling connections...')
		console.log('输入数据连线数量:', __graph_json_data.lines.length)
		console.log("🚀 ~ useGraphData.js:1204 ~ processSiblingConnections ~ 连线数据:", __graph_json_data)

		let siblingCount = 0
		let parentChildCount = 0
		const siblingPairs = []

		// 第一遍：构建父子关系映射，识别真正的兄弟连线
		const parentChildMap = new Map() // parent -> [children]
		const childParentMap = new Map() // child -> [parents]

		// 先收集所有父子关系
		__graph_json_data.lines.forEach(line => {
			// 默认设置为父子连线
			if (!line.connectionType) {
				line.connectionType = 'parent-child'
			}

			if (line.connectionType === 'parent-child') {
				// 建立父子关系映射
				if (!parentChildMap.has(line.from)) {
					parentChildMap.set(line.from, [])
				}
				parentChildMap.get(line.from).push(line.to)

				if (!childParentMap.has(line.to)) {
					childParentMap.set(line.to, [])
				}
				childParentMap.get(line.to).push(line.from)
			}
		})

		console.log('父子关系映射构建完成')
		console.log('父节点数量:', parentChildMap.size)

		// 第二遍：重新检查连线，识别应该是兄弟关系的连线
		__graph_json_data.lines.forEach(line => {
			if (line.connectionType === 'parent-child') {
				const fromNode = line.from
				const toNode = line.to

				// 检查from和to是否有共同的父节点（即它们是兄弟节点）
				const fromParents = childParentMap.get(fromNode) || []
				const toParents = childParentMap.get(toNode) || []

				// 找共同父节点
				const commonParents = fromParents.filter(parent => toParents.includes(parent))

				if (commonParents.length > 0) {
					// 有共同父节点，这应该是兄弟连线
					console.log(`发现兄弟连线: ${fromNode} -> ${toNode} (共同父节点: ${commonParents.join(', ')})`)
					line.connectionType = 'sibling'
					line.toJunctionPoint = 'tb'
					line.fromJunctionPoint = 'tb'
					line.textOffset_x = 30
					siblingCount++

					// 收集兄弟节点对
					siblingPairs.push([fromNode, toNode])
				} else {
					parentChildCount++
				}
			} else if (line.connectionType === 'sibling') {
				// 已经标记为兄弟连线的
				line.toJunctionPoint = 'tb'
				line.fromJunctionPoint = 'tb'
				line.textOffset_x = 30
				siblingCount++

				siblingPairs.push([line.from, line.to])
			}
		})

		console.log(`连线重新分类完成:`)
		console.log(`- 识别出的兄弟连线: ${siblingCount}`)
		console.log(`- 父子连线: ${parentChildCount}`)

		// 第三遍：处理父连线相邻排列
		if (siblingPairs.length > 0) {
			console.log(`开始处理 ${siblingPairs.length} 对兄弟节点的父连线相邻排列`)

			// 构建完整的兄弟关系网络
			const allSiblingRelations = findAllSiblingRelations(__graph_json_data, siblingPairs)
			console.log('所有兄弟关系数量:', allSiblingRelations.length)

			// 处理父连线相邻排列
			arrangeParentLinesAdjacent(__graph_json_data, allSiblingRelations)
		}

		console.log(`兄弟节点连线处理完成:`)
		console.log(`- 兄弟连线数量: ${siblingCount}`)
		console.log(`- 父子连线数量: ${parentChildCount}`)
		console.log(`- 总连线数量: ${__graph_json_data.lines.length}`)

		return __graph_json_data
	}

	// 构建兄弟节点组，处理多兄弟节点的情况
	const buildSiblingGroups = (siblingPairs) => {
		const siblingGroups = []
		const processedNodes = new Set()

		// 为每对兄弟节点构建连通图
		siblingPairs.forEach(([node1, node2]) => {
			if (!processedNodes.has(node1) && !processedNodes.has(node2)) {
				// 创建新的兄弟组
				const group = [node1, node2]
				processedNodes.add(node1)
				processedNodes.add(node2)

				// 查找其他相关的兄弟节点，构建完整的兄弟组
				let foundNew = true
				while (foundNew) {
					foundNew = false
					siblingPairs.forEach(([pairA, pairB]) => {
						// 如果其中一个节点已在组中，另一个不在，则添加到组中
						if (group.includes(pairA) && !group.includes(pairB) && !processedNodes.has(pairB)) {
							group.push(pairB)
							processedNodes.add(pairB)
							foundNew = true
						} else if (group.includes(pairB) && !group.includes(pairA) && !processedNodes.has(pairA)) {
							group.push(pairA)
							processedNodes.add(pairA)
							foundNew = true
						}
					})
				}

				siblingGroups.push(group)
			}
		})

		return siblingGroups
	}

	// 基于兄弟连线找出所有兄弟关系（包括共同父节点的兄弟关系）
	const findAllSiblingRelations = (__graph_json_data, siblingPairs) => {
		console.log('查找所有兄弟关系...')

		// 构建父子关系映射
		const parentChildMap = new Map() // parent -> [children]
		const childParentMap = new Map() // child -> [parents]

		__graph_json_data.lines.forEach(line => {
			if (line.connectionType === 'parent-child') {
				// 建立父子关系
				if (!parentChildMap.has(line.from)) {
					parentChildMap.set(line.from, [])
				}
				parentChildMap.get(line.from).push(line.to)

				if (!childParentMap.has(line.to)) {
					childParentMap.set(line.to, [])
				}
				childParentMap.get(line.to).push(line.from)
			}
		})

		// 从直接兄弟连线开始，扩展找出所有兄弟关系
		const allSiblingPairs = [...siblingPairs] // 复制原始兄弟对

		// 基于共同父节点找出更多兄弟关系
		parentChildMap.forEach((children, parent) => {
			if (children.length > 1) {
				// 为这些子节点创建兄弟关系对
				for (let i = 0; i < children.length; i++) {
					for (let j = i + 1; j < children.length; j++) {
						const child1 = children[i]
						const child2 = children[j]

						// 检查是否已经存在这个兄弟关系
						const exists = allSiblingPairs.some(([a, b]) =>
							(a === child1 && b === child2) || (a === child2 && b === child1)
						)

						if (!exists) {
							allSiblingPairs.push([child1, child2])
						}
					}
				}
			}
		})

		console.log(`总共找到 ${allSiblingPairs.length} 对兄弟关系`)
		return allSiblingPairs
	}

	// 按照兄弟链的顺序排列父连线
	const orderParentLinesBySiblingChain = (parentLines, siblingPairs) => {
		console.log('按兄弟链顺序排列父连线...')

		// 构建兄弟连线的顺序映射
		const siblingOrder = new Map()
		let orderIndex = 0

		// 找出兄弟链的起始点
		const allFromNodes = new Set(siblingPairs.map(pair => pair[0]))
		const allToNodes = new Set(siblingPairs.map(pair => pair[1]))
		const startNodes = [...allFromNodes].filter(node => !allToNodes.has(node))

		// 构建兄弟链的顺序
		const siblingChain = []
		if (startNodes.length > 0) {
			let currentNode = startNodes[0]
			siblingChain.push(currentNode)
			siblingOrder.set(currentNode, orderIndex++)

			// 沿着兄弟链构建顺序
			while (currentNode) {
				const nextPair = siblingPairs.find(pair => pair[0] === currentNode)
				if (nextPair) {
					currentNode = nextPair[1]
					siblingChain.push(currentNode)
					siblingOrder.set(currentNode, orderIndex++)
				} else {
					break
				}
			}
		}

		// 按照兄弟链的顺序排列父连线
		const orderedLines = parentLines.sort((a, b) => {
			const orderA = siblingOrder.get(a.to) ?? 999
			const orderB = siblingOrder.get(b.to) ?? 999
			return orderA - orderB
		})
		return orderedLines
	}

	// 处理兄弟节点的父连线相邻排列
	const arrangeParentLinesAdjacent = (__graph_json_data, siblingPairs) => {
		console.log('Processing parent lines adjacency...')

		// 第一步：构建兄弟节点网络，处理多兄弟节点情况
		const siblingGroups = buildSiblingGroups(siblingPairs)
		console.log(`构建了 ${siblingGroups.length} 个兄弟节点组:`, siblingGroups)

		const parentLineGroups = []

		// 第二步：为每个兄弟节点组找到它们的共同父连线，并按兄弟链顺序排列
		siblingGroups.forEach((siblingGroup, groupIndex) => {
			// 查找每个兄弟节点的父连线
			const parentLinesMap = new Map() // parentId -> [lines]

			siblingGroup.forEach(siblingId => {
				const parentLines = __graph_json_data.lines.filter(line => line.to === siblingId && line.connectionType === 'parent-child')
				parentLines.forEach(parentLine => {
					const parentId = parentLine.from
					if (!parentLinesMap.has(parentId)) {
						parentLinesMap.set(parentId, [])
					}
					parentLinesMap.get(parentId).push(parentLine)
				})
			})

			// 找到有多个子连线的父节点（即共同父节点）
			parentLinesMap.forEach((lines, parentId) => {
				if (lines.length > 1) {
					// 按照兄弟链的顺序排列父连线
					const orderedParentLines = orderParentLinesBySiblingChain(lines, siblingPairs)

					parentLineGroups.push({
						parentNode: parentId,
						siblings: orderedParentLines.map(line => line.to),
						parentLines: orderedParentLines
					})
				}
			})
		})

		if (parentLineGroups.length === 0) {
			console.log('没有发现需要相邻排列的父连线')
			return
		}

		// 重新排列lines数组，让父连线和兄弟连线都相邻
		const newLines = []
		const processedLines = new Set()

		// 第一步：添加父连线组（保持相邻）
		parentLineGroups.forEach(parentGroup => {
			parentGroup.parentLines.forEach(parentLine => {
				if (!processedLines.has(parentLine)) {
					newLines.push(parentLine)
					processedLines.add(parentLine)
				}
			})
		})

		// 第二步：添加兄弟连线组（保持相邻）
		const siblingLines = __graph_json_data.lines.filter(line =>
			line.connectionType === 'sibling' && !processedLines.has(line)
		)

		if (siblingLines.length > 0) {
			const arrangedSiblingLines = arrangeSiblingLinesAdjacent(siblingLines)

			arrangedSiblingLines.forEach(line => {
				if (!processedLines.has(line)) {
					newLines.push(line)
					processedLines.add(line)
				}
			})
		}

		// 第三步：添加剩余的连线
		__graph_json_data.lines.forEach(line => {
			if (!processedLines.has(line)) {
				newLines.push(line)
				processedLines.add(line)
			}
		})

		// 更新lines数组
		__graph_json_data.lines = newLines

		console.log(`连线相邻排列完成:`)
		console.log(`- 处理的父连线组数: ${parentLineGroups.length}`)
		console.log(`- 兄弟连线数: ${siblingLines.length}`)
		console.log(`- 连线总数: ${newLines.length}`)
	}

	// 处理兄弟连线的相邻排列
	const arrangeSiblingLinesAdjacent = (siblingLines) => {
		console.log('排列兄弟连线相邻...')

		const arrangedLines = []
		const processedLines = new Set()

		// 找出所有兄弟连线的起始点（没有其他兄弟连线指向它的节点）
		const allFromNodes = new Set(siblingLines.map(line => line.from))
		const allToNodes = new Set(siblingLines.map(line => line.to))

		// 起始点：在from中但不在to中的节点
		const startNodes = [...allFromNodes].filter(node => !allToNodes.has(node))

		// 构建从节点到连线的映射
		const fromNodeToLine = new Map()
		siblingLines.forEach(line => {
			fromNodeToLine.set(line.from, line)
		})

		// 从每个起始点开始构建连接链
		startNodes.forEach(startNode => {
			const chain = buildSiblingChainFromNode(startNode, fromNodeToLine, new Set())

			// 添加连接链到结果中
			chain.forEach(line => {
				if (!processedLines.has(line)) {
					arrangedLines.push(line)
					processedLines.add(line)
				}
			})

			if (chain.length > 0) {
				console.log(`兄弟连线链 (${chain.length}条): ${chain.map(l => `${l.from}->${l.to}`).join(' -> ')}`)
			}
		})

		// 处理剩余的连线（可能是环形结构）
		siblingLines.forEach(line => {
			if (!processedLines.has(line)) {
				arrangedLines.push(line)
				processedLines.add(line)
				console.log(`添加剩余兄弟连线: ${line.from}->${line.to}`)
			}
		})

		console.log(`兄弟连线排列完成，共 ${arrangedLines.length} 条`)
		return arrangedLines
	}

	// 从指定节点开始构建兄弟连线链
	const buildSiblingChainFromNode = (startNode, fromNodeToLine, visited) => {
		const chain = []
		let currentNode = startNode

		while (currentNode && !visited.has(currentNode)) {
			visited.add(currentNode)

			// 查找从当前节点出发的连线
			const currentLine = fromNodeToLine.get(currentNode)
			if (!currentLine) break

			chain.push(currentLine)

			// 移动到下一个节点
			currentNode = currentLine.to
		}

		return chain
	}

	// 按层级展开/收起
	const openByLevel = async (level) => {
		console.log("=>(useGraphData.js:1691) level", level);
		const graphInstance = graphInstanceRef.value?.getInstance()
		if (graphInstance) {
			// Reset data
			graphInstance.getNodes().forEach(node => {
				node.expanded = true
				node.alignItems = 'top'
			})

			graphInstance.getNodes().forEach(node => {
				if (rootNodeIds.includes(node.data.entityId)) {
					node.className = `${node.data.hasBusiness} my-industy-node-level-` + node.data.fillingStandards
				} else {
					if (isNil(node.data.hasBusiness) || isNil(node.data.fillingStandards) || node.data.fillingStandards.length === 0) {
						node.className = `K my-industy-node-level-default-0`
					} else {
						node.className = `${node.data.hasBusiness} my-industy-node-level-` + node.data.fillingStandards
					}
				}
			})


			graphInstance.getNodes().forEach(node => {
				// Determine the level of the node (root node is 0)
				if (Math.abs(node.lot.level) >= level) {
					// Collapse node
					node.expanded = false
				}
			})

			await graphInstance.moveToCenter()
			await graphInstance.doLayout()
		}
	}

	const openByLevelByNode = async (selectNode, command = 'EXPAND_ALL') => {
		const graphInstance = graphInstanceRef.value?.getInstance()
		// 递归函数：从allNodes中提取所有节点的id
		const getAllNodeIds = (nodes) => {
			const result = []

			// 递归遍历函数
			function traverse(nodeList) {
				if (!nodeList || !Array.isArray(nodeList)) {
					return
				}
				for (let i = 0; i < nodeList.length; i++) {
					const node = nodeList[i]
					// 添加当前节点的id
					if (node && node.id) {
						result.push(node.id)
					}
					// 检查并递归处理子节点
					if (node.targetTo.length > 0) {
						traverse(node.targetTo)
					}
				}
			}

			traverse(nodes)
			return result
		}
		// 获取所有节点的id（包括嵌套的子节点）
		const allNodeIds = getAllNodeIds([selectNode])
		if (graphInstance) {
			// Reset data
			graphInstance.getNodes().forEach(no => {
				if (allNodeIds.includes(no.id)) {
					no.expanded = command === 'EXPAND_ALL'
				}
			})
			await graphInstance.moveToCenter()
			await graphInstance.doLayout()
		}
	}

	// 节点关系分析工具函数 - 优化版本
	// 构建关系索引，避免重复遍历
	let relationshipIndex = null

	const buildRelationshipIndex = (lines) => {
		if (relationshipIndex) return relationshipIndex

		relationshipIndex = {
			parentMap: new Map(), // nodeId -> parentId
			childrenMap: new Map(), // parentId -> [childIds]
			siblingMap: new Map() // nodeId -> [siblingIds]
		}

		// 一次遍历构建所有关系
		lines.forEach(line => {
			const { from, to } = line

			// 构建父子关系
			relationshipIndex.parentMap.set(to, from)

			if (!relationshipIndex.childrenMap.has(from)) {
				relationshipIndex.childrenMap.set(from, [])
			}
			relationshipIndex.childrenMap.get(from).push(to)
		})

		// 构建兄弟关系
		relationshipIndex.childrenMap.forEach((children) => {
			children.forEach(child => {
				const siblings = children.filter(c => c !== child)
				relationshipIndex.siblingMap.set(child, siblings)
			})
		})

		return relationshipIndex
	}

	const findParentNode = (nodeId, lines) => {
		const index = buildRelationshipIndex(lines)
		return index.parentMap.get(nodeId) || null
	}

	const areSiblingNodes = (nodeId1, nodeId2, graphData) => {
		const index = buildRelationshipIndex(graphData.lines)
		const siblings = index.siblingMap.get(nodeId1) || []
		return siblings.includes(nodeId2)
	}

	const getSiblingNodes = (nodeId, graphData) => {
		const index = buildRelationshipIndex(graphData.lines)
		return index.siblingMap.get(nodeId) || []
	}

	const getChildNodes = (nodeId, graphData) => {
		const index = buildRelationshipIndex(graphData.lines)
		return index.childrenMap.get(nodeId) || []
	}

	// 查找兄弟节点之间的连线
	const findSiblingConnections = (nodeId, graphData) => {
		const siblings = getSiblingNodes(nodeId, graphData)
		const siblingConnections = []

		// 查找从当前节点到兄弟节点的连线
		siblings.forEach(siblingId => {
			const connectionToSibling = graphData.lines.find(line =>
				line.from === nodeId && line.to === siblingId
			)
			if (connectionToSibling) {
				siblingConnections.push({
					...connectionToSibling,
					type: 'to_sibling',
					siblingId
				})
			}

			// 查找从兄弟节点到当前节点的连线
			const connectionFromSibling = graphData.lines.find(line =>
				line.from === siblingId && line.to === nodeId
			)
			if (connectionFromSibling) {
				siblingConnections.push({
					...connectionFromSibling,
					type: 'from_sibling',
					siblingId
				})
			}
		})

		return siblingConnections
	}

	// 查找所有兄弟节点之间的连线
	const findAllSiblingConnections = (graphData) => {
		const allSiblingConnections = []

		graphData.lines.forEach(line => {
			const fromNode = line.from
			const toNode = line.to

			// 检查 from 和 to 是否为兄弟节点
			if (areSiblingNodes(fromNode, toNode, graphData)) {
				allSiblingConnections.push({
					...line,
					type: 'sibling_connection',
					fromNode: fromNode,
					toNode: toNode,
					relationship: `${fromNode} 持有 ${toNode} ${line.text || ''}的股份`
				})
			}
		})

		return allSiblingConnections
	}

	// 判断某条线是否是兄弟节点连线 - 返回布尔值
	const isSiblingConnection = (line, graphData) => {
		return areSiblingNodes(line.from, line.to, graphData)
	}

	// 专门查找指定节点作为兄弟节点的连线
	const findSiblingConnectionsForNode = (nodeId, graphData) => {
		return graphData.lines.filter(line => {
			// 查找以该节点为起点的兄弟连线
			const isFromSibling = line.from === nodeId && areSiblingNodes(line.from, line.to, graphData)
			// 查找以该节点为终点的兄弟连线
			const isToSibling = line.to === nodeId && areSiblingNodes(line.from, line.to, graphData)

			return isFromSibling || isToSibling
		}).map(line => ({
			...line,
			type: line.from === nodeId ? 'outgoing_sibling' : 'incoming_sibling',
			siblingNode: line.from === nodeId ? line.to : line.from
		}))
	}

	// 防抖版本的数据获取函数
	const debouncedFetchGraphData = debounce(fetchGraphData, optimizationConfig.value.debounceDelay)

	return {
		loading,
		rootLoading,
		originData,
		rootPercent,
		graphInstanceRef,
		graphInstanceRootRef,
		rootNodeIds,
		DISCORD_NODES,
		ROOT_NODE_LEVEL_MAP,
		graphOptions,
		graphRootOptions,
		isTop,
		fetchGraphData,
		debouncedFetchGraphData,
		getRootNodeData,
		openByLevel,
		openByLevelByNode,
		rootData: () => rootData,
		// 新增的优化功能
		isTransitioning,
		loadingText,
		loadingProgress,
		setLoadingState,
		updateLoadingProgress,
		finishLoading,
		clearCache,
		clearExpiredCache, // 清理过期缓存
		stopCacheCleanup, // 停止缓存清理定时器
		// 缓存工具函数
		getCacheKey,
		getFromCache,
		setCache: setCache,
		updateCacheIndex,
		processDataInChunks,
		// 新增的数据处理函数
		processAndCacheGraphData,
		renderGraphData,
		// 节点关系分析函数
		findParentNode,
		areSiblingNodes,
		getSiblingNodes,
		getChildNodes,
		findSiblingConnections,
		findAllSiblingConnections,
		findSiblingConnectionsForNode,
		isSiblingConnection,
		processSiblingConnections
	}
}

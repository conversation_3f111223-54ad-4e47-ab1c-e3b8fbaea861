import { ref, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import portraitApi from '@/api/biz/portraitApi'
import { isNil } from 'lodash-es'

export function useNodeActions() {
	const router = useRouter()

	// 响应式数据
	const showContextMenu = ref(false)
	const showNodeTooltipPanel = ref(false)
	const showRootRelationPanel = ref(false)
	const rootLoading = ref(false)
	const selectedNode = ref({})
	const pageRef = ref()
	const graphWrapRef = ref()
	const nodeMenuPanelPos = ref({ x: 0, y: 0 })
	const nodeTooltipPos = ref({ x: 0, y: 0 })

	// 处理节点右键菜单
	const handleNodeContextMenu = (e, nodeType, nodeObject) => {
		console.log('Node context menu:', nodeObject, e)
		if (nodeType === 'node') {
			nodeMenuPanelPos.value = { x: e.clientX + 10, y: e.clientY + 10 }
			showContextMenu.value = true
			showNodeTooltipPanel.value = false
			showRootRelationPanel.value = false
			selectedNode.value = nodeObject
		} else {
			showContextMenu.value = false
		}
	}

	// 处理节点点击
	const handleNodeClick = async (nodeObject, graphInstanceRef) => {
		const graphInstance = graphInstanceRef.value?.getInstance()
		if (graphInstance) {
			const allLinks = graphInstance.getLinks() || []
			allLinks.forEach(link => {
				link.relations.forEach(line => {
					line.color = '#333'
					line.fontColor = '#333'
				})
			})

			markChainColor(allLinks, nodeObject.id)
			await graphInstance.dataUpdated()
		}
	}

	// 标记链路颜色
	const markChainColor = (data, leafId, color = "#D20000") => {
		console.log("=>(useNodeActions.js:74) data", data);
		let current = leafId
		while (true) {
			let found = false
			for (const item of data) {
				console.log("=>(useNodeActions.js:57) item", item);
				const matched = item.relations?.filter(r => r.to === current) || []
				console.log("=>(useNodeActions.js:59) current", current);
				console.log("=>(useNodeActions.js:59) matched", matched);
				if (matched.length > 0) {
					found = true
					matched.forEach(rel => {
						rel.color = color
						rel.fontColor = color
						current = rel.from
					})
					break
				}
			}
			if (!found) break
		}
	}


	// 执行命令
	const exec = (command, formModel, fetchGraphDataFn, openByLevelFn) => {
		switch (command) {
			case 'DETAILS':
				handleNodeAction('3', formModel)
				break
			case 'FOCUS':
				handleNodeAction('1', formModel, fetchGraphDataFn)
				break
			case 'PORTRAIT':
				handleNodeAction('2', formModel)
				break
			case 'UNEXPAND_ALL':
				if (openByLevelFn) openByLevelFn(selectedNode.value, command)
				break
			case 'EXPAND_ALL':
				if (openByLevelFn) openByLevelFn(selectedNode.value, command)
				break
			default:
				break
		}
		showContextMenu.value = false
	}

	// 处理节点操作
	const handleNodeAction = async (actionType, formModel, fetchGraphDataFn) => {
		const { data } = selectedNode.value

		if (actionType === '1') {
			// 聚焦
			formModel.entityId = data.entityId
			if (fetchGraphDataFn) {
				fetchGraphDataFn()
			}
		}

		if (actionType === '2') {
			// 画像
			const targetRoute = router.resolve({
				path: '/institutionalPortrait/service',
				query: { entityId: data.entityId }
			})
			window.open(targetRoute.href, '_blank')
		}

		if (actionType === '3') {
			// 详情 - 使用右键菜单的位置
			await handleNodeTooltip(selectedNode.value, formModel, nodeMenuPanelPos.value)
		}

		showRootRelationPanel.value = false
		showContextMenu.value = false
		// 注意：不要在这里关闭 showNodeTooltipPanel，因为详情面板需要保持显示
	}

	// 处理节点提示框
	const handleNodeTooltip = async (nodeObject, formModel, position = null) => {
		console.log('Node tooltip:', nodeObject)
		const { data } = nodeObject

		if (!showNodeTooltipPanel.value) {
			try {
				const res = await portraitApi.institutionInfo({
					entityId: data.entityId,
					versionId: formModel?.versionId
				})
				selectedNode.value = Object.assign(nodeObject, res)
				// 默认位置：页面中央偏右
				const basePosition = pageRef.value?.getBoundingClientRect()
				if (basePosition) {
					nodeTooltipPos.value.x = basePosition.x
					nodeTooltipPos.value.y = basePosition.y
				} else {
					// 如果无法获取页面位置，使用屏幕中央
					nodeTooltipPos.value.x = window.innerWidth * 0.5
					nodeTooltipPos.value.y = window.innerHeight * 0.3
				}
				showNodeTooltipPanel.value = true
				showContextMenu.value = false
			} catch (error) {
				console.error('获取节点详情失败:', error)
			}
		}
	}

	// 显示根节点关系
	const showRootNodeRelation = async (e, loadGraphRootDataFn, rootData) => {
		rootLoading.value = true
		nodeMenuPanelPos.value = { x: e.clientX + 10, y: e.clientY - 100 }
		showRootRelationPanel.value = true
		setTimeout(async () => {
			if (loadGraphRootDataFn && rootData) {
				await loadGraphRootDataFn(rootData)
			}
		});
	}

	// 关闭所有面板
	const closeAllPanels = () => {
		showContextMenu.value = false
		showNodeTooltipPanel.value = false
		showRootRelationPanel.value = false
	}


	function buildNodeAndChildren(node, parentId, isUpper = false, graphData, rootNodeIds, DISCORD_NODES, rootNodeLevelMap, rootData) {
		graphData.nodes.push({
			id: `${node.entityId}`,
			text: node.name,
			expanded: false,
			data: {
				entityId: node.entityId,
				fillingStandards: rootNodeIds.includes(node.entityId) ? rootNodeLevelMap[node.entityId] : node.fillingStandards.slice(-1),
				hasBusiness: rootNodeIds.includes(node.entityId) ? 'Y' : node.hasBusiness,
				isClickable: isUpper ? node.isClickable === '1' : true,
				text: node.percent
			}
		})

		if (parentId) {
			const exceptNode = `${rootData.entityId}` !== DISCORD_NODES[0].entityId &&
				`${rootData.entityId}` !== DISCORD_NODES[1].entityId &&
				`${rootData.entityId}` !== rootNodeIds[1]
			const isCurTop = rootNodeIds.includes(node.entityId) && exceptNode
			const linsShape = node.detailList.length === 1 ? 1 : 4

			graphData.lines.push(
				isUpper
					? {
						from: `${node.entityId}`,
						to: parentId,
						text: node.percent,
						// textOffset_x: isCurTop ? 60 : 0,
						// textOffset_y: -3,
						// linsShape
					}
					: {
						from: parentId,
						to: `${node.entityId}`,
						text: node.percent,
						// textOffset_x: isCurTop ? 25 : 60,
						// textOffset_y: -3,
						// linsShape,
						fromJunctionPoint: isCurTop ? 'tb' : 'lr',
						toJunctionPoint: isCurTop ? 'tb' : 'lr'
					}
			)
		}

		if (node.detailList && node.detailList.length > 0) {
			node.detailList.forEach(child => {
				buildNodeAndChildren(child, `${node.entityId}`, isUpper, graphData, rootNodeIds, DISCORD_NODES, rootNodeLevelMap, rootData)
			})
		}
	}

	const handleNodeExpand = async (node, graphInstanceRef, rootNodeIds, DISCORD_NODES, ROOT_NODE_LEVEL_MAP, cacheUtils = {},formModel) => {
		console.log('🔄 开始展开节点:', node.data?.entityId)
		console.log('🔄 lot---node:', node.lot)
// 2. 获取图表实例
		const graphInstance = graphInstanceRef.value?.getInstance()

		if (rootNodeIds[0] === formModel.entityId) {
			await updateLineStyles(graphInstance, rootNodeIds, node)
		}

		//兼容非集团节点展开操作
		if (rootNodeIds[0] !== formModel.entityId){
			graphInstance.getNodes().forEach(no => {
				if (Math.abs(no.lot.level) >= Math.abs(node.lot.level) + 1) {
					no.expanded = false
				}
			})
			// await updateNodeStyles(graphInstance, rootNodeIds)
		}


		// 1. 前置条件检查
		if (!validateExpandConditions(node, formModel)) {
			await graphInstance.doLayout()
			return false
		}

		if (!graphInstance) {
			console.error('❌ 图表实例不存在，无法展开节点')
			return false
		}

		try {
			// 4. 开始加载状态
			await graphInstance.loading(true)
			// 3. 检查缓存
			const cachedData = await checkNodeExpandCache(node, cacheUtils)

			let graphData
			if (cachedData) {
				console.log('✅ 使用缓存数据展开节点')
				graphData = cachedData
			} else {
				console.log('🔨 构建新的子节点数据')
				// 5. 构建子节点数据
				graphData = await buildChildrenGraphData(node, rootNodeIds, DISCORD_NODES, ROOT_NODE_LEVEL_MAP)

				// 6. 缓存新构建的数据
				await cacheNodeExpandData(node, graphData, cacheUtils)
			}

			// 7. 标记节点已加载，防止重复展开
			node.data.childrenLoaded = true

			// 8. 追加数据到图表
			if (graphData.nodes.length > 0 || graphData.lines.length > 0) {
				await graphInstance.appendJsonData(graphData)
				console.log(`✅ 成功展开节点，新增 ${graphData.nodes.length} 个节点，${graphData.lines.length} 条连线`)
			} else {
				console.log('ℹ️ 节点无子数据，展开完成')
			}

			// 9. 更新节点样式
			await updateNodeStyles(graphInstance, rootNodeIds)

			//更新线条显示
			await updateLineStyles(graphInstance, rootNodeIds, node)

			// 10. 调整视图位置
			await graphInstance.doLayout()

			return true

		} catch (error) {
			console.error('❌ 节点展开失败:', error)
			// 回滚状态
			node.data.childrenLoaded = false
			return false
		} finally {
			// 11. 清除加载状态
			graphInstance.clearLoading()
		}
	}
	const handleNodeCollapse = async (node, graphInstanceRef, rootNodeIds) => {
		const graphInstance = graphInstanceRef.value?.getInstance()
		const expandNodeArray = node.lot.parent.targetNodes.filter(no => no.expanded)
		if (graphInstance) {
			const allLinks = graphInstance.getLinks() || []
			if (expandNodeArray.length === 0) {
				allLinks.filter(lk => rootNodeIds.includes(lk.fromNode.id)).forEach(link => {
					link.relations.forEach(line => {
						line.isHide = false
					})
				})
			} else if (expandNodeArray.length === 1) {
				const expandNode = expandNodeArray[0];
				if (expandNode.id === rootNodeIds[0]) {
					allLinks.filter(lk => lk.fromNode.id === rootNodeIds[1]).forEach(link => {
						link.relations.forEach(line => {
							if (line.to === rootNodeIds[2]) {
								line.isHide = true
							}
						})
					})
				} else if (expandNode.id === rootNodeIds[2]) {
					allLinks.filter(lk => lk.toNode.id === rootNodeIds[1]).forEach(link => {
						link.relations.forEach(line => {
							if (line.to === rootNodeIds[1]) {
								line.isHide = true
							}
						})
					})
				}
			}
			await graphInstance.dataUpdated()
		}
	}

	const validateExpandConditions = (node) => {
		// 检查是否需要从远程服务器加载数据
		if (!node.data?.isNeedLoadDataFromRemoteServer) {
			console.log('ℹ️ 节点不需要从远程加载数据，跳过展开')
			return false
		}

		// 检查是否已经加载过子节点
		if (node.data?.childrenLoaded) {
			console.log('ℹ️ 节点子数据已加载，跳过展开')
			return false
		}

		// 检查节点数据完整性
		if (!node.data?.entityId) {
			console.warn('⚠️ 节点缺少entityId，无法展开')
			return false
		}

		return true
	}

	const buildChildrenGraphData = async (node, rootNodeIds, DISCORD_NODES, ROOT_NODE_LEVEL_MAP) => {
		const graphData = {
			nodes: [],
			lines: []
		}

		// 检查是否有子数据
		const childrenData = node.data.childrenData || []
		if (childrenData.length === 0) {
			console.log('ℹ️ 节点无子数据')
			return graphData
		}

		// 批量构建子节点
		childrenData.forEach((childData, index) => {
			try {
				buildNodeAndChildren(
					childData,
					`${node.data.entityId}`,
					false,
					graphData,
					rootNodeIds,
					DISCORD_NODES,
					ROOT_NODE_LEVEL_MAP,
					node.data
				)
			} catch (error) {
				console.error(`❌ 构建子节点 ${index + 1} 失败:`, error)
			}
		})

		return graphData
	}

	const updateNodeStyles = async (graphInstance, rootNodeIds) => {
		console.log('🎨 更新节点样式...')

		const nodes = graphInstance.getNodes()
		let updatedCount = 0

		nodes.forEach(node => {
			const oldClassName = node.className

			if (rootNodeIds.includes(node.data.entityId)) {
				// 根节点样式
				node.className = `${node.data.hasBusiness} my-industy-node-level-${node.data.fillingStandards}`
			} else {
				// 普通节点样式
				if (isNil(node.data.hasBusiness) || isNil(node.data.fillingStandards) || node.data.fillingStandards.length === 0) {
					node.className = `K my-industy-node-level-default-0`
				} else {
					node.className = `${node.data.hasBusiness} my-industy-node-level-${node.data.fillingStandards}`
				}
			}

			if (oldClassName !== node.className) {
				updatedCount++
			}
		})

		// 触发样式更新
		await nextTick()
	}

	const updateLineStyles = async (graphInstance, rootNodeIds, node) => {
		const expandNodeArray = node.lot.parent.targetNodes.filter(no => no.expanded)
		const allLinks = graphInstance.getLinks() || []
		if (expandNodeArray.length === 1) {
			const expandNode = expandNodeArray[0];
			if (expandNode.id === rootNodeIds[0]) {
				allLinks.filter(lk => lk.fromNode.id === rootNodeIds[1]).forEach(link => {
					link.relations.forEach(line => {
						if (line.to === rootNodeIds[2]) {
							line.isHide = true
						}
					})
				})
			} else if (expandNode.id === rootNodeIds[2]) {
				allLinks.filter(lk => lk.toNode.id === rootNodeIds[1]).forEach(link => {
					link.relations.forEach(line => {
						if (line.to === rootNodeIds[1]) {
							line.isHide = true
						}
					})
				})
			}
		} else {
			allLinks.filter(lk => rootNodeIds.includes(lk.fromNode.id)).forEach(link => {
				link.relations.forEach(line => {
					line.isHide = false
				})
			})
		}
		await graphInstance.dataUpdated()
	}

	const checkNodeExpandCache = async (node, cacheUtils) => {
		if (!cacheUtils.getFromCache || !node.data?.entityId) {
			return null
		}

		try {
			// 生成节点展开的缓存键
			const cacheKey = `node_expand_${node.data.entityId}`
			console.log('🔍 检查节点展开缓存:', cacheKey)

			const cachedData = cacheUtils.getFromCache(cacheKey)
			if (cachedData) {
				console.log('✅ 找到节点展开缓存数据')
				return cachedData
			} else {
				console.log('❌ 未找到节点展开缓存数据')
				return null
			}
		} catch (error) {
			console.warn('检查节点展开缓存失败:', error)
			return null
		}
	}

	const cacheNodeExpandData = async (node, graphData, cacheUtils) => {
		if (!cacheUtils.setCache || !node.data?.entityId) {
			return
		}

		try {
			// 生成节点展开的缓存键
			const cacheKey = `node_expand_${node.data.entityId}`

			// 缓存数据，包含统计信息
			const cacheData = {
				...graphData,
				nodeId: node.data.entityId,
				timestamp: Date.now(),
				stats: {
					nodeCount: graphData.nodes?.length || 0,
					lineCount: graphData.lines?.length || 0
				}
			}

			console.log('💾 缓存节点展开数据:', {
				cacheKey,
				nodeCount: cacheData.stats.nodeCount,
				lineCount: cacheData.stats.lineCount
			})

			cacheUtils.setCache(cacheKey, cacheData)
		} catch (error) {
			console.warn('缓存节点展开数据失败:', error)
		}
	}



	return {
		rootLoading,
		showContextMenu,
		showNodeTooltipPanel,
		showRootRelationPanel,
		selectedNode,
		pageRef,
		graphWrapRef,
		nodeMenuPanelPos,
		nodeTooltipPos,
		handleNodeContextMenu,
		handleNodeClick,
		exec,
		handleNodeAction,
		handleNodeTooltip,
		showRootNodeRelation,
		handleNodeExpand,
		handleNodeCollapse,
		closeAllPanels,
		// 新增的缓存相关函数
		checkNodeExpandCache,
		cacheNodeExpandData,
		validateExpandConditions,
		buildChildrenGraphData,
		updateNodeStyles
	}
}

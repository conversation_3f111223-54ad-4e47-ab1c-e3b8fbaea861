import { ref, reactive } from 'vue'
import { isNil } from 'lodash-es'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import bizInstitutionApi from '@/api/biz/bizInstitutionApi'

export function useFormData() {
	// 表单数据
	const formModel = ref({
		type: false,
		isCurrentVersion: '1',
		versionId: '00000000000000000000',
	})

	// 版本信息
	const currentVersionLabel = ref('')
	const currentVersionValue = ref()

	// 组织树数据
	const orgTreeData = ref([])
	const treeProps = reactive({
		keys: 'id',
		title: 'simpOrgName',
		loading: false,
		selectLeastOne: true,
		expanded: [], // 默认展开
	})

	// 处理树数据的可编辑状态
	const disposeTree = async (tree) => {
		for (let i = 0; i < tree.length; i++) {
			tree[i].disabled = tree[i]?.isClickable == 1 ? false : true
			if (tree[i]?.children) {
				await disposeTree(tree[i]?.children)
			}
		}
	}

	// 禁用日期函数
	const disabledDate = (current) => {
		const nextMonthDate = dayjs(currentVersionValue.value)
		return current && current > nextMonthDate
	}

	// 处理日期变化
	const handleDateChange = (date, yearMonth, fetchGraphDataFn) => {
		if (date) {
			const [year, month] = date.split('-')
			formModel.value.year = year
			formModel.value.month = month
			delete formModel.value.isCurrentVersion
		} else {
			delete formModel.value?.year
			delete formModel.value?.month
			formModel.value.isCurrentVersion = '1'
		}

		if (isNil(formModel.value?.entityId)) {
			return message.warning('请选择机构')
		}

		if (fetchGraphDataFn) {
			fetchGraphDataFn()
		}
	}

	// 查询当前版本
	const handleQueryCurrentVersion = (fetchGraphDataFn) => {
		delete formModel.value.yearMonth
		formModel.value.isCurrentVersion = '1'

		if (isNil(formModel.value?.entityId)) {
			return message.warning('请选择机构')
		}

		if (fetchGraphDataFn) {
			fetchGraphDataFn()
		}
	}

	// 处理变化事件
	const onChange = (value, fetchGraphDataFn) => {
		console.log('Form change:', value)

		// 如果是机构切换（entityId变化），重置type为false
		if (value && typeof value === 'string' && value !== formModel.value.entityId) {
			console.log('机构切换，重置type为false')
			formModel.value.type = false
		}

		if (fetchGraphDataFn) {
			fetchGraphDataFn()
		}
	}

	// 获取组织树数据
	const fetchOrgTreeData = async () => {
		try {
			const res = await bizInstitutionApi.getCurVersionDate()
			currentVersionLabel.value = res ? `${res?.year}年${res?.period}月` : ''
			currentVersionValue.value = res ? `${res?.year}-${res?.period}` : ''

			// 处理树是否可编辑
			treeProps.loading = true
			const treeData = await bizInstitutionApi.getStructureTree({
				structureType: '0',
				isWhereFilling: '1'
			})

			if (treeData?.length) {
				await disposeTree(treeData)
				orgTreeData.value = treeData
				treeProps.expanded.push(treeData[0]?.id)
			} else {
				orgTreeData.value = []
			}
		} catch (error) {
			console.error('获取组织树数据失败:', error)
			orgTreeData.value = []
		} finally {
			treeProps.loading = false
		}
	}

	return {
		formModel,
		currentVersionLabel,
		currentVersionValue,
		orgTreeData,
		treeProps,
		disabledDate,
		handleDateChange,
		handleQueryCurrentVersion,
		onChange,
		fetchOrgTreeData
	}
}

import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 性能监控组合函数
 * 复用自 managementStructure，针对 shareholdingStructure 进行优化
 */
export function usePerformanceMonitor() {
  const performanceData = ref({
    renderTime: 0,
    apiCallTimes: [],
    memoryUsage: 0,
    componentMountTime: 0,
    graphRenderTime: 0,
    dataProcessTime: 0
  })

  let mountStartTime = 0
  let observers = []

  /**
   * 测量函数执行时间
   */
  const measureTime = (fn, label = 'operation') => {
    return async (...args) => {
      const startTime = performance.now()
      
      try {
        const result = await fn(...args)
        const endTime = performance.now()
        const duration = endTime - startTime
        
        // 记录不同类型的性能数据
        if (label.includes('api') || label.includes('fetch')) {
          performanceData.value.apiCallTimes.push({
            label,
            duration,
            timestamp: new Date()
          })
        } else if (label.includes('graph') || label.includes('render')) {
          performanceData.value.graphRenderTime = duration
        } else if (label.includes('data') || label.includes('process')) {
          performanceData.value.dataProcessTime = duration
        }
        
        console.log(`${label} 执行时间: ${duration.toFixed(2)}ms`)
        return result
      } catch (error) {
        const endTime = performance.now()
        const duration = endTime - startTime
        console.error(`${label} 执行失败，耗时: ${duration.toFixed(2)}ms`, error)
        throw error
      }
    }
  }

  /**
   * 监控内存使用情况
   */
  const monitorMemory = () => {
    if ('memory' in performance) {
      const memory = performance.memory
      performanceData.value.memoryUsage = {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
      }
    }
  }

  /**
   * 监控DOM变化
   */
  const observeDOM = (target) => {
    if (!target) return

    const observer = new MutationObserver((mutations) => {
      const startTime = performance.now()
      
      // 处理DOM变化
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 记录DOM节点变化
        }
      })
      
      const endTime = performance.now()
      console.log(`DOM变化处理时间: ${(endTime - startTime).toFixed(2)}ms`)
    })

    observer.observe(target, {
      childList: true,
      subtree: true,
      attributes: true
    })

    observers.push(observer)
    return observer
  }

  /**
   * 监控长任务
   */
  const observeLongTasks = () => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) { // 超过50ms的任务
            console.warn(`长任务检测: ${entry.name}, 耗时: ${entry.duration.toFixed(2)}ms`)
          }
        })
      })

      try {
        observer.observe({ entryTypes: ['longtask'] })
        observers.push(observer)
      } catch (e) {
        console.log('浏览器不支持长任务监控')
      }
    }
  }

  /**
   * 获取页面加载性能指标
   */
  const getPagePerformance = () => {
    if ('getEntriesByType' in performance) {
      const navigation = performance.getEntriesByType('navigation')[0]
      if (navigation) {
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: getFirstPaint(),
          firstContentfulPaint: getFirstContentfulPaint()
        }
      }
    }
    return null
  }

  /**
   * 获取首次绘制时间
   */
  const getFirstPaint = () => {
    const paintEntries = performance.getEntriesByType('paint')
    const fpEntry = paintEntries.find(entry => entry.name === 'first-paint')
    return fpEntry ? fpEntry.startTime : 0
  }

  /**
   * 获取首次内容绘制时间
   */
  const getFirstContentfulPaint = () => {
    const paintEntries = performance.getEntriesByType('paint')
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcpEntry ? fcpEntry.startTime : 0
  }

  /**
   * 生成性能报告
   */
  const generateReport = () => {
    const report = {
      timestamp: new Date(),
      componentMountTime: performanceData.value.componentMountTime,
      memoryUsage: performanceData.value.memoryUsage,
      apiCalls: performanceData.value.apiCallTimes.slice(-10), // 最近10次API调用
      graphRenderTime: performanceData.value.graphRenderTime,
      dataProcessTime: performanceData.value.dataProcessTime,
      pagePerformance: getPagePerformance(),
      recommendations: []
    }

    // 生成优化建议
    if (report.componentMountTime > 1000) {
      report.recommendations.push('组件挂载时间过长，考虑优化初始化逻辑')
    }

    if (report.memoryUsage && report.memoryUsage.used > 100) {
      report.recommendations.push('内存使用量较高，检查是否存在内存泄漏')
    }

    if (report.graphRenderTime > 2000) {
      report.recommendations.push('图表渲染时间过长，建议启用性能优化模式')
    }

    if (report.dataProcessTime > 1000) {
      report.recommendations.push('数据处理时间过长，建议启用分块处理')
    }

    const slowApiCalls = report.apiCalls.filter(call => call.duration > 2000)
    if (slowApiCalls.length > 0) {
      report.recommendations.push('存在响应时间超过2秒的API调用，考虑优化或添加缓存')
    }

    return report
  }

  /**
   * 清理监控器
   */
  const cleanup = () => {
    observers.forEach(observer => {
      if (observer.disconnect) {
        observer.disconnect()
      }
    })
    observers = []
  }

  // 生命周期钩子
  onMounted(() => {
    mountStartTime = performance.now()
    
    // 启动性能监控
    observeLongTasks()
    
    // 定期监控内存
    const memoryInterval = setInterval(monitorMemory, 30000) // 每30秒检查一次
    
    // 组件挂载完成时间
    setTimeout(() => {
      performanceData.value.componentMountTime = performance.now() - mountStartTime
    }, 0)

    // 清理定时器
    onUnmounted(() => {
      clearInterval(memoryInterval)
      cleanup()
    })
  })

  return {
    performanceData,
    measureTime,
    monitorMemory,
    observeDOM,
    generateReport,
    cleanup
  }
}

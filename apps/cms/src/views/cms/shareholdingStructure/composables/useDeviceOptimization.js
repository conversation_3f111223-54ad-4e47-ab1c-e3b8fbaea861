import { ref, computed, onMounted } from 'vue'

/**
 * 设备性能检测和优化配置组合函数
 * 复用自 managementStructure，针对 shareholdingStructure 进行优化
 */
export function useDeviceOptimization() {
  const deviceInfo = ref({
    hardwareConcurrency: navigator.hardwareConcurrency || 1,
    memory: null,
    connection: null,
    isLowEnd: false,
    performanceScore: 0
  })

  const optimizationConfig = ref({
    // 默认配置
    enableAnimation: true,
    renderQuality: 'high',
    cacheSize: 50, // 优化：降低默认缓存大小
    debounceDelay: 100,
    chunkSize: 50, // 优化：降低默认块大小
    enableVirtualScroll: false,
    enableWebWorker: false,
    maxNodes: 500, // 优化：降低默认最大节点数
    // 针对关系图的特殊配置
    enableGraphOptimization: true,
    maxGraphNodes: 300, // 优化：降低默认最大图节点数
    graphRenderDelay: 100,
    // 新增：内存管理配置
    enableMemoryOptimization: true,
    memoryThreshold: 0.8, // 内存使用率阈值
    enableDataCompression: false // 数据压缩（暂时关闭）
  })

  /**
   * 检测设备性能
   */
  const detectDevicePerformance = () => {
    let score = 0

    // CPU 核心数评分 (0-30分)
    const cores = deviceInfo.value.hardwareConcurrency
    if (cores >= 8) score += 30
    else if (cores >= 4) score += 20
    else if (cores >= 2) score += 10
    else score += 5

    // 内存评分 (0-40分)
    if (performance.memory) {
      deviceInfo.value.memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }

      const memoryLimitMB = performance.memory.jsHeapSizeLimit / (1024 * 1024)
      if (memoryLimitMB >= 4096) score += 40
      else if (memoryLimitMB >= 2048) score += 30
      else if (memoryLimitMB >= 1024) score += 20
      else if (memoryLimitMB >= 512) score += 10
      else score += 5
    } else {
      score += 20 // 默认中等分数
    }

    // 网络连接评分 (0-20分)
    if (navigator.connection) {
      deviceInfo.value.connection = {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      }

      const effectiveType = navigator.connection.effectiveType
      if (effectiveType === '4g') score += 20
      else if (effectiveType === '3g') score += 15
      else if (effectiveType === '2g') score += 5
      else score += 10
    } else {
      score += 15 // 默认分数
    }

    // 用户代理评分 (0-10分)
    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.includes('mobile') || userAgent.includes('android')) {
      score += 5 // 移动设备通常性能较低
    } else {
      score += 10
    }

    deviceInfo.value.performanceScore = score
    deviceInfo.value.isLowEnd = score < 50

    return score
  }

  /**
   * 根据设备性能生成优化配置
   */
  const generateOptimizationConfig = () => {
    const score = deviceInfo.value.performanceScore

    if (score < 30) {
      // 极低端设备 - 激进优化
      optimizationConfig.value = {
        enableAnimation: false,
        renderQuality: 'low',
        cacheSize: 20, // 进一步降低缓存大小
        debounceDelay: 500,
        chunkSize: 10, // 更小的块大小
        enableVirtualScroll: true,
        enableWebWorker: false,
        maxNodes: 50, // 大幅降低节点数
        enableGraphOptimization: true,
        maxGraphNodes: 30, // 大幅降低图节点数
        graphRenderDelay: 300,
        enableMemoryOptimization: true,
        memoryThreshold: 0.7, // 更严格的内存阈值
        enableDataCompression: true
      }
    } else if (score < 50) {
      // 低端设备 - 强化优化
      optimizationConfig.value = {
        enableAnimation: false,
        renderQuality: 'medium',
        cacheSize: 30, // 降低缓存大小
        debounceDelay: 300,
        chunkSize: 25, // 降低块大小
        enableVirtualScroll: true,
        enableWebWorker: true,
        maxNodes: 150, // 降低节点数
        enableGraphOptimization: true,
        maxGraphNodes: 100, // 降低图节点数
        graphRenderDelay: 200,
        enableMemoryOptimization: true,
        memoryThreshold: 0.75,
        enableDataCompression: false
      }
    } else if (score < 70) {
      // 中端设备
      optimizationConfig.value = {
        enableAnimation: true,
        renderQuality: 'medium',
        cacheSize: 500,
        debounceDelay: 200,
        chunkSize: 100,
        enableVirtualScroll: false,
        enableWebWorker: true,
        maxNodes: 500,
        enableGraphOptimization: true,
        maxGraphNodes: 350,
        graphRenderDelay: 150
      }
    } else {
      // 高端设备
      optimizationConfig.value = {
        enableAnimation: true,
        renderQuality: 'high',
        cacheSize: 1000,
        debounceDelay: 100,
        chunkSize: 200,
        enableVirtualScroll: false,
        enableWebWorker: true,
        maxNodes: 1000,
        enableGraphOptimization: false,
        maxGraphNodes: 500,
        graphRenderDelay: 100
      }
    }
  }

  /**
   * 获取关系图配置
   */
  const getGraphConfig = computed(() => {
    const config = optimizationConfig.value

    return {
      // 基础性能配置
      performanceMode: config.enableGraphOptimization,
      disableLineClickEffect: true,
      // 渲染质量配置
      defaultNodeBorderWidth: 0,
      defaultLineColor: config.renderQuality === 'low' ? '#999' : '#333',

      // 布局配置
      layout: {
        layoutName: 'tree',
        from: 'left',
				// fixedRootNode: true,
        min_per_height: 60,
        min_per_width: 250,
        levelDistance: '500,500,500,500,500,500'
      }
    }
  })

  /**
   * 监控内存使用
   */
  const monitorMemoryUsage = () => {
    if (!performance.memory) return null

    const current = {
      used: Math.round(performance.memory.usedJSHeapSize / (1024 * 1024)),
      total: Math.round(performance.memory.totalJSHeapSize / (1024 * 1024)),
      limit: Math.round(performance.memory.jsHeapSizeLimit / (1024 * 1024))
    }

    // 内存使用率超过80%时发出警告
    const usageRate = current.used / current.limit
    if (usageRate > 0.8) {
      console.warn(`内存使用率过高: ${(usageRate * 100).toFixed(1)}%`)
      return { ...current, warning: true }
    }

    return { ...current, warning: false }
  }

  /**
   * 获取性能建议
   */
  const getPerformanceRecommendations = computed(() => {
    const recommendations = []
    const score = deviceInfo.value.performanceScore

    if (score < 30) {
      recommendations.push('建议关闭动画效果以提升性能')
      recommendations.push('建议限制显示的节点数量')
      recommendations.push('建议启用图表优化模式')
    } else if (score < 50) {
      recommendations.push('建议减少缓存大小')
      recommendations.push('建议增加防抖延迟')
      recommendations.push('建议启用分块处理')
    } else if (score < 70) {
      recommendations.push('当前设备性能中等，可适当优化')
    } else {
      recommendations.push('设备性能良好，可使用完整功能')
    }

    return recommendations
  })

  /**
   * 初始化设备检测
   */
  onMounted(() => {
    detectDevicePerformance()
    generateOptimizationConfig()

    console.log('设备性能评分:', deviceInfo.value.performanceScore)
    console.log('优化配置:', optimizationConfig.value)
    console.log('性能建议:', getPerformanceRecommendations.value)
  })

  return {
    deviceInfo: computed(() => deviceInfo.value),
    optimizationConfig: computed(() => optimizationConfig.value),
    getGraphConfig,
    getPerformanceRecommendations,
    detectDevicePerformance,
    generateOptimizationConfig,
    monitorMemoryUsage
  }
}

import { ref } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 错误处理组合函数
 * 复用自 managementStructure，针对 shareholdingStructure 进行优化
 */
export function useErrorHandler() {
  const errors = ref([])
  const isRetrying = ref(false)

  /**
   * 错误包装器 - 统一处理异步函数的错误
   */
  const withErrorHandling = (fn, options = {}) => {
    const {
      showMessage = true,
      retryCount = 0,
      retryDelay = 1000,
      fallback = null,
      context = '操作'
    } = options

    return async (...args) => {
      let lastError = null
      
      for (let attempt = 0; attempt <= retryCount; attempt++) {
        try {
          if (attempt > 0) {
            isRetrying.value = true
            console.log(`${context} 重试第 ${attempt} 次...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
          }
          
          const result = await fn(...args)
          isRetrying.value = false
          return result
        } catch (error) {
          lastError = error
          console.error(`${context} 失败 (尝试 ${attempt + 1}/${retryCount + 1}):`, error)
          
          // 记录错误
          errors.value.push({
            error,
            context,
            timestamp: new Date(),
            attempt: attempt + 1
          })
          
          // 如果是最后一次尝试，处理错误
          if (attempt === retryCount) {
            isRetrying.value = false
            
            if (showMessage) {
              handleError(error, context)
            }
            
            // 如果有回退函数，执行回退
            if (fallback && typeof fallback === 'function') {
              try {
                return await fallback(error, ...args)
              } catch (fallbackError) {
                console.error('回退函数执行失败:', fallbackError)
              }
            }
            
            throw error
          }
        }
      }
    }
  }

  /**
   * 处理不同类型的错误
   */
  const handleError = (error, context = '操作') => {
    let errorMessage = `${context}失败`
    
    if (error.response) {
      // HTTP 错误
      const status = error.response.status
      const data = error.response.data
      
      switch (status) {
        case 400:
          errorMessage = data?.message || '请求参数错误'
          break
        case 401:
          errorMessage = '登录已过期，请重新登录'
          break
        case 403:
          errorMessage = '没有权限执行此操作'
          break
        case 404:
          errorMessage = '请求的资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        case 502:
        case 503:
        case 504:
          errorMessage = '服务暂时不可用，请稍后重试'
          break
        default:
          errorMessage = data?.message || `${context}失败 (${status})`
      }
    } else if (error.code === 'NETWORK_ERROR') {
      errorMessage = '网络连接失败，请检查网络设置'
    } else if (error.name === 'TimeoutError') {
      errorMessage = '请求超时，请稍后重试'
    } else if (error.message) {
      errorMessage = error.message
    }
    
    message.error(errorMessage)
    console.error('错误详情:', error)
  }

  /**
   * 网络错误处理
   */
  const handleNetworkError = (error) => {
    if (!navigator.onLine) {
      message.error('网络连接已断开，请检查网络设置')
      return
    }
    
    handleError(error, '网络请求')
  }

  /**
   * 数据处理错误
   */
  const handleDataError = (error, data = null) => {
    console.error('数据处理错误:', error)
    console.log('错误数据:', data)
    
    if (error.name === 'TypeError') {
      message.error('数据格式错误，请刷新页面重试')
    } else {
      message.error('数据处理失败，请稍后重试')
    }
  }

  /**
   * 图表渲染错误处理
   */
  const handleGraphError = (error, graphData = null) => {
    console.error('图表渲染错误:', error)
    console.log('图表数据:', graphData)
    
    if (error.message?.includes('memory') || error.message?.includes('heap')) {
      message.error('数据量过大，建议刷新页面或联系管理员')
    } else if (error.message?.includes('canvas') || error.message?.includes('render')) {
      message.error('图表渲染失败，请尝试刷新页面')
    } else {
      message.error('图表加载失败，请稍后重试')
    }
  }

  /**
   * 清理错误记录
   */
  const clearErrors = () => {
    errors.value = []
  }

  /**
   * 获取错误统计
   */
  const getErrorStats = () => {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    
    const recentErrors = errors.value.filter(item => item.timestamp > oneHourAgo)
    
    return {
      total: errors.value.length,
      recent: recentErrors.length,
      types: recentErrors.reduce((acc, item) => {
        const type = item.error.name || 'Unknown'
        acc[type] = (acc[type] || 0) + 1
        return acc
      }, {})
    }
  }

  /**
   * 创建带有错误处理的异步函数
   */
  const createSafeAsync = (fn, options = {}) => {
    return withErrorHandling(fn, {
      showMessage: true,
      retryCount: 1,
      retryDelay: 1000,
      ...options
    })
  }

  /**
   * 创建带有错误处理的API调用
   */
  const createSafeApiCall = (fn, options = {}) => {
    return withErrorHandling(fn, {
      showMessage: true,
      retryCount: 2,
      retryDelay: 1000,
      context: 'API调用',
      ...options
    })
  }

  return {
    errors,
    isRetrying,
    withErrorHandling,
    handleError,
    handleNetworkError,
    handleDataError,
    handleGraphError,
    clearErrors,
    getErrorStats,
    createSafeAsync,
    createSafeApiCall
  }
}

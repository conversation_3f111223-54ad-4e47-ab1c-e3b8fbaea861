<template>
	<div ref="pageRef" class="shareholding-structure-container">
		<!-- 全页面loading遮罩 -->
		<div v-if="loading" class="page-loading-overlay">
			<div class="loading-content">
				<div class="loading-spinner">
					<div class="spinner-ring"></div>
					<div class="spinner-ring"></div>
					<div class="spinner-ring"></div>
				</div>
				<div class="loading-text">{{ loadingText }}</div>
				<div class="loading-progress">
					<div class="progress-bar" :style="{ width: loadingProgress + '%' }"></div>
				</div>
			</div>
		</div>

		<!-- 控制面板区域 -->
		<section class="control-panel" :class="{ 'content-blurred': loading }">
			<QueryForm v-model:form-data="formModel" :current-version-label="currentVersionLabel" :org-tree-data="orgTreeData"
				:tree-props="treeProps" :disabled-date="disabledDate" @date-change="handleDateChange"
				@query-current-version="handleQueryCurrentVersion" @change="onChange"/>
		</section>

		<!-- 图表展示区域 -->
		<section class="graph-display-area" :class="{ 'content-blurred': loading }">
			<!-- 图表容器 -->
			<div
				ref="graphWrapRef"
				class="graph-container"
				:class="{ 'graph-transitioning': isTransitioning }"
			>
				<RelationGraph ref="graphInstanceRef" :options="graphOptions" @onContextmenu="handleNodeContextMenu"
					@onCanvasClick="closeAllPanels" :on-node-expand="handleNodeExpandWrapper" :on-node-collapse="handleNodeCollapseWrapper" :on-node-click="handleNodeClickWrapper">
					<template #graph-plug>
						<Legend
							:top-level-color="topLevelColors"
							:has-business="businessStatusColors"
							class="graph-legend"
							:class="{ 'legend-hidden': loading }"
						/>
					</template>
					<template #node="{ node }">
						<NodeRenderer :node="node" :is-top="isTopLevel" :root-node-ids="rootNodeIds" :form-model="formModel"
							:loading="loading" @show-root-relation="showRootNodeRelationWrapper" />
					</template>
				</RelationGraph>
			</div>
		</section>

		<!-- 交互组件区域 -->
		<section class="interaction-components">
			<!-- 右键菜单 -->
			<ContextMenu v-if="showContextMenu" :position="nodeMenuPanelPos" :selected-node="selectedNode" :rootNodeIds="rootNodeIds"
				@execute="execWrapper" />

			<!-- 节点详情面板 -->
			<NodeTooltipPanel v-if="showNodeTooltipPanel" :position="nodeTooltipPos" :selected-node="selectedNode"
				:listed-columns="listedVoListColumns" @close="showNodeTooltipPanel = false" />

			<!-- 根节点关系图 -->
			<RootRelationModal v-model:visible="showRootRelationPanel" :loading="rootLoading"
				:graph-options="graphRootOptions" ref="graphInstanceRootRef" />
		</section>
	</div>
</template>

<script setup>
	import { computed, onMounted, onUnmounted, nextTick, defineAsyncComponent } from 'vue'
	import RelationGraph from 'relation-graph-vue3'
	import { message } from 'ant-design-vue'
	import tool from '@/utils/tool'

	// 导入组件 - 使用懒加载优化性能
	import Legend from './components/legend.vue'
	const QueryForm = defineAsyncComponent(() => import('./components/QueryForm.vue'))
	const NodeRenderer = defineAsyncComponent(() => import('./components/NodeRenderer.vue'))
	const ContextMenu = defineAsyncComponent(() => import('./components/ContextMenu.vue'))
	const NodeTooltipPanel = defineAsyncComponent(() => import('./components/NodeTooltipPanel.vue'))
	const RootRelationModal = defineAsyncComponent(() => import('./components/RootRelationModal.vue'))

	// 导入组合式函数
	import { useFormData } from './composables/useFormData'
	import { useGraphData } from './composables/useGraphData'
	import { useNodeActions } from './composables/useNodeActions'
	import { useDeviceOptimization } from './composables/useDeviceOptimization'
	// import { usePerformanceMonitor } from './composables/usePerformanceMonitor'
	import { useErrorHandler } from './composables/useErrorHandler'
	import { useRoute } from 'vue-router'
  const route = useRoute()
	// 常量定义
	const LISTED_COLUMNS = [
		{
			title: '序号',
			dataIndex: 'index',
			customRender: ({ index }) => `${index + 1}`
		},
		{
			title: '市场',
			dataIndex: 'market',
			customRender: ({ text }) => tool.dictTypeData('market_listed', text)
		},
		{
			title: '股票简称',
			dataIndex: 'stock',
		},
		{
			title: '股票代码',
			dataIndex: 'stockCode',
		}
	]

	const TOP_LEVEL_COLORS = {
		'中信集团': '#D20000',
		'中信股份': '#D45100',
		'中信有限': '#485563',
	}

	const BUSINESS_STATUS_COLORS = {
		'root': {
			"20140824164901061871": '#D20000',
			"20140822152314001513": '#D45100',
			"20140824164901061873": '#485563'
		},
		'Y': {
			'并表': '#ddedf8',
			'参股': '#e1f0da',
			'实质管理': '#ffeadf',
		},
		'N': {
			'并表': '#7f7f7f',
			'参股': '#a6a6a6',
			'实质管理': '#bfbfbf',
		}
	}
	// ==================== 使用组合函数 ====================
	// 设备优化和性能监控
	const {
		deviceInfo,
		optimizationConfig,
		getGraphConfig,
		getPerformanceRecommendations,
		monitorMemoryUsage
	} = useDeviceOptimization()

	// const { measureTime, performanceData } = usePerformanceMonitor()
	const { withErrorHandling, createSafeAsync } = useErrorHandler()

	// 表单数据管理
	const {
		formModel,
		currentVersionLabel,
		currentVersionValue,
		orgTreeData,
		treeProps,
		disabledDate,
		handleDateChange: handleFormDateChange,
		handleQueryCurrentVersion: handleFormQueryCurrentVersion,
		onChange: handleFormChange,
		fetchOrgTreeData
	} = useFormData()

	// 图表数据管理
	const {
		loading,
		originData,
		rootPercent,
		graphInstanceRef,
		graphInstanceRootRef,
		rootNodeIds,
		DISCORD_NODES,
		ROOT_NODE_LEVEL_MAP,
		graphOptions,
		graphRootOptions,
		fetchGraphData: fetchGraphDataCore,
		debouncedFetchGraphData,
		getRootNodeData,
		openByLevel,
		openByLevelByNode,
		rootData,
		// 新增的优化功能
		isTransitioning,
		loadingText,
		loadingProgress,
		setLoadingState,
		updateLoadingProgress,
		finishLoading,
		clearCache,
		clearExpiredCache, // 清理过期缓存
		stopCacheCleanup, // 停止缓存清理定时器
		// 缓存工具函数
		getCacheKey,
		getFromCache,
		setCache,
		updateCacheIndex,
		processDataInChunks
	} = useGraphData()

	// 节点操作管理
	const {
		rootLoading,
		showContextMenu,
		showNodeTooltipPanel,
		showRootRelationPanel,
		selectedNode,
		pageRef,
		graphWrapRef,
		nodeMenuPanelPos,
		nodeTooltipPos,
		handleNodeContextMenu,
		handleNodeClick,
		handleNodeExpand,
		handleNodeCollapse,
		exec,
		handleNodeTooltip,
		showRootNodeRelation,
		closeAllPanels
	} = useNodeActions()

	// 重新导出常量以供模板使用
	const topLevelColors = TOP_LEVEL_COLORS
	const businessStatusColors = BUSINESS_STATUS_COLORS
	const listedVoListColumns = LISTED_COLUMNS

	// ==================== 包装函数 ====================
	// 使用防抖版本的数据获取函数，提升性能
	const fetchGraphData = createSafeAsync(() => debouncedFetchGraphData(formModel.value), {
		context: '获取图表数据',
		retryCount: 2
	})

	const handleDateChange = async (date, yearMonth) => {
		handleFormDateChange(date, yearMonth)
		await fetchGraphData()
	}

	const handleQueryCurrentVersion = async () => {
		handleFormQueryCurrentVersion()
		await fetchGraphData()
	}

	const onChange = async (value) => {
		handleFormChange(value)
		await fetchGraphData()
	}

	// 计算属性
	const isTopLevel = computed(() => {
		return !formModel.value.entityId || formModel.value?.entityId === rootNodeIds[0]
	})

	// 工具函数
	function findTopEntityId(node) {
		let current = node;
		while (current.detailList && current.detailList.length > 0) {
			current = current.detailList[0];
		}
		return current.entityId;
	}
	// 包装节点点击处理函数
	const handleNodeClickWrapper = (nodeObject) => {
		handleNodeClick(nodeObject, graphInstanceRef)
	}

	// 包装节点右展开处理函数
	const handleNodeExpandWrapper = (nodeObject) => {
		// 传入缓存工具函数
		const cacheUtils = {
			getFromCache,
			setCache,
			getCacheKey,
			updateCacheIndex
		}
		handleNodeExpand(nodeObject, graphInstanceRef, rootNodeIds, DISCORD_NODES, ROOT_NODE_LEVEL_MAP, cacheUtils, formModel.value)
	}

	// 包装节点右键收起
	const handleNodeCollapseWrapper = (nodeObject) => {
		handleNodeCollapse(nodeObject, graphInstanceRef, rootNodeIds)
	}

	// 包装执行命令函数
	const execWrapper = (command) => {
		exec(command, formModel.value, fetchGraphData, openByLevelByNode)
	}

	function generateOffsets(n, step = 50) {
		if (n === 1) return [0];
		const offsets = [];
		const mid = Math.floor(n / 2);
		for (let i = 0; i < n; i++) {
			offsets.push((i - mid) * step);
			// 如果是偶数，右边整体往右偏移 step/2
			if (n % 2 === 0) offsets[i] += step / 2;
		}
		return offsets;
	}

	const loadGraphRootData = async (data) => {
		rootLoading.value = true
		const __graph_json_data = {
			rootId: '20140824164901061871',
			nodes: [
				{ id: '20140824164901061871', text: '中信集团有限公司', data: { fillingStandards: 'group-0', hasBusiness: 'Y' } },
				{ id: '20140822152314001513', text: '中信股份有限公司', offset_x: 230, data: { fillingStandards: 'stock-0', hasBusiness: 'Y' } },
			],
			lines: []
		};
		const offsetArray = generateOffsets(data.length)

		data.forEach((item,idx) => {
			const topGroup = item.upperList[0]
			__graph_json_data.nodes.push({ id: item.entityId, text: item.name, data: { fillingStandards: item.fillingStandards.slice(-1), hasBusiness: item.hasBusiness ?? 'N' } })
			__graph_json_data.lines.push({ from: topGroup.entityId, to: item.entityId, text: topGroup.percent, textOffset_y: 50, textOffset_x: 20 })
			__graph_json_data.lines.push({ from: item.entityId, to: rootNodeIds[1], text: (item.shareRate * 100) + '%', toJuctionPointOffsetX: offsetArray[idx],textOffset_y: 50, textOffset_x: 20  })
		})
		const graphInstance = graphInstanceRootRef.value.getInstance();
		if (graphInstance){
			await graphInstance.setJsonData(__graph_json_data);
			console.log("=>(index.vue:716) __graph_json_data", __graph_json_data);
			graphInstance.getNodes().forEach(node => {
				if (isNaN(Math.abs(node.data.fillingStandards))) {
					node.className = `${node.data.hasBusiness} my-industy-node-level-` + node.data.fillingStandards;
				} else {
					node.className = `${node.data.hasBusiness} my-industy-node-level-` + Math.abs(node.data.fillingStandards);
				}
			});
			await graphInstance.refresh();
			await graphInstance.moveToCenter()
			rootLoading.value = false
		}
	}



	// 包装根节点关系显示函数
	const showRootNodeRelationWrapper = (e) => {
		showRootNodeRelation(e, loadGraphRootData, rootData())
	}

	// 处理系统缩放
	const handleSystemZoom = () => {
		// 检测是否为 macOS 系统
		const isMac = /Mac|iPhone|iPod|iPad/i.test(navigator.userAgent) ||
					  navigator.platform.toUpperCase().indexOf('MAC') >= 0;

		// 如果是 macOS 系统，则忽略缩放处理
		if (isMac) {
			return;
		}
		const devicePixelRatio = window.devicePixelRatio || 1;
		if (devicePixelRatio !== 1) {
			const cas = document.querySelector('.relation-graph canvas')
			if (cas) {
				// 为容器添加缩放补偿
				cas.style.zoom = `${1 / devicePixelRatio}`;
				cas.style.transformOrigin = '0 0';
				// container.style.width = `${100 * devicePixelRatio}%`;
				// container.style.height = `${100 * devicePixelRatio}%`;
			}
		}
	}

	// ==================== 生命周期钩子 ====================
	let cacheCleanupTimer = null

	// 组件挂载时初始化 - 性能优化版本
	onMounted(async () => {
		try {
			// 显示设备性能信息和建议
			console.log('设备性能评分:', deviceInfo.value.performanceScore)
			console.log('是否为低端设备:', deviceInfo.value.isLowEnd)
			console.log('优化配置:', optimizationConfig.value)

			const recommendations = getPerformanceRecommendations.value
			if (recommendations.length > 0) {
				console.log('性能优化建议:', recommendations)
				// 在低端设备上显示提示
				if (deviceInfo.value.isLowEnd) {
					message.info('检测到设备性能较低，已自动启用性能优化模式')
				}
			}

			// 分步骤初始化，避免阻塞
			await nextTick()

			// 根据设备性能调整加载策略
			const delay = deviceInfo.value.isLowEnd ? 300 : 100
			if (route.query.entityId) {
				loading.value = true
			}
			setTimeout(async () => {
				await fetchOrgTreeData()
				await getRootNodeData(formModel.value)
				if (route.query.entityId){
					formModel.value.entityId = route.query.entityId
					formModel.value.type = true
					if (route.query.year){
						formModel.value.yearMonth = route.query.year + '-' + route.query.month
					}
					await fetchGraphData()
				}
			}, delay)

			// 处理系统缩放
			handleSystemZoom()
			// 监听窗口变化
			window.addEventListener('resize', handleSystemZoom)

			// 设置定期清理过期缓存，根据设备性能调整频率
			const cleanupInterval = deviceInfo.value.isLowEnd ? 3 * 60 * 1000 : 5 * 60 * 1000
			cacheCleanupTimer = setInterval(() => {
				// 优化：只清理过期缓存，而不是全部清理
				clearExpiredCache()
				// 在低端设备上监控内存
				if (deviceInfo.value.isLowEnd) {
					const memoryInfo = monitorMemoryUsage()
					if (memoryInfo?.warning) {
						console.warn('内存使用率过高，建议刷新页面')
						// 内存不足时清理全部缓存
						clearCache()
					}
				}
			}, cleanupInterval)
		} catch (error) {
			console.error('组件初始化失败:', error)
		}
	})

	// 组件卸载时清理
	onUnmounted(() => {
		window.removeEventListener('resize', handleSystemZoom)
		// 停止缓存清理定时器
		stopCacheCleanup()
		// 可选：清理所有缓存（如果需要的话）
		clearCache()
	})

</script>

<style lang="scss" scoped>
	// ==================== 主容器样式 ====================
	.shareholding-structure-container {
		padding: 12px;
		position: relative;
		min-height: calc(100vh - 24px);
		overflow: hidden;

		// 当前页面loading遮罩样式
		.page-loading-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(6px);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1000;
			animation: fadeIn 0.3s ease-in-out;

			.loading-content {
				text-align: center;
				padding: 50px;
				background: white;
				border-radius: 16px;
				box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
				border: 1px solid rgba(255, 255, 255, 0.3);
				min-width: 300px;

				.loading-spinner {
					position: relative;
					width: 80px;
					height: 80px;
					margin: 0 auto 30px;

					.spinner-ring {
						position: absolute;
						width: 100%;
						height: 100%;
						border: 4px solid transparent;
						border-radius: 50%;
						animation: spin 1.5s linear infinite;

						&:nth-child(1) {
							border-top-color: #1890ff;
							animation-delay: 0s;
						}

						&:nth-child(2) {
							border-right-color: #52c41a;
							animation-delay: 0.5s;
						}

						&:nth-child(3) {
							border-bottom-color: #faad14;
							animation-delay: 1s;
						}
					}
				}

				.loading-text {
					font-size: 18px;
					color: #333;
					margin-bottom: 25px;
					font-weight: 500;
				}

				.loading-progress {
					width: 250px;
					height: 6px;
					background: #f0f0f0;
					border-radius: 3px;
					overflow: hidden;
					margin: 0 auto;

					.progress-bar {
						height: 100%;
						background: linear-gradient(90deg, #1890ff, #52c41a);
						border-radius: 3px;
						transition: width 0.3s ease;
						animation: progressShine 2s ease-in-out infinite;
					}
				}
			}
		}

		// 内容模糊效果
		.content-blurred {
			filter: blur(2px);
			pointer-events: none;
			transition: filter 0.3s ease;
		}

		.control-panel {
			margin-bottom: 16px;
			transition: filter 0.3s ease;
		}

		.graph-display-area {
			position: relative;
			transition: filter 0.3s ease;

			.graph-container {
				width: 100%;
				box-sizing: border-box;
				height: calc(100vh - 170px);
				position: relative;
				transition: all 0.3s ease;

				&.graph-transitioning {
					transform: scale(0.98);
					opacity: 0.8;
				}

				.graph-legend {
					position: absolute;
					top: 8px;
					right: 8px;
					z-index: 10;
					transition: opacity 0.3s ease;

					&.legend-hidden {
						opacity: 0;
					}
				}
			}
		}

		// ==================== 动画定义 ====================
		@keyframes fadeIn {
			from {
				opacity: 0;
				transform: translateY(-10px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}
			100% {
				transform: rotate(360deg);
			}
		}

		@keyframes progressShine {
			0%, 100% {
				background: linear-gradient(90deg, #1890ff, #52c41a);
			}
			50% {
				background: linear-gradient(90deg, #52c41a, #faad14);
			}
		}
	}

	// ==================== 节点样式 ====================
	.my-industy-node {
		width: 200px;
		border-radius: 5px;
		background-color: #ffffff;
		overflow: clip;
		transition: all 0.2s ease;

		.my-card-body {
			line-height: 30px;
		}

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}
	}

	:deep(.c-btn-open-close) {
		& span {
			background-color: #D20000 !important;
		}
	}

	:deep(.my-industy-node-level-group-0) {
		.my-industy-node {
			//border: #D20000 solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #D20000;
			}
			.my-card-body {
				color: #fff;
				background: #D20000;
			}
		}
	}

	:deep(.my-industy-node-level-stock-0) {
		.my-industy-node {
			//border: #D45100 solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #D45100;
			}
			.my-card-body {
				color: #fff;
				background: #D45100;
			}
		}
	}

	:deep(.my-industy-node-level-limit-0) {
		.my-industy-node {
			//border: #485563 solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #485563;
			}
			.my-card-body {
				color: #fff;
				background: #485563;
			}
		}
	}

	:deep(.my-industy-node-level-fixed-0) {
		.my-industy-node {
			border: #7f7f7f solid 1px;
			.my-card-body {
				color: #fff;
				background: #7f7f7f;
			}
		}
	}

	:deep(.my-industy-node-level-default-0) {
		.my-industy-node {
			border: #fccbcb solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #fccbcb;
			}
		}
	}

	:deep(.my-industy-node-level-1.Y) {
		.my-industy-node {
			border: #ddedf8 solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #ddedf8;
			}
		}
	}

	:deep(.my-industy-node-level-1.N) {
		.my-industy-node {
			border: #7f7f7f solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #333;
			}
			.my-card-body {
				color: #fff;
				background: #7f7f7f;
			}
		}
	}

	:deep(.my-industy-node-level-2.Y) {
		.my-industy-node {
			border: #e1f0da solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #e1f0da;
			}
		}
	}
	:deep(.my-industy-node-level-2.N) {
		.my-industy-node {
			border: #a6a6a6 solid 1px;
			.my-card-header {
				background-color: #fff;
				color: #333;
			}
			.my-card-body {
				color: #fff;
				background: #a6a6a6;
			}
		}
	}

	:deep(.my-industy-node-level-3.Y) {
		.my-industy-node {
			border: #ffeadf solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #ffeadf;
			}
		}
	}
	:deep(.my-industy-node-level-3.N) {
		.my-industy-node {
			border: #bfbfbf solid 1px;
			.my-card-header {
				color: #333;
				background-color: #fff;
			}
			.my-card-body {
				color: #fff;
				background: #bfbfbf;
			}
		}
	}

	//:deep(.my-industy-node-level-5.Y) {
	//	.my-industy-node {
	//		border: #fff7dc solid 1px;
	//		color: #333333;
	//		.my-card-header {
	//			background: #fff;
	//		}
	//		.my-card-body {
	//			background: #fff7dc;
	//		}
	//	}
	//}
	//:deep(.my-industy-node-level-5.N) {
	//	.my-industy-node {
	//		border: #d9d9d9 solid 1px;
	//		color: #999999;
	//		.my-card-header {
	//			background-color: #fff;
	//			color: #333;
	//		}
	//		.my-card-body {
	//			color: #fff;
	//			background: #d9d9d9;
	//		}
	//	}
	//}

	.context-menu {
		position: fixed;
		background-color: white;
		border: 1px solid #ccc;
		box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
		z-index: 1000;
		padding: 10px;
		border-radius: 4px;
	}

	.context-menu ul {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.context-menu li {
		padding: 8px 12px;
		cursor: pointer;
	}

	.context-menu li:hover {
		background-color: #f0f0f0;
	}
</style>

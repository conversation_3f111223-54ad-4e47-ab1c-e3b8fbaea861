<template>
	<div class="flex-1 p-[24px] bg-white rounded-lg overflow-hidden flex flex-col" ref="cardBoxRef">
		<a-space v-if="hasPerm(['batchLock', 'batchUnLock'])">
			<qt-button
				type="primary"
				v-if="hasPerm(['batchLock'])"
				@click="
					(next) => {
						lockBatchFn('1', next)
					}
				"
			>
				锁定
			</qt-button>
			<qt-button
				type="primary"
				v-if="hasPerm(['batchUnLock'])"
				@click="
					(next) => {
						lockBatchFn('2', next)
					}
				"
			>
				解锁
			</qt-button>
		</a-space>
		<div class="my-4">
			<a-space>
				<a-form ref="searchFormRef" name="advanced_search" class="ant-advanced-search-form" :model="searchFormState">
					<div class="flex justify-between items-center">
						<div class="flex gap-4">
							<a-form-item name="orgIds" label="单位" class="w-[400px]">
								<TreeSelect
									v-model:value="searchFormState.orgIds"
									:tree-data="treeData"
									:field-names="selectTreeOrgFieldNames"
									:show-checked-strategy="TreeSelect.SHOW_ALL"
									:maxTagCount="1"
									tree-checkable
									searchKey="simpOrgName"
									:loading="treeLoading"
									placeholder="请输入单位名称"
								/>
							</a-form-item>
							<a-form-item name="lockStatus" label="流程状态" class="w-[350px]">
								<Select
									class="w-full"
									mode="multiple"
									v-model:value="searchFormState.lockStatus"
									:options="optionsArr"
									placeholder="请选择流程状态"
								/>
							</a-form-item>
							<a-button
								shape="default"
								type="primary"
								class="snowy-button-left"
								@click="() => searchFormRef.resetFields()"
							>
								重置筛选
							</a-button>
							<a-dropdown>
								<template #overlay>
									<a-menu>
										<a-menu-item key="1" @click="selectAll"> 选择全部 </a-menu-item>
										<a-menu-item key="2" @click="selectedRowKeys = []"> 取消选择 </a-menu-item>
									</a-menu>
								</template>
								<a-button type="primary">
									选择
									<DownOutlined />
								</a-button>
							</a-dropdown>
						</div>
					</div>
				</a-form>
			</a-space>
		</div>
		<div class="flex-1" ref="tableboxRef">
			<a-table
				class="s-table"
				ref="tableRef"
				:scroll="{ y: height }"
				:pagination="{
					pageSize: pageSizeNumber,
					showSizeChanger: true,
					showQuickJumper: true,
					showTotal: (total) => `共 ${total} 条`
				}"
				@change="
					(pag) => {
						console.log(pag)
						pageSizeNumber = pag.pageSize
					}
				"
				:dataSource="tableData"
				:columns="columns"
				:row-key="(record) => record.entityId"
				:row-selection="options.rowSelection"
				:loading="tableLoading"
				size="middle"
				bordered
			>
				<template #bodyCell="{ column, record }">
					<template v-if="column.dataIndex === 'lockStatus'">
						{{ $TOOL.dictTypeData('lockStatus', record.lockStatus) }}
					</template>
					<template v-if="column.dataIndex === 'action'">
						<a-space style="gap: 0">
							<template #split>
								<a-divider type="vertical" />
							</template>
							<qt-button
								v-if="hasPerm('lock')"
								:disabled="record.lockStatus == 1"
								@click="(next) => lockFn(next, record)"
								type="link"
								size="small"
							>
								锁定
							</qt-button>
							<a-button
								v-if="hasPerm('unLock')"
								:disabled="record.lockStatus == 0"
								@click="flowFormRef.onOpen([record.entityId])"
								type="link"
								size="small"
							>
								解锁
							</a-button>
						</a-space>
					</template>
				</template>
			</a-table>
		</div>
	</div>
	<Flow ref="flowRef" />
	<FlowForm ref="flowFormRef" @successFul="successFul" />
</template>

<script setup lang="jsx">
	import { message, Modal, TreeSelect } from 'ant-design-vue'
	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	import bizEntityProcessApi from '@/api/biz/bizEntityProcessApi'

	import tool from '@/utils/tool'
	import Flow from './flow.vue'
	import FlowForm from './flowForm.vue'
	// 定义tableDOM
	const tableboxRef = ref()
	const flowRef = ref()
	const flowFormRef = ref()
	const tableRef = ref()
	const formRef = ref()
	const searchFormRef = ref()
	const searchFormState = ref({
		orgIds: [],
		lockStatus: []
	})
	const selectTreeOrgFieldNames = ref({
		children: 'children',
		label: 'simpOrgName',
		value: 'id'
	})
	const pageSizeNumber = ref(20)
	const optionsArr = ref([
		{ label: '未锁定', value: '0' },
		{ label: '已锁定', value: '1' }
	])
	const tableLoading = ref(false)
	const tableData = ref([])

	const columns = computed(() => {
		let arr = [
			{
				title: '序号',
				dataIndex: 'id',
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '机构唯一编码',
				dataIndex: 'entityCode',
				width: 180,
				customRender: ({ text, record, index }) => {
					return (
						<div class="cursor-pointer h-full text-[#D6000F]" onClick={() => flowRef.value.onOpen(record.entityId)}>
							{text}
						</div>
					)
				}
			},
			{
				title: '企业名称',
				dataIndex: 'entityName',
				customRender: ({ text, record, index }) => {
					return (
						<div class="cursor-pointer h-full text-[#D6000F]" onClick={() => flowRef.value.onOpen(record.entityId)}>
							{text}
						</div>
					)
				}
			},
			{
				title: '流程状态',
				dataIndex: 'lockStatus',
				width: 120,
				customRender: ({ text, record, index }) => {
					return optionsArr.value.find((item) => item.value === text)?.label
				}
			},
			{
				title: '操作用户',
				dataIndex: 'createUserName',
				width: 120
			},
			{
				title: '操作时间',
				dataIndex: 'createTime',
				width: 160
			}
		]
		if (hasPerm(['lock', 'unLock'])) {
			arr.push({
				title: '操作',
				dataIndex: 'action',
				align: 'center',
				width: 150,
				fixed: 'right'
			})
		}
		return arr
	})
	const selectedRowKeys = ref([])
	const selectedRowArr = ref([])
	// 列表选择配置
	const options = {
		clear: () => {
			selectedRowKeys.value = []
			selectedRowArr.value = []
		},
		rowSelection: {
			selectedRowKeys,
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
				selectedRowArr.value = selectedRows
			}
		}
	}
	const selectAll = () => {
		let arr = []
		tableData.value.map((item) => arr.push(item.entityId))
		selectedRowKeys.value = arr
		console.log(selectedRowKeys.value)
	}
	const getTbale = async () => {
		tableLoading.value = true
		bizEntityProcessApi
			.getList({
				orgIds: searchFormState.value.orgIds?.join(','),
				lockStatus: searchFormState.value.lockStatus?.join(',')
			})
			.then((res) => {
				tableData.value = res
			})
			.finally(() => {
				tableLoading.value = false
			})
	}

	const treeLoading = ref(false)
	const treeData = ref([])
	// 加载左侧的树
	const loadTreeData = async (versionId = '') => {
		// 处理树是否可编辑
		treeLoading.value = true
		const disposeTree = async (tree) => {
			for (let i = 0; i < tree.length; i++) {
				tree[i].disabled = tree[i]?.isClickable == 1 ? false : true
				if (tree[i]?.children) {
					disposeTree(tree[i]?.children)
				}
			}
		}
		bizInstitutionApi
			.getStructureTree({ structureType: '0' })
			.then(async (res) => {
				if (res?.length) {
					await disposeTree(res)
					res[0].disabled = true
					treeData.value = res
				} else {
					treeData.value = []
				}
			})
			.finally(() => {
				treeLoading.value = false
			})
	}
	loadTreeData()
	// 加锁
	const lockApi = async (params, next) => {
		bizEntityProcessApi
			.batchLockOrg({ entityIds: params.join(',') })
			.then((res) => {
				getTbale()
			})
			.finally(() => {
				next()
			})
	}
	const lockFn = async (next, record) => {
		lockApi([record.entityId], next)
	}
	// 批量解锁，锁定
	const lockBatchFn = async (type, next) => {
		if (!selectedRowKeys.value.length) {
			next()
			return message.warning(`请选择需要${type == '1' ? '锁定' : '解锁'}的数据`)
		}
		switch (type) {
			case '1':
				if (selectedRowArr.value.filter((item) => item.lockStatus == 1)?.length) {
					next()
					return message.warning(`请选未已锁定的数据`)
				}
				await lockApi(selectedRowKeys.value, next)
				options.clear()
				break
			case '2':
				next()
				if (selectedRowArr.value.filter((item) => item.lockStatus == 0)?.length)
					return message.warning(`请选择已锁定的数据`)
				flowFormRef.value.onOpen(selectedRowKeys.value)
				break
		}
	}
	const successFul = () => {
		options.clear()
		getTbale()
	}
	const cardBoxRef = ref()

	const height = ref(200)
	const tableScrollComputed = async () => {
		console.log(tableboxRef.value.offsetHeight)
		height.value = tableboxRef.value.offsetHeight - 56 - 47
	}
	onMounted(async () => {
		await tableScrollComputed()
		await getTbale()
		window.addEventListener('resize', tableScrollComputed)
	})
	onUnmounted(() => {
		window.removeEventListener('resize', tableScrollComputed)
	})
	watch(
		() => searchFormState.value,
		(newVal) => {
			console.log(newVal)
			getTbale()
		},
		{ deep: true }
	)
</script>

<style lang="less" scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	:deep(.ant-table-wrapper) {
		height: 100%;
		.ant-table-cell-scrollbar:not([rowspan]) {
			box-shadow: none;
		}
		.ant-table-thead th {
			background: #d6000f !important;
			color: #fff !important;
			border: 0 !important;
		}
		.ant-table-thead .ant-table-cell-fix-right-first::after {
			border: 0 !important;
		}
	}
	:deep(.ant-spin-nested-loading) {
		height: 100%;
	}
	:deep(.ant-spin-container) {
		height: 100%;
		display: flex;
		flex-flow: column nowrap;
		justify-content: space-between;
	}
</style>

<template>
	<xn-form-container
		title="流程查询"
		:width="1200"
		:maxHeight="0.9"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<div class="flex-1">
			<div v-if="indexShow" class="h-full">
				<s-table
					ref="tableRef"
					:columns="columns"
					:data="loadData"
					:hasSearch="false"
					bordered
					:row-key="(record) => record.id"
				>
					<template #bodyCell="{ column, record }">
						<template v-if="column.dataIndex === 'operationType'">
							{{ $TOOL.dictTypeData('operationType', record.operationType) }}
						</template>
						<template v-if="column.dataIndex === 'lockStatus'">
							{{ $TOOL.dictTypeData('lockStatus', record.lockStatus) }}
						</template>
						<template v-if="column.dataIndex === 'attachment'">
							<OrderUpload v-if="record.attachment" :readonly="true" v-model:value="record.attachment" />
						</template>
					</template>
				</s-table>
			</div>
		</div>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="dictForm" lang="jsx">
	import bizEntityProcessApi from '@/api/biz/bizEntityProcessApi'
	const visible = ref(false)
	const searchFormState = ref({})
	const indexShow = ref(true)

	const columns = [
		{
			title: '操作时间',
			dataIndex: 'createTime',
			ellipsis: true,
			width: 180
		},
		{
			title: '操作用户',
			dataIndex: 'createUserName',
			ellipsis: true,
			width: 120
		},
		{
			title: '操作',
			dataIndex: 'operationType',
			ellipsis: true,
			width: 120
		},
		{
			title: '流程状态',
			dataIndex: 'lockStatus',
			ellipsis: true,
			width: 120
		},
		{
			title: '备注',
			dataIndex: 'extJson',
			ellipsis: true
		},
		{
			title: '附件',
			dataIndex: 'attachment',
			width: 400
		}
	]

	// 表格查询 返回 Promise 对象
	const loadData = (parameter) => {
		return bizEntityProcessApi.getLogList(Object.assign(parameter, searchFormState.value)).then((data) => {
			return { records: data, total: data.length }
		})
	}

	// 打开抽屉
	const onOpen = (id) => {
		visible.value = true
		searchFormState.value.entityId = id
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style lang="less" scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.ant-picker {
		width: 100%;
	}
	.ant-modal-body {
		display: flex;
		flex-flow: column nowrap;
	}
</style>

<template>
	<xn-form-container
		title="流程操作"
		:width="600"
		:maxHeight="0.9"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" name="formRef" :model="formState">
			<a-form-item name="files" label="附件：">
				<OrderUpload ref="apprFinalfileRef" v-model:value="formState.files" uploadText="请上传附件" />
			</a-form-item>
			<a-form-item name="extJson" label="备注：">
				<a-textarea v-model:value="formState.extJson" placeholder="请输入备注"></a-textarea>
			</a-form-item>
		</a-form>
		<template #footer>
			<qt-button shape="default" type="primary" class="snowy-button-left" @click="(next) => submitForm(next)">
				确定
			</qt-button>
			<a-button class="xn-mr8" @click="onClose">取消</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="FlowForm">
	import bizEntityProcessApi from '@/api/biz/bizEntityProcessApi'
	const emits = defineEmits(['successFul'])
	const visible = ref(false)
	const formState = ref({})
	const formRef = ref()

	// 打开抽屉
	const onOpen = (idArr) => {
		visible.value = formState.value = { entityIds: idArr.join(',') }
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		formRef.value.resetFields()
	}
	const submitForm = (next) => {
		bizEntityProcessApi
			.batchUnLockOrg(formState.value)
			.then(() => {
				emits('successFul')
				onClose()
			})
			.finally(() => {
				next()
			})
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style lang="less" scoped></style>

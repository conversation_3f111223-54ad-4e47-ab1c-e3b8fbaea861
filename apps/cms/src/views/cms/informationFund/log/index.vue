<template>
	<div>
		<a-card :bordered="false" class="mb-3">
			<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
				<!-- <div class="items-center flex-wrap"> -->
				<a-row class="items-center flex-wrap">
					<a-col :span="8">
						<a-form-item label="时间范围" name="timeRange" :labelCol="{ span: 5 }" labelAlign="right">
							<a-range-picker
								format="YYYY-MM-DD"
								:presets="rangePresets"
								@change="onRangeChange"
								v-model:value="searchFormState.timeRange"
								valueFormat="YYYY-MM-DD"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="操作" name="types" :labelCol="{ span: 5 }" labelAlign="right">
							<Select
								mode="multiple"
								v-model:value="searchFormState.types"
								:options="searchOptions"
								:maxTagCount="1"
								placeholder="请选择操作"
							></Select>
						</a-form-item>
					</a-col>
					<a-col :span="8"></a-col>
					<a-col :span="8" class="mt-2">
						<a-form-item label="机构" name="org" :labelCol="{ span: 5 }" labelAlign="right">
							<TreeSelect
								v-model:value="searchFormState.org"
								:tree-data="selectTreeOrg"
								:field-names="selectTreeOrgFieldNames"
								:maxTagCount="1"
								@focus="onFocus"
								allow-clear
								multiple
								placeholder="请选择机构"
							>
							</TreeSelect>
						</a-form-item>
					</a-col>
					<a-col :span="8" class="mt-2">
						<a-form-item label="用户" name="user" :labelCol="{ span: 5 }" labelAlign="right">
							<!-- <Select
							mode="multiple"
							v-model:value="searchFormState.user"
							:options="userOptions"
							:maxTagCount="2"
							placeholder="请选择用户"
						></Select> -->
							<xn-page-select
								v-model:value="searchFormState.user"
								ref="xnUserPageSelectRef"
								placeholder="请选择用户"
								allow-clear
								mode="multiple"
								:maxTagCount="1"
								:page-function="selectApiFunction.childUserSelector"
								:echo-function="selectApiFunction.echoUser"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8" align="center" class="mt-2">
						<a-space>
							<a-button shape="default" type="primary" @click="loadData"> 查询 </a-button>
							<QtButton
								shape="default"
								type="primary"
								@click="(next) => onExport(next)"
								v-if="hasPerm('infomationLogExport')"
							>
								导出
							</QtButton>
						</a-space>
					</a-col>
				</a-row>
				<!-- </div> -->
			</a-form>
		</a-card>
		<a-card :bordered="false" class="mb-3">
			<SearchTable
				ref="tableRef"
				:columns="columns"
				:api="entityoperationlogApi.getData"
				:hasSearch="false"
				bordered
				:row-key="(record) => record.id"
				:searchInfo="searchInfo"
			>
				<template #bodyCell="{ column, record }">
					<template v-if="column.dataIndex === 'action'">
						<a-space>
							<a @click="handleCheck(record)" :class="{ disabled: record.type == 8 }">查看</a>
						</a-space>
					</template>
				</template>
			</SearchTable>
		</a-card>
	</div>
	<Form ref="formRef" :searchOptions="searchOptions" />
</template>

<script setup name="sysModule">
	import { onMounted } from 'vue'
	import { message } from 'ant-design-vue'
	import dayjs from 'dayjs'
	import downloadUtil from '@/utils/downloadUtil.js'
	import entityoperationlogApi from '@/api/biz/entityoperationlogApi'
	import bizOrgApi from '@/api/biz/bizOrgApi'
	import userCenterApi from '@/api/sys/userCenterApi'
	import userApi from '@/api/sys/userApi'
	import tool from '@/utils/tool'
	import Form from './form.vue'
	import { useRouter } from 'vue-router'
	import { useOrgTreeStore } from '@/store'
	const OrgTreeStore = useOrgTreeStore()
	const currency = tool.dictList('currency')
	const router = useRouter()

	const searchFormState = ref({})
	const searchInfo = ref({})
	const xnUserPageSelectRef = ref(null)
	watch(
		() => searchFormState.value,
		(val) => {
			const obj = {
				startDate: val.timeRange?.length ? val.timeRange[0] : '',
				endDate: val.timeRange?.length ? val.timeRange[1] : '',
				types: val.types?.join(',')
			}
			if (val.user?.length) {
				obj.userIds = val.user?.join?.(',')
			}
			// entityIds: val.org?.join(','),
			if (val.org?.length) {
				obj.entityIds = val.org?.join(',')
			}
			console.log(obj)
			searchInfo.value = obj
		},
		{ deep: true }
	)
	const formRef = ref()
	const searchFormRef = ref()
	const searchOptions = ref([])
	const userOptions = ref([
		{
			label: '王玥',
			value: 0
		},
		{
			label: '张角',
			value: 1
		}
	])
	const rangePresets = ref([
		{
			label: '今日',
			value: [dayjs(), dayjs()]
		},
		{
			label: '近7天',
			value: [dayjs().add(-7, 'd'), dayjs()]
		},
		{
			label: '本月',
			value: [dayjs().startOf('month'), dayjs().endOf('month')]
		}
	])
	// 传递选择组件需要的API
	const selectApiFunction = {
		childUserSelector: (param) => {
			return entityoperationlogApi.userSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		},
		echoUser: (param) => {
			return userCenterApi.userCenterGetUserListByIdList(param).then((data) => {
				return Promise.resolve(data)
			})
		}
	}
	const onRangeChange = (dates, dateStrings) => {
		if (dates) {
			console.log('From: ', dates[0], ', to: ', dates[1])
		} else {
			console.log('Clear')
		}
	}
	const selectTreeOrg = ref(OrgTreeStore.selectTreeOrg)
	const onFocus = () => {
		selectTreeOrg.value = OrgTreeStore.selectTreeOrg
	}
	const selectTreeOrgFieldNames = ref({
		children: 'children',
		label: 'simpOrgName',
		value: 'id'
	})
	const tableRef = ref()
	const columns = [
		{
			title: '用户',
			dataIndex: 'userName'
		},
		{
			title: '操作',
			dataIndex: 'type',
			customRender: ({ text, record, index }) => {
				let hasIndex = searchOptions.value?.findIndex?.((item) => item.value == text)
				return hasIndex != -1 ? searchOptions.value?.[hasIndex]?.label : ''
			}
		},
		{
			title: '操作时间',
			dataIndex: 'createTime'
			// sorter: true
		},
		{
			title: '期间',
			dataIndex: 'versionDate',
			customRender: ({ text, record, index }) => {
				let date = text ? text.slice(0, 7) + '月' : ''
				return date ? date.replace('-', '年') : ''
			}
			// sorter: true
		},
		{
			title: '机构',
			dataIndex: 'simpOrgName'
			// sorter: true
		},
		{
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: '80px'
		}
	]

	const loadData = (parameter) => {
		return entityoperationlogApi.getData(Object.assign(parameter, searchInfo.value)).then((res) => {
			return res
		})
	}
	onMounted(() => {
		xnUserPageSelectRef.value.onPage()
		searchOptions.value = tool.dictList('operationLogType') || []
	})
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	const onExport = (next) => {
		entityoperationlogApi
			.export(searchInfo.value)
			.then((res) => {
				message.success('导出成功')
				downloadUtil.resultDownload(res)
			})
			.finally(() => {
				next()
			})
	}
	const handleCheck = (record) => {
		if (record.type == 8) {
			return message.error('该操作无法查看详情!')
		}
		if ([6, 7].includes(+record.type)) {
			router.push({ path: '/institutionalPortrait/service', query: { entityId: record.entityId } })
			return
		}
		formRef.value.onOpen(record)
	}
</script>
<style lang="less" scoped>
	:deep(.ant-form-item) {
		margin-bottom: 0 !important;
	}
	.disabled {
		cursor: not-allowed;
		color: #c0c4cc !important;
	}
	.ant-advanced-search-form {
		:deep(.ant-picker) {
			width: 100%;
		}
	}
</style>

<template>
	<xn-form-container
		title="查看详情"
		width="80%"
		maxHeight="none"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
		wrap-class-name="high-modal"
	>
		<a-spin :spinning="loading">
			<a-form
				ref="formRef"
				:labelCol="{
					style: {
						width: '80px'
					}
				}"
				class="mb-2"
			>
				<!-- <a-form-item label="起始时间">
					<div class="ml-5">2020-10-01 17:31:23</div>
				</a-form-item> -->
				<!-- <a-form-item label="终止时间"> <div class="ml-5">2020-10-01 17:45:53</div></a-form-item> -->
				<a-form-item label="操作时间">
					<div class="ml-5">{{ detail.createTime }}</div></a-form-item
				>
				<a-form-item label="操作">
					<div class="ml-5">{{ type }}</div></a-form-item
				>
				<a-form-item label="用户">
					<div class="ml-5">{{ detail.userName }}</div></a-form-item
				>
				<a-form-item label="期间">
					<div class="ml-5">{{ versionDate }}</div></a-form-item
				>
				<a-form-item label="机构">
					<div class="ml-5">{{ detail.simpOrgName }}</div>
				</a-form-item>
			</a-form>
			<div class="flex flex-col">
				<Main v-if="pageIsAdd" ref="mainRef" :disabled="true" :showAll="false" :initData="shEntityAddOrUpdVO" />
				<template v-else>
					<div v-if="[4, 5].includes(+logType)" class="mt-5">
						<Collapse title="变更数据">
							<template #content>
								<a-table
									:columns="columnsDiffDisposalType"
									:data-source="shEntityDisposalData"
									:pagination="false"
									size="small"
									:row-key="(record) => record.id"
								></a-table>
							</template>
						</Collapse>
					</div>
					<Detail
						:shEntityOperationLogVO="shEntityOperationLogVO"
						:logType="logType"
						:isLoading="isLoading"
						v-else
					></Detail>
				</template>
			</div>
		</a-spin>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="dictForm" lang="jsx">
	import { ref, provide } from 'vue'
	import { message } from 'ant-design-vue'
	import { ExportOutlined } from '@ant-design/icons-vue'
	import entityoperationlogApi from '@/api/biz/entityoperationlogApi.js'
	import Main from '../service/components/main.vue'
	import Detail from './detail.vue'
	const mainRef = ref()
	const visible = ref(false)

	const loading = ref(false)
	const tabActive = ref('0')
	// 提供注册/注销方法
	provide('validatorRegistry', {
		tabActive,
		register: (key, validator) => {
			// tabFormObj.value.set(key, validator)
		},
		unregister: (key) => {
			// tabFormObj.value.delete(key)
		}
	})
	const props = defineProps({
		searchOptions: {
			type: Array,
			default: () => []
		}
	})
	const pageIsAdd = ref(false)
	const detail = ref()
	const type = ref()
	const shEntityOperationLogVO = ref()
	const shEntityAddOrUpdVO = ref({})
	const logType = ref(-1)
	const versionDate = ref()
	const shEntityDisposalData = ref([])
	const isLoading = ref(false)
	const getDetail = (record) => {
		isLoading.value = true
		entityoperationlogApi
			.getDetail({ id: record.id })
			.then((res) => {
				isLoading.value = false
				detail.value = res || {}
				if (res?.type == 1) {
					pageIsAdd.value = true
				} else {
					pageIsAdd.value = false
				}
				shEntityOperationLogVO.value = res?.shEntityOperationLogVO || []
				shEntityAddOrUpdVO.value = res?.shEntityAddOrUpdVO || {}
				shEntityDisposalData.value = res?.shEntityDisposal?.disposalId ? [res?.shEntityDisposal] : []
				logType.value = res?.type || -1
				let date = res?.versionDate ? res?.versionDate.slice(0, 7) + '月' : ''
				if (date) {
					versionDate.value = date.replace('-', '年') || ''
				}
				let hasIndex = props.searchOptions.findIndex((item) => item.value == res?.type)
				if (hasIndex != -1) {
					type.value = props.searchOptions[hasIndex].label || ''
				}
			})
			.catch(() => {
				isLoading.value = false
			})
	}
	const columnsDiffDisposalType = computed(() => {
		const arr = [
			{
				title: '序号',
				dataIndex: 'id',
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '变更前',
				dataIndex: 'beforeDisposalType',
				customRender: ({ text, record, index }) => {
					return record.beforeDisposalType || '一'
				}
			},
			{
				title: '变更后',
				dataIndex: 'nowDisposalType',
				customRender: ({ text, record, index }) => {
					return record.nowDisposalType || '一'
				}
			}
		]
		return arr
	})
	// 打开抽屉
	const onOpen = async (record) => {
		visible.value = true
		loading.value = true
		loading.value = false
		getDetail(record)
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		tabActive.value = '0'
		shEntityAddOrUpdVO.value = {}
		shEntityOperationLogVO.value = []
		shEntityDisposalData.value = []
	}

	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style lang="less" scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.ant-picker {
		width: 100%;
	}
	.ant-modal-body {
		display: flex;
		flex-flow: column nowrap;
	}
</style>
<style lang="less">
	.high-modal {
		.ant-modal-header {
			border-bottom: none;
		}
		.ant-modal {
		}
		.ant-modal-content {
			display: flex;
			flex-direction: column;
			height: calc(80vh);
		}
		.ant-modal-body {
			flex: 1;
		}
	}
</style>

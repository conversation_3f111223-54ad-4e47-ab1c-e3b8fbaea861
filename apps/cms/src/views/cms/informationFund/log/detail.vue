<template>
	<div>
		<a-empty
			v-if="
				!isLoading &&
				!businessInfoPanelShow &&
				!shareHolderPanelShow &&
				!operatingInformationPanelShow &&
				!manageInfoPanelShow
			"
			description="暂无变更数据"
			class="h-[200px] flex flex-col justify-center items-center"
		>
		</a-empty>
		<a-spin :spinning="isLoading" v-else wrapperClassName="spinner">
			<a-tabs v-model:activeKey="tabActive" class="h-full" :destroyInactiveTabPane="true">
				<a-tab-pane key="0" tab="工商信息" :forceRender="true" v-if="businessInfoPanelShow">
					<Collapse title="基本信息">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.businessBasicInformation"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
					<Collapse title="董监高">
						<template #content>
							<a-table
								:columns="columns2"
								:data-source="shEntityOperationLogVO.businessManagerInformation"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
				</a-tab-pane>
				<a-tab-pane key="1" tab="内部股权信息" :forceRender="true" v-if="shareHolderPanelShow">
					<Collapse title="填报标准">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.fillingStandards"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
					<Collapse title="内部股东">
						<template #content>
							<a-table
								:columns="columns4"
								:data-source="shEntityOperationLogVO.equityRelationship"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
				</a-tab-pane>
				<a-tab-pane key="2" tab="经营数据" :forceRender="true" v-if="operatingInformationPanelShow">
					<Collapse title="经营数据">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.operatingInformation"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
				</a-tab-pane>
				<a-tab-pane key="3" tab="管理职能" :forceRender="true" v-if="manageInfoPanelShow">
					<Collapse title="基本信息">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.manageFunctionBasicInformation"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
					<Collapse title="行业">
						<template #content>
							<a-divider orientation="left">所属类别</a-divider>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.industryInfo"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
							<a-divider orientation="left">行业信息列表</a-divider>
							<a-table
								:columns="columns3"
								:data-source="shEntityOperationLogVO.categoryInfo"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
					<Collapse title="机构性质">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.natureInstitution"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
					<!-- <Collapse title="瘦身健体">
					<template #content>
						<a-table
							:columns="columns"
							:data-source="list"
							:pagination="false"
							size="small"
							:row-key="(record) => record.id"
						></a-table>
					</template>
				</Collapse> -->
					<Collapse title="瘦身健体">
						<template #content>
							<a-divider orientation="left">亏损治理</a-divider>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.lossManagement"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
							<template v-if="[2, 3].includes(+logType)">
								<a-divider orientation="left">处置信息</a-divider>
								<a-table
									:columns="columns2"
									:data-source="shEntityOperationLogVO.disposalId"
									:pagination="false"
									size="small"
									:row-key="(record) => record.id"
								></a-table>
							</template>
						</template>
					</Collapse>
					<Collapse title="特殊情况的参股公司">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.special"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
					<Collapse title="填报人信息">
						<template #content>
							<a-table
								:columns="columns"
								:data-source="shEntityOperationLogVO.informationPerson"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							></a-table>
						</template>
					</Collapse>
				</a-tab-pane>
			</a-tabs>
		</a-spin>
	</div>
</template>

<script setup name="dictForm" lang="jsx">
	import tool from '@/utils/tool'
	import { some } from 'lodash-es'
	import { onUnmounted, watchEffect } from 'vue'
	const isOrNo = tool.dictList('WHETHER')
	const sector = tool.dictList('sector')
	const belong_sector = tool.dictList('belong_sector')
	const legal_entity_level = tool.dictList('legal_entity_level')
	const management_level = tool.dictList('management_level')
	const thepur_exist = tool.dictList('thepur_exist')
	// spv
	const isother_subher = tool.dictList('isother_subher')
	const cancel_progress = tool.dictList('cancel_progress')
	const cancel_type = tool.dictList('cancel_type')
	const cancel_cause = tool.dictList('cancel_cause')
	const loss_type = tool.dictList('loss_type')
	const appr_type = tool.dictList('appr_type')
	const industry_categ = tool.dictList('industry_categ')
	const dictTypeTree = tool.dictDataAll()
	const props = defineProps({
		shEntityOperationLogVO: {
			type: Object,
			default: () => {}
		},
		logType: {
			type: Number,
			default: -1
		},
		isLoading: {
			type: Boolean,
			default: true
		}
	})
	const businessInfoPanelShow = computed(() => {
		return (
			props.shEntityOperationLogVO?.businessBasicInformation?.length > 0 ||
			props.shEntityOperationLogVO?.businessManagerInformation?.length > 0
		)
	})
	const shareHolderPanelShow = computed(() => {
		return (
			props.shEntityOperationLogVO?.fillingStandards?.length > 0 ||
			props.shEntityOperationLogVO?.equityRelationship?.length > 0
		)
	})
	const operatingInformationPanelShow = computed(() => {
		return props.shEntityOperationLogVO?.operatingInformation?.length > 0
	})
	const manageInfoPanelShow = computed(() => {
		return (
			props.shEntityOperationLogVO?.manageFunctionBasicInformation?.length > 0 ||
			props.shEntityOperationLogVO?.industryInfo?.length > 0 ||
			props.shEntityOperationLogVO?.categoryInfo?.length > 0 ||
			props.shEntityOperationLogVO?.natureInstitution?.length > 0 ||
			props.shEntityOperationLogVO?.lossManagement?.length > 0 ||
			props.shEntityOperationLogVO?.disposalId?.length > 0 ||
			props.shEntityOperationLogVO?.special?.length > 0 ||
			props.shEntityOperationLogVO?.informationPerson?.length > 0
		)
	})
	const dynamicImport = (icon) => {
		const href = new URL(`../../../../assets/images/cms/home/<USER>
		return href
	}
	const tabActive = ref('0')
	const stopWatch = watchEffect(() => {
		let validIndex = 0
		some(
			[businessInfoPanelShow, shareHolderPanelShow, operatingInformationPanelShow, manageInfoPanelShow],
			(item, index) => {
				if (item.value) {
					validIndex = index
				}
				return item.value
			}
		)
		tabActive.value = validIndex + ''
	})

	const columns = computed(() => {
		const arr = [
			{
				title: '序号',
				dataIndex: 'id',
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '字段',
				dataIndex: 'fieldName'
			},
			{
				title: '变更前',
				dataIndex: 'baseField'
			},
			{
				title: '变更后',
				dataIndex: 'contrastField'
			}
		]
		return arr
	})
	const list = ref([
		{
			id: 0,
			name: '注册资本',
			before: '1000万人民币',
			after: '2000万人民币'
		},
		{
			id: 1,
			name: '经营范围',
			before: '软件开发',
			after: '软件开发、技术服务'
		},
		{
			id: 2,
			name: '企业类型',
			before: '有限责任公司',
			after: '有限责任公司(自然人投资或控股)'
		},
		{
			id: 3,
			name: '成立日期',
			before: '2022-01-01',
			after: '2022-01-01'
		},
		{
			id: 4,
			name: '核准日期',
			before: '2022-01-01',
			after: '2022-02-01'
		}
	])
	const columns2 = computed(() => {
		const arr = [
			{
				title: '序号',
				dataIndex: 'id',
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '姓名',
				dataIndex: 'name'
			},
			{
				title: '变更前',
				dataIndex: 'baseField'
			},
			{
				title: '变更后',
				dataIndex: 'contrastField'
			},
			{
				title: '变更类型',
				dataIndex: 'type'
			}
		]
		return arr
	})
	const columns3 = computed(() => {
		const arr = [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '行业门类',
				dataIndex: 'baseCategName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contCategName
					} else if (record.type == '删除') {
						data = record.baseCategName
					} else {
						data = record.baseCategName + ' -> ' + record.contCategName
					}
					return data
				}
			},
			{
				title: '行业大类',
				dataIndex: 'baseLargeName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contLargeName
					} else if (record.type == '删除') {
						data = record.baseLargeName
					} else {
						data = record.baseLargeName + ' -> ' + record.contLargeName
					}
					return data
				}
			},
			{
				title: '行业中类',
				dataIndex: 'baseMiddleName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contMiddleName
					} else if (record.type == '删除') {
						data = record.baseMiddleName
					} else {
						data = record.baseMiddleName + ' -> ' + record.contMiddleName
					}
					return data
				}
			},
			{
				title: '行业小类',
				dataIndex: 'baseCodeName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contCodeName
					} else if (record.type == '删除') {
						data = record.baseCodeName
					} else {
						data = record.baseCodeName + ' -> ' + record.contCodeName
					}
					return data
				}
			},
			{
				title: '最近一年经审计后的营业收入',
				dataIndex: 'baseOperating',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let baseOperating = record.baseOperating
					let contOperating = record.contOperating
					let src = baseOperating > contOperating ? dynamicImport('icon-down.png') : dynamicImport('icon-up.png')
					let style = baseOperating > contOperating ? 'color:#26c030' : 'color:#d6000f'
					let data = ''
					if (record.type == '新增') {
						data = contOperating
					} else if (record.type == '删除') {
						data = baseOperating
					} else {
						data = (
							<div>
								{(baseOperating || '') + ' -> '}
								<span style={style}>{contOperating || ''}</span>
								<span>
									<img style="width: 15px; height:15px" src={src} />
								</span>
							</div>
						)
					}
					return data
				}
			},
			{
				title: '营收占比（%）',
				dataIndex: 'baseRevenue',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let baseRevenue = record.baseRevenue
					let contRevenue = record.contRevenue
					let src = baseRevenue > contRevenue ? dynamicImport('icon-down.png') : dynamicImport('icon-up.png')
					let style = baseRevenue > contRevenue ? 'color:#26c030' : 'color:#d6000f'
					let data = ''
					if (record.type == '新增') {
						data = contRevenue + '%'
					} else if (record.type == '删除') {
						data = baseRevenue + '%'
					} else {
						data = (
							<div>
								{(baseRevenue ? baseRevenue + '%' : '') + ' -> '}
								<span style={style}>{contRevenue ? contRevenue + '%' : ''}</span>
								<span>
									<img style="width: 15px; height:15px" src={src} />
								</span>
							</div>
						)
					}
					return data
				}
			},
			{
				title: '变更类型',
				dataIndex: 'type',
				ellipsis: true
			}
		]
		return arr
	})
	const relationship = tool.dictList('relationship')
	const columns4 = computed(() => {
		return [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '内部股东企业全称',
				dataIndex: 'baseOrgName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contOrgName
					} else if (record.type == '删除') {
						data = record.baseOrgName
					} else {
						data =
							record.baseOrgName == record.contOrgName
								? record.baseOrgName
								: record.baseOrgName + ' -> ' + record.contOrgName
					}
					return data
				}
			},
			{
				title: '与内部股东的财务关系',
				dataIndex: 'baseRelationship',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					// let baseRelationship = relationship.find((item) => item.value === record.baseRelationship)?.label
					// let contRelationship = relationship.find((item) => item.value === record.contRelationship)?.label
					let baseRelationship = record.baseRelationship
					let contRelationship = record.contRelationship
					let data = ''
					if (record.type == '新增') {
						data = contRelationship
					} else if (record.type == '删除') {
						data = baseRelationship
					} else {
						data =
							baseRelationship == contRelationship ? baseRelationship : baseRelationship + ' -> ' + contRelationship
					}
					return data
				}
			},
			{
				title: '内部股东持股比例（%）',
				dataIndex: 'baseShareRate',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let baseShareRate = record.baseShareRate ? parseFloat((record.baseShareRate * 100).toPrecision(12)) : 0
					let contShareRate = record.contShareRate ? parseFloat((record.contShareRate * 100).toPrecision(12)) : 0
					let src = baseShareRate > contShareRate ? dynamicImport('icon-down.png') : dynamicImport('icon-up.png')
					let style = baseShareRate > contShareRate ? 'color:#26c030' : 'color:#d6000f'
					let data = ''
					if (record.type == '新增') {
						data = contShareRate ? contShareRate + '%' : ''
					} else if (record.type == '删除') {
						data = baseShareRate ? baseShareRate + '%' : ''
					} else {
						data =
							baseShareRate == contShareRate ? (
								<div>{baseShareRate + '%'}</div>
							) : (
								<div style="display: flex;overflow-x: auto;">
									{baseShareRate + '%' + ' -> '}
									<span style={style}>{contShareRate + '%'}</span>
									<span>
										<img style="width: 15px; height:15px" src={src} />
									</span>
								</div>
							)
					}
					return data
				}
			},
			{
				title: '是否持股比例最高的内部股东',
				dataIndex: 'baseIsHight',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contIsHight
					} else if (record.type == '删除') {
						data = record.baseIsHight
					} else {
						data =
							record.baseIsHight == record.contIsHight
								? record.baseIsHight
								: record.baseIsHight + ' -> ' + record.contIsHight
					}
					return data
				}
			},
			{
				title: '变更类型',
				dataIndex: 'type',
				ellipsis: true
			}
		]
	})
	const list2 = ref([
		{
			id: 0,
			name: '王2',
			before: '',
			after: '高级管理人员',
			type: '新增'
		},
		{
			id: 1,
			name: '王3',
			before: '高级管理人员',
			after: '',
			type: '删除'
		},
		{
			id: 2,
			name: '王4',
			before: '董事',
			after: '高级管理人员',
			type: '变更'
		}
	])
	onUnmounted(() => {
		stopWatch()
	})
</script>
<style lang="less" scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.ant-picker {
		width: 100%;
	}
	.ant-modal-body {
		display: flex;
		flex-flow: column nowrap;
	}
	.spinner {
		display: flex;
		width: 100%;
		min-height: 200px;
		justify-content: center;
		align-items: center;
	}
	:deep(.ant-spin-nested-loading .ant-spin-container) {
		width: 100%;
	}
</style>

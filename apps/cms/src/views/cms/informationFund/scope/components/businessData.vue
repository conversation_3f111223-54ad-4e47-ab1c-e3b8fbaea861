<template>
	<!-- 经营数据 -->
	<a-form
		ref="formRef"
		:labelCol="{
			style: {
				width: '150px'
			}
		}"
		:wrapper-col="{
			span: 10
		}"
		:model="formState"
		:rules="formRules"
		:disabled="disabled"
		:scrollToFirstError="true"
	>
		<Collapse title="经营数据（截止到当前版本月份）">
			<template #content>
				<a-form-item ref="singleProfit" label="单体报表净利润" name="singleProfit">
					<a-input-number
						class="w-full"
						v-model:value="formState.singleProfit"
						@change="
							(value) => {
								computedHasLoss()
							}
						"
						addon-after="万元"
						allowClear
						placeholder="请输入单体报表净利润"
					/>
				</a-form-item>
				<a-form-item ref="parentProfit" label="合并报表归母净利润" name="parentProfit">
					<a-input-number
						class="w-full"
						v-model:value="formState.parentProfit"
						@change="
							(value) => {
								computedHasLoss()
							}
						"
						addon-after="万元"
						allowClear
						placeholder="请输入合并报表归母净利润"
					/>
				</a-form-item>
				<a-form-item label="是否亏损" name="hasLoss" class="mb-[0px !important]">
					<Select
						class="w-full"
						:options="isOrNo"
						v-model:value="formState.hasLoss"
						@change="
							(value) => {
								if (value == 'N') formState.lossType = null
							}
						"
						disabled
						allowClear
						placeholder="请选择是否亏损"
					/>
				</a-form-item>
				<a-form-item label="亏损类型" name="lossType" class="mb-[0px !important]">
					<Select
						:options="loss_type"
						v-model:value="formState.lossType"
						:disabled="disabled || formState.hasLoss == 'N'"
						allowClear
						placeholder="请选择亏损类型"
					/>
				</a-form-item>
			</template>
		</Collapse>
	</a-form>
</template>
<script setup>
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import tool from '@/utils/tool'
	import bus from '@/utils/bus'
	const isOrNo = tool.dictList('WHETHER')
	const loss_type = tool.dictList('loss_type')
	const props = defineProps({
		data: {
			type: Object,
			default: () => ({})
		},
		disabled: {
			type: Boolean,
			default: false
		},
		keys: {
			type: String,
			default: ''
		}
	})

	const formRef = ref()
	const formState = ref({})
	const formRules = computed(() => {
		let obj = {
			singleProfit: [required('请输入单体报表净利润(亿元)')],
			parentProfit: [required('请输入合并报表归母净利润')],
			lossType: formState.value.hasLoss == 'Y' ? [required('请选择亏损类型')] : []
		}
		return obj
	})
	const computedHasLoss = () => {
		if (formState.value.hasBusiness == 'Y') {
			if (formState.value.parentProfit <= 0 && formState.value.singleProfit < 0) {
				formState.value.hasLoss = 'Y'
			} else {
				formState.value.hasLoss = 'N'
				formState.value.lossType = null
			}
		} else if (formState.value.hasBusiness == 'N') {
			formState.value.hasLoss = 'N'
			formState.value.lossType = null
		}
	}
	bus.on('hasBusinessChange', (data) => {
		formState.value.hasBusiness = data
		computedHasLoss()
	})
	const submitForm = async () => {
		return new Promise((resolve, reject) => {
			Object.keys(formState.value).forEach((key) => {
				if (formState.value[key] && typeof formState.value[key] === 'string')
					formState.value[key] = formState.value[key].trim()
			})
			formRef.value
				.validate()
				.then(() => {
					const form = cloneDeep(formRef.value.getFieldsValue())
					formRef.value.clearValidate()
					resolve(form)
				})
				.catch((error) => {
					console.log(error)
					if (error.errorFields?.length) {
						formRef.value.scrollToField(error.errorFields[0].name[0])
						message.warning(error.errorFields[0].errors[0])
					}
					error.key = props.keys
					reject(error)
				})
		})
	}
	// 注入注册方法
	const { register, unregister } = inject('validatorRegistry')
	onMounted(() => {
		register(props.keys, submitForm)
	})
	onUnmounted(() => {
		unregister(props.keys)
	})
	watch(
		() => props.data,
		(val) => {
			formState.value = cloneDeep(val)
		},
		{ immediate: true, deep: true }
	)
	const clearValidate = () => {
		formRef.value.clearValidate()
	}
	defineExpose({
		formState,
		clearValidate
	})
</script>
<style lang="less" scoped></style>

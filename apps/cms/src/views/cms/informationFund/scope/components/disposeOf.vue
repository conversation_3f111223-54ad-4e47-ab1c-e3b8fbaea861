<template>
	<!-- 管理职能 -->
	<a-form
		ref="formRef"
		:labelCol="{
			style: {
				width: '150px'
			}
		}"
		:wrapper-col="{
			span: 10
		}"
		:model="formState"
		:rules="formRules"
		:disabled="disabled"
		:scrollToFirstError="true"
	>
		<!-- :disabled="disabled" -->
		<Collapse title="基本信息">
			<template #content>
				<a-form-item label="机构id" name="id" v-if="false">
					<a-input v-model:value="formState.id" allowClear placeholder="机构id" disabled />
				</a-form-item>
				<a-form-item ref="entityCode" label="机构唯一编码" name="entityCode">
					<a-input v-model:value="formState.entityCode" allowClear placeholder="请输入企业唯一编码" disabled />
				</a-form-item>
				<a-form-item ref="manageCode" label="管理编码" name="manageCode">
					<a-input v-model:value="formState.manageCode" allowClear placeholder="请输入管理编码" disabled />
				</a-form-item>
				<a-form-item ref="manageCompany" label="所属管理主体" name="manageCompany">
					<a-input
						v-model:value="formState.manageCompany"
						allowClear
						placeholder="请输入所属管理主体（非必填）"
						disabled
					/>
				</a-form-item>
				<a-form-item ref="orgParentunitId" label="上级管理单位" name="orgParentunitId">
					<TreeSelect
						v-model:value="formState.orgParentunitId"
						:tree-data="orgParentunitIdTreeData"
						:field-names="selectTreeOrgFieldNames"
						placeholder="请选择上级管理单位"
						v-if="props.tabActive == 3"
					>
					</TreeSelect>
				</a-form-item>
				<a-form-item label="集团板块" name="plate">
					<Select :options="sector" v-model:value="formState.plate" placeholder="请选择集团板块" />
				</a-form-item>
				<a-form-item label="泰富板块" name="belongSectorId" v-if="formState.manageCompany == '中信泰富有限公司'">
					<Select
						:options="belong_sector"
						v-model:value="formState.belongSectorId"
						placeholder="请选择泰富板块"
						:disabled="disabled || formState.manageCompany != '中信泰富有限公司'"
					/>
				</a-form-item>
				<a-form-item ref="consolidationParentId" label="上级并表单位" name="consolidationParentId">
					<TreeSelect
						v-model:value="formState.consolidationParentId"
						:tree-data="consolidationParentIdTreeData"
						:field-names="selectTreeOrgFieldNames"
						placeholder="请选择上级并表单位"
						:disabled="disabled || orgNatureIsShow"
						v-if="props.tabActive == 3"
					>
					</TreeSelect>
				</a-form-item>
				<a-form-item ref="legalEntityLevel" label="法人层级" name="legalEntityLevel">
					<Select
						:options="legal_entity_level"
						v-model:value="formState.legalEntityLevel"
						placeholder="请选择法人层级"
						@focus="
							() => {
								oldLegalEntityLevel = formState.legalEntityLevel
							}
						"
						@select="
							(value) => {
								if (Number(value) > 7) {
									Modal.confirm({
										title: '提示',
										content: '“根据集团要求，法人层级不应超过7级，请确认法人层级是否准确？',
										okText: '已确认',
										cancelText: '未确认',
										onOk: async () => {},
										onCancel: () => {
											formState.legalEntityLevel = oldLegalEntityLevel
										}
									})
								} else {
									oldLegalEntityLevel = value
								}
							}
						"
					/>
				</a-form-item>
				<a-form-item label="管理层级" name="managementLevel">
					<Select
						:options="management_level"
						v-model:value="formState.managementLevel"
						@focus="
							() => {
								oldManagementLevel = formState.managementLevel
							}
						"
						@select="
							(value) => {
								if (Number(value) > 4) {
									Modal.confirm({
										title: '提示',
										content: '“根据集团要求，管理层级不应超过4级，请确认管理层级是否准确？',
										okText: '已确认',
										cancelText: '未确认',
										onOk: async () => {},
										onCancel: () => {
											formState.managementLevel = oldManagementLevel
										}
									})
								} else {
									oldManagementLevel = value
								}
							}
						"
						placeholder="请选择管理层级"
					/>
				</a-form-item>
			</template>
		</Collapse>
		<Collapse title="机构性质">
			<template #content>
				<a-form-item ref="hasBusiness" label="是否实质开展经营" name="hasBusiness">
					<Select
						:options="isOrNo"
						v-model:value="formState.hasBusiness"
						placeholder="请选择是否实质开展经营"
						@change="
							(value, option) => {
								if (value == 'Y') {
									formState.isSpvNew = 'N'
									formState.isFinaHire = 'N'
									formState.isotherSubher = 'N'
									formState.thepurExist = null
									formRef.clearValidate()
								} else {
									formState.isSpvNew = null
									formState.isFinaHire = null
									formState.isotherSubher = null
									formState.thepurExist = null
								}
								bus.emit('hasBusinessChange', value)
							}
						"
					/>
				</a-form-item>
				<a-form-item
					ref="isSpvNew"
					label="是否为SPV公司"
					name="isSpvNew"
					:labelCol="{
						style: {
							width: '150px',
							paddingLeft: '30px',
							textAlign: 'left'
						}
					}"
				>
					<Select
						:options="isOrNo"
						v-model:value="formState.isSpvNew"
						allowClear
						placeholder="请选择是否为SPV公司"
						:disabled="disabled ? disabled : formState.hasBusiness == 'Y'"
						@focus="
							() => {
								oldIsSpvNew = formState.isSpvNew
							}
						"
						@change="
							(value) => {
								const setFn = () => {
									formState.isFinaHire = 'N'
									formState.isotherSubher = 'N'
									formState.thepurExist = null

									formState.industryCateg = 'L'
									formState.industryLarge = 'L72'
									formState.industryMiddle = 'L721'
									formState.categoryCode = 'L7212'

									const md = findId(industryTreeData, 'L721')
									industry_categ_md = [{ label: md.dictLabel, value: md.dictValue }]
									const lg = findId(industryTreeData, 'L72')
									industry_categ_lg = [{ label: lg.dictLabel, value: lg.dictValue }]

									if (industry_categ_sm?.filter((item) => item.code == 'L7212')?.length < 1) {
										industry_categ_sm.push({ code: 'L7212', name: '投资与资产管理', parentCode: 'L721' })
									}
									formState.entitycategoryList = [
										{
											industryCateg: 'L',
											industryLarge: 'L72',
											industryMiddle: 'L721',
											categoryCode: 'L7212',
											categoryCodeName: '投资与资产管理',
											operatingRevenue: 0,
											revenueProportion: 0,
											isEdit: true
										}
									]
								}
								if (value == 'Y') {
									if (formState.entitycategoryList?.length == 0) {
										setFn()
									} else if (
										(formState.entitycategoryList?.length == 1 &&
											formState.entitycategoryList[0].categoryCode != 'L7212') ||
										formState.entitycategoryList?.length > 1
									) {
										Modal.confirm({
											title: '提示',
											content: 'SPV公司按照7212投资与资产管理进行行业填报，将不存在其余行业信息。',
											okText: '是',
											cancelText: '否',
											onOk: () => {
												formState.entitycategoryList.map((item) => {
													if (item.id) formState.entitycategoryDeleteIds.push(item.id)
												})
												setFn()
											},
											onCancel: () => {
												formState.isSpvNew = oldIsSpvNew
											}
										})
									}
								} else {
									Modal.confirm({
										title: '提示',
										content:
											'SPV属性和存续目的不得随意变更，须提前与战投部业务处室沟通协商，擅自变更将追究责任，请确认是否已与业务处室协商一致？',
										okText: '是',
										cancelText: '否',
										onOk: () => {
											formState.thepurExist = null
											// formState.isFinaHire = 'N'
											// formState.isotherSubher = 'N'
										},
										onCancel: () => {
											formState.isSpvNew = oldIsSpvNew
										}
									})
								}
								selectNFn()
							}
						"
					/>
				</a-form-item>
				<a-form-item
					label="是否为金融租赁项目公司"
					name="isFinaHire"
					:labelCol="{
						style: {
							width: '230px',
							paddingLeft: '30px',
							textAlign: 'left'
						}
					}"
				>
					<Select
						:options="isOrNo"
						v-model:value="formState.isFinaHire"
						:disabled="disabled ? disabled : formState.hasBusiness == 'Y'"
						@change="
							(value) => {
								if (value == 'Y') {
									formState.isSpvNew = 'N'
									formState.isotherSubher = 'N'
									formState.thepurExist = 'financial_company_1'
								} else {
									formState.thepurExist = null
								}
								selectNFn()
							}
						"
						allowClear
						placeholder="请选择是否为金融租赁项目公司"
					/>
				</a-form-item>
				<a-form-item
					label="是否其他不实质开展经营公司"
					name="isotherSubher"
					:labelCol="{
						style: {
							width: '230px',
							paddingLeft: '30px',
							textAlign: 'left'
						}
					}"
				>
					<Select
						:options="isOrNo"
						v-model:value="formState.isotherSubher"
						:disabled="disabled ? disabled : formState.hasBusiness == 'Y'"
						@change="
							(value) => {
								if (value == 'Y') {
									formState.isSpvNew = 'N'
									formState.isFinaHire = 'N'
								}

								formState.thepurExist = null
								selectNFn()
							}
						"
						allowClear
						placeholder="请选择是否其他不实质开展经营公司"
					/>
				</a-form-item>
				<a-form-item
					ref="thepurExist"
					label="不实质开展经营机构的存续目的"
					name="thepurExist"
					:labelCol="{
						style: {
							width: '220px'
						}
					}"
				>
					<Select
						:options="
							formState.isSpvNew == 'Y'
								? isother_subher
								: formState.isFinaHire == 'Y'
								  ? financial_company
								  : formState.isotherSubher == 'Y'
								    ? thepur_exist
								    : []
						"
						v-model:value="formState.thepurExist"
						:disabled="disabled ? disabled : formState.hasBusiness == 'Y'"
						@focus="
							() => {
								oldThepurExist = formState.thepurExist
							}
						"
						@select="
							(value) => {
								if (formState.isSpvNew == 'Y') {
									Modal.confirm({
										title: '提示',
										content:
											'SPV属性和存续目的不得随意变更，须提前与战投部业务处室沟通协商，擅自变更将追究责任，请确认是否已与业务处室协商一致？',
										okText: '是',
										cancelText: '否',
										onOk: () => {},
										onCancel: () => {
											formState.thepurExist = oldThepurExist
										}
									})
								}
							}
						"
						placeholder="请选择不实质开展经营机构的存续目的"
						allowClear
					/>
				</a-form-item>
			</template>
		</Collapse>
		<Collapse title="行业" :height="150">
			<template #titleIcon>
				<a-tooltip
					title="各机构年度营业收入占比低于5%的业务对应的行业小类、为保证本单位主要业务正常运转而进行的不对外提供产品和服务的辅助活动不纳入登记范围。工商登记的经营范围中未实际开展的业务不应填报行业类别。"
				>
					<QuestionCircleOutlined />
				</a-tooltip>
			</template>
			<template #content>
				<a-dropdown class="top-0 right-4 dropDown" style="position: absolute !important">
					<template #overlay>
						<a-menu>
							<a-menu-item key="0" @click="lookFileFn"> 预览 </a-menu-item>
							<a-menu-item key="1" @click="downFileFn" :disabled="downLoading">
								<template #icon> <LoadingOutlined v-if="downLoading" /> </template>
								下载
							</a-menu-item>
						</a-menu>
					</template>
					<a-button type="primary" :disabled="false">
						国民经济行业分类注释
						<DownloadOutlined />
					</a-button>
				</a-dropdown>
				<div class="flex gap-4 ml-4">
					<a-form-item
						class="w-[280px]"
						label="所属门类"
						name="industryCateg"
						:labelCol="{
							style: {
								width: '80px'
							}
						}"
						:wrapper-col="{
							span: 24
						}"
					>
						<Select
							:options="industry_categ"
							v-model:value="formState.industryCateg"
							placeholder="请选择所属门类"
							class="w-full"
							@change="
								() => {
									formState.industryLarge = null
									formState.industryMiddle = null
									formState.categoryCode = null
									let arr = findId(industryTreeData, formState.industryCateg)?.children || []
									industry_categ_lg = map(arr, (item) => {
										item.label = item.dictLabel
										item.value = item.dictValue
										return item
									})
								}
							"
						/>
					</a-form-item>
					<a-form-item
						class="w-[280px]"
						label="所属大类"
						name="industryLarge"
						:labelCol="{
							style: {
								width: '80px'
							}
						}"
						:wrapper-col="{
							span: 24
						}"
					>
						<Select
							:options="industry_categ_lg"
							v-model:value="formState.industryLarge"
							placeholder="请选择所属大类"
							class="w-full"
							@change="
								() => {
									formState.industryMiddle = null
									formState.categoryCode = null
									let arr = findId(industryTreeData, formState.industryLarge)?.children || []
									industry_categ_md = map(arr, (item) => {
										item.label = item.dictLabel
										item.value = item.dictValue
										return item
									})
								}
							"
						/>
					</a-form-item>
					<a-form-item
						class="w-[280px]"
						label="所属中类"
						name="industryMiddle"
						:labelCol="{
							style: {
								width: '80px'
							}
						}"
						:wrapper-col="{
							span: 24
						}"
					>
						<Select
							:options="industry_categ_md"
							v-model:value="formState.industryMiddle"
							placeholder="请选择所属中类"
							class="w-full"
							@change="
								() => {
									formState.categoryCode = null
									arr = findId(industryTreeData, formState.industryMiddle)?.children || []
									console.log(arr)
									industry_categ_sm = map(arr, (item) => {
										item.name = item.dictLabel
										item.code = item.dictValue
										return item
									})
								}
							"
						/>
					</a-form-item>
					<a-form-item
						class="w-[280px]"
						label="所属小类"
						name="categoryCode"
						:labelCol="{
							style: {
								width: '80px'
							}
						}"
						:wrapper-col="{
							span: 24
						}"
					>
						<Select
							:options="industry_categ_sm"
							v-model:value="formState.categoryCode"
							:fieldNames="{ label: 'name', value: 'code', children: 'children' }"
							placeholder="请选择所属小类"
							class="w-full"
							@change="
								(value) => {
									if (value) {
										const sm = findId(industryTreeData, value)
										const md = findId(industryTreeData, sm.parentId, 'id')
										industry_categ_md = [{ label: md.dictLabel, value: md.dictValue }]
										formState.industryMiddle = md.dictValue

										const lg = findId(industryTreeData, md.parentId, 'id')
										industry_categ_lg = [{ label: lg.dictLabel, value: lg.dictValue }]
										formState.industryLarge = lg.dictValue

										const mx = findId(industryTreeData, lg.parentId, 'id')
										formState.industryCateg = mx.dictValue
									} else {
										formState.industryMiddle = null
										formState.industryLarge = null
										formState.industryCateg = null
									}
								}
							"
						/>
					</a-form-item>
					<!-- v-if="!disabled" -->
					<a-button type="primary" @click="addRow" v-if="!disabled" :disabled="categoryAddBtn">新增</a-button>
					<a-button
						type="primary"
						@click="
							() => {
								if (
									formState.entitycategoryList.length > 0 &&
									filter(formState.entitycategoryList, (item) => item.isEdit).length > 0
								) {
									return message.warning('行业信息表存在未保存的数据！')
								}
								getSmOptions()
							}
						"
						v-if="!disabled"
					>
						推荐分类
					</a-button>
				</div>
				<a-row :gutter="16" :span="24" class="mb-2">
					<a-col :span="24">
						<a-row :gutter="16" :span="24">
							<a-col :span="24">
								<a-table
									:columns="columns"
									:data-source="formState.entitycategoryList"
									:pagination="false"
									size="small"
									:row-key="(record) => record.id"
									:scroll="{ x: 1000 }"
								>
									<template #bodyCell="{ column, record, index }">
										<template v-if="column.dataIndex === 'action'">
											<a-space style="gap: 0px">
												<template #split>
													<a-divider type="vertical" />
												</template>
												<a-button
													type="link"
													danger
													size="small"
													v-if="record?.isEdit"
													@click="handleSave(record, index)"
												>
													保存
												</a-button>
												<a-button type="link" danger size="small" v-else @click="editRow(record, index)">
													编辑
												</a-button>
												<a-popconfirm title="确定要删除吗？" @confirm="delRow(record, index)">
													<a-button type="link" danger size="small">删除</a-button>
												</a-popconfirm>
											</a-space>
										</template>
									</template>
								</a-table>
							</a-col>
						</a-row>
					</a-col>
				</a-row>
				<a-form-item
					label="行业文件上传"
					name="cateFile"
					:wrapper-col="{
						span: 24
					}"
					v-if="props.tabActive == 3"
				>
					<OrderUpload v-model:value="formState.cateFile" uploadText="请上传行业信息相关文件" :readonly="disabled" />
				</a-form-item>
			</template>
		</Collapse>

		<Collapse title="处置信息" v-if="formState?.entityDisposalList?.length">
			<template #content>
				<div class="flex gap-4 ml-4"></div>
				<!-- <a-button class="mb-4" type="primary" @click="addRow2" v-if="!disabled">新增</a-button> -->
				<a-table
					:columns="disposalColumns"
					:data-source="formState.entityDisposalList"
					:pagination="false"
					size="small"
					:row-key="(record) => record.id"
				>
					<template #bodyCell="{ column, record, index }">
						<template v-if="column.dataIndex === 'action'">
							<!-- <a-space style="gap: 0px">
								<template #split>
									<a-divider type="vertical" />
								</template>
								<a-button type="link" danger size="small" v-if="record?.isEdit" @click="handleSave2(record)">
									保存
								</a-button>
								<a-button type="link" danger size="small" v-else @click="editRow2(record, index)"> 编辑 </a-button>
								<a-popconfirm title="确定要删除吗？" @confirm="delRow2(record, index)">
									<a-button type="link" danger size="small">删除</a-button>
								</a-popconfirm>
							</a-space> -->
						</template>
					</template>
				</a-table>
			</template>
		</Collapse>
		<Collapse title="特殊情况的参股公司" v-if="orgNatureIsShow">
			<template #content>
				<!-- 参股公司显示该区域，参股公司即填报标准为类别二 -->
				<a-form-item
					label="是否为[持股比例高于50%但不并表的机构]"
					name="isitanInsti"
					:labelCol="{
						style: {
							width: '270px'
						}
					}"
				>
					<Select
						:options="isOrNo"
						v-model:value="formState.isitanInsti"
						disabled
						allowClear
						placeholder="请选择是否为[持股比例高于50%但不并表的机构]"
					/>
				</a-form-item>
				<a-form-item
					label="是否使用中信品牌"
					name="isuseCiticb"
					:labelCol="{
						style: {
							width: '270px'
						}
					}"
				>
					<Select
						:options="isOrNo"
						v-model:value="formState.isuseCiticb"
						allowClear
						placeholder="请选择是否使用中信品牌"
					/>
				</a-form-item>
				<a-form-item
					label="中信方是否为第一大股东"
					name="isciticLargshared"
					:labelCol="{
						style: {
							width: '270px'
						}
					}"
				>
					<Select
						:options="isOrNo"
						v-model:value="formState.isciticLargshared"
						allowClear
						placeholder="请选择中信方是否为第一大股东"
					/>
				</a-form-item>
			</template>
		</Collapse>
		<Collapse
			title="审批信息"
			v-if="formState.apprType && formState.apprFilitime && formState.apprRequname && formState.apprFinalfile"
		>
			<template #content>
				<a-form-item label="审批类型" name="apprType" v-if="formState.apprType">
					<Select :options="appr_type" v-model:value="formState.apprType" allowClear placeholder="请选择审批类型" />
				</a-form-item>
				<a-form-item label="审批备案时间" name="apprFilitime" v-if="formState.apprFilitime">
					<a-date-picker
						class="w-full"
						v-model:value="formState.apprFilitime"
						valueFormat="YYYY-MM-DD"
						allowClear
						placeholder="请选择审批备案时间"
					/>
				</a-form-item>
				<a-form-item label="审批请示名称" name="apprRequname" v-if="formState.apprRequname">
					<a-input v-model:value="formState.apprRequname" allowClear placeholder="请输入审批请示名称" />
				</a-form-item>
				<a-form-item
					label="最终决策文件"
					name="apprFinalfile"
					:wrapper-col="{
						span: 24
					}"
					v-if="apprFinalfileShow"
				>
					<OrderUpload
						ref="apprFinalfileRef"
						v-model:value="formState.apprFinalfile"
						uploadText="请上传最终决策文件"
						:readonly="disabled"
					/>
				</a-form-item>
			</template>
		</Collapse>
		<Collapse title="填报人信息">
			<template #content>
				<div class="grid grid-cols-2 gap-x-4">
					<a-form-item label="填报人姓名" name="filler">
						<a-input v-model:value="formState.filler" allowClear placeholder="请输入填报人姓名" />
					</a-form-item>
					<a-form-item label="E-mail" name="email">
						<a-input v-model:value="formState.email" allowClear placeholder="请输入填报人E-mail地址" />
					</a-form-item>
					<a-form-item label="移动电话" name="mobile">
						<a-input v-model:value="formState.mobile" allowClear placeholder="请输入填报人移动电话" />
					</a-form-item>
					<a-form-item label="联系电话" name="telephone">
						<a-input v-model:value="formState.telephone" allowClear placeholder="请输入填报人联系电话" />
					</a-form-item>
					<a-form-item label="所在企业" name="fillerCompanyId">
						<TreeSelect
							v-model:value="formState.fillerCompanyId"
							:tree-data="selectTreeOrg"
							:field-names="selectTreeOrgFieldNames"
							:dropdownMatchSelectWidth="500"
							placeholder="请选择所在企业"
							v-if="props.tabActive == 3"
						>
						</TreeSelect>
					</a-form-item>
				</div>
			</template>
		</Collapse>
		<div style="display: none">
			<a-form-item label="行业" name="entitycategoryList"> </a-form-item>
			<a-form-item label="行业删除" name="entitycategoryDeleteIds"> </a-form-item>
			<a-form-item label="处置信息" name="entityDisposalList"> </a-form-item>
		</div>
	</a-form>
	<a-modal
		v-model:open="open"
		title="国民经济行业分类注释"
		@ok="handleOk"
		width="100%"
		wrap-class-name="full-modal"
		:bodyStyle="{ overflowY: 'auto' }"
	>
		<XnFilePreview :src="previewFileInfo.src" :fileType="previewFileInfo.fileType" @goBack="handleOk">
			<template #btnBox>
				<div></div>
			</template>
		</XnFilePreview>
	</a-modal>
</template>
<script setup lang="jsx">
	import { message, Modal } from 'ant-design-vue'
	import { cloneDeep, map, filter } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import bus from '@/utils/bus'
	import tool from '@/utils/tool'
	import bizEntitycategoryApi from '@/api/biz/bizEntitycategoryApi.js'
	import downloadUtil from '@/utils/downloadUtil.js'
	import XnFilePreview from '@/components/XnFilePreview/index.vue'
	import { useOrgTreeStore } from '@/store'
	const OrgTreeStore = useOrgTreeStore()

	const formRef = ref()
	bus.on('shareholderForm', (data) => {
		formState.value.fillingStandards = data
		if (data == 'filling_standards_2') formState.value.consolidationParentId = null
	})
	bus.on('isitanInstiChange', (data) => {
		formState.value.isitanInsti = data
	})
	const orgNatureIsShow = computed(() => {
		if (formState.value.fillingStandards == 'filling_standards_2') {
			formState.value.consolidationParentId = null
			formRef.value.clearValidate()
		}
		return formState.value.fillingStandards == 'filling_standards_2'
	})
	const formState = ref({
		entitycategoryList: [],
		entityDisposalList: []
	})
	const isOrNo = tool.dictList('WHETHER')
	const sector = tool.dictList('sector')
	const belong_sector = tool.dictList('belong_sector')
	const legal_entity_level = tool.dictList('legal_entity_level')
	const management_level = tool.dictList('management_level')
	const thepur_exist = tool.dictList('thepur_exist')
	const financial_company = tool.dictList('financial_company')
	// spv
	const isother_subher = tool.dictList('isother_subher')
	const cancel_progress = tool.dictList('cancel_progress')
	const cancel_type = tool.dictList('cancel_type')
	const cancel_cause = tool.dictList('cancel_cause')
	const loss_type = tool.dictList('loss_type')
	const appr_type = tool.dictList('appr_type')
	const industry_categ = tool.dictList('industry_categ')
	const dictTypeTree = tool.dictDataAll()
	const industry_categ_lg = ref([])
	const industry_categ_md = ref([])
	const industry_categ_sm = ref([])
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		disabled: {
			type: Boolean,
			default: false
		},
		isAdd: {
			type: Boolean,
			default: false
		},
		keys: {
			type: String,
			default: ''
		},
		entitycategoryList: {
			type: Array,
			default: () => []
		},
		tabActive: {
			type: String,
			default: ''
		}
	})

	const formRules = computed(() => {
		const obj = {
			belongSectorId: formState.value.manageCompany === '中信泰富有限公司' ? [required('请选择泰富板块')] : [],
			plate: [required('请选择集团板块')],
			orgParentunitId: [required('请选择上级管理单位')],
			consolidationParentId: orgNatureIsShow.value ? [] : [required('请选择上级并表单位')],
			// managementLevel: [required('请选择管理层级')],
			industryCateg: [required('请选择所属门类')],
			industryLarge: [required('请选择所属大类')],
			industryMiddle: [required('请选择所属中类')],
			categoryCode: [required('请选择所属小类')],
			cateFile: [required('请上传行业信息附件')],
			hasBusiness: [required('请选择是否实质开展经营')],
			isSpvNew: formState.value.hasBusiness == 'N' ? [required('请选择是否为SPV公司')] : [],
			isFinaHire: formState.value.hasBusiness == 'N' ? [required('请选择是否为金融租赁项目公司')] : [],
			isotherSubher: formState.value.hasBusiness == 'N' ? [required('请选择是否其他不实质开展经营公司')] : [],
			thepurExist: formState.value.hasBusiness == 'N' ? [required('请选择不实质开展经营机构的存续目的')] : [],

			apprType: [required('请选择审批类型')],
			apprFilitime: [required('请选择审批备案时间')],
			apprRequname: [required('请选择审批请示名称')],
			apprFinalfile: [required('请上传文件批复')],
			filler: [required('请输入填报人姓名')],
			email: [required('请输入email'), rules.email],
			mobile: [required('请输入手机号'), rules.phone],
			telephone: [required('请输入联系电话'), rules.fixedPhone],
			fillerCompanyId: [required('请选择所在企业')]
		}
		return obj
	})
	const oldLegalEntityLevel = ref('')
	const oldManagementLevel = ref('')
	const oldIsSpvNew = ref('')
	const oldThepurExist = ref('')
	const selectTreeOrg = ref(OrgTreeStore.selectTreeOrg)
	const filterOrgTree = ref(OrgTreeStore.selectTreeOrgFilling)
	const disposeTree = async (tree, name) => {
		for (let i = 0; i < tree.length; i++) {
			tree[i].disabled = tree[i][name] == 0
			if (tree[i]?.children) {
				disposeTree(tree[i]?.children)
			}
		}
	}
	const orgParentunitIdTreeData = computed(() => {
		let treeArr = cloneDeep(filterOrgTree.value)
		disposeTree(treeArr, 'isCheckManage')
		return treeArr || []
	})
	const consolidationParentIdTreeData = computed(() => {
		let treeArr = cloneDeep(filterOrgTree.value)
		disposeTree(treeArr, 'isCheckCons')
		return treeArr || []
	})
	const fillingStandardsComputed = computed(() => {
		return formState.value.fillingStandards == 'filling_standards_4'
	})
	watch(
		() => props.tabActive,
		(val) => {
			if (val == 3) {
				selectTreeOrg.value = OrgTreeStore.selectTreeOrg
				filterOrgTree.value = OrgTreeStore.selectTreeOrgFilling
			}
		}
	)
	const selectTreeOrgFieldNames = ref({
		children: 'children',
		label: 'simpOrgName',
		value: 'id'
	})
	// 表格
	const industryTreeData = dictTypeTree.find((item) => item.dictValue == 'industry_categ').children

	const findId = (data, code, searchKey = 'dictValue') => {
		for (const item of data) {
			if (item[searchKey] == code) return item
			if (item?.children?.length) {
				const found = findId(item.children, code, searchKey)
				if (found) return found
			}
		}
		return null
	}
	const fieldNames = ref({
		label: 'dictLabel',
		value: 'dictValue'
	})
	const categoryListValidate = () => {
		const arr = filter(formState.value.entitycategoryList, (item) => item.industryCateg == 'NO1')
		if (formState.value.entitycategoryList.length > 1 && arr.length) {
			Modal.confirm({
				title: '提示',
				content: '是否删除其他行业信息?',
				okText: '是',
				cancelText: '否',
				onOk: () => {
					formState.value.entitycategoryList = arr
				}
			})
		}
	}
	const total = computed(() => {
		let sum = 0
		formState.value.entitycategoryList.forEach((item) => {
			sum += Number(item.revenueProportion)
		})
		return sum
	})
	const columns = computed(() => {
		let arr = [
			{
				title: '序号',
				dataIndex: 'isEdit',
				ellipsis: true,
				width: 80,
				fixed: 'left',
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '行业门类',
				dataIndex: 'industryCateg',
				width: 180,
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					if (record.isEdit) {
						return (
							<Select
								class="w-full"
								v-model:value={record.industryCateg}
								options={industry_categ}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择行业门类"
								allowClear
								onChange={() => {
									if (record.industryCateg == 'NO1') {
										record.industryLarge = 'NO11'
										record.industryMiddle = 'NO111'
										record.categoryCode = 'NO1111'
									} else {
										record.industryLarge = null
										record.industryMiddle = null
										record.categoryCode = null
									}
								}}
							/>
						)
					} else {
						return industry_categ.find((item) => item.value == text)?.label
					}
				}
			},
			{
				title: '行业大类',
				dataIndex: 'industryLarge',
				width: 180,
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					const options = findId(industryTreeData, record.industryCateg)?.children || []
					if (record.isEdit) {
						return (
							<Select
								class="w-full"
								v-model:value={record.industryLarge}
								options={options}
								fieldNames={fieldNames.value}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择行业大类"
								allowClear
								disabled={!record.industryCateg}
							/>
						)
					} else {
						return options.find((item) => item.dictValue == text)?.dictLabel
					}
				}
			},
			{
				title: '行业中类',
				dataIndex: 'industryMiddle',
				width: 180,
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					const options = findId(industryTreeData, record.industryLarge)?.children || []
					if (record.isEdit) {
						return (
							<Select
								class="w-full"
								v-model:value={record.industryMiddle}
								options={options}
								fieldNames={fieldNames.value}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择行业中类"
								allowClear
								disabled={!record.industryLarge}
							/>
						)
					} else {
						return options.find((item) => item.dictValue == text)?.dictLabel
					}
				}
			},
			{
				title: '行业小类',
				dataIndex: 'categoryCode',
				width: 180,
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					const options = findId(industryTreeData, record.industryMiddle)?.children || []
					record.categoryCodeName = options.find((item) => item.dictValue == text)?.dictLabel
					if (record.isEdit) {
						return (
							<Select
								class="w-full"
								v-model:value={record.categoryCode}
								options={options}
								fieldNames={fieldNames.value}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择行业小类"
								onChange={() => {
									console.log(record.categoryCode)
									record.categoryCodeName = options.find((item) => item.dictValue == text)?.dictLabel
								}}
								disabled={!record.industryMiddle}
								allowClear
							/>
						)
					} else {
						return options.find((item) => item.dictValue == text)?.dictLabel
					}
				}
			},
			{
				title: '最近一年经审计后的营业收入',
				dataIndex: 'operatingRevenue',
				ellipsis: true,
				width: 250,
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input-number
							v-model:value={record.operatingRevenue}
							min={0}
							style="margin-left:-12px;"
							placeholder="请输入最近一年经审计后的营业收入"
							allowClear
						/>
					) : (
						text
					)
					return html
				}
			},
			{
				title: '营收占比(%)',
				dataIndex: 'revenueProportion',
				width: 120,
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input-number
							v-model:value={record.revenueProportion}
							min={0}
							max={100}
							style="margin-left:-12px;"
							placeholder="请输入营收占比"
							allowClear
						/>
					) : (
						text
					)
					return html
				}
			}
			// {
			// 	title: '文件上传',
			// 	dataIndex: 'cateFile',
			// 	ellipsis: true,
			// 	width: 300,
			// 	customRender: ({ text, record }) => {
			// 		let html = (
			// 			<XnUpload
			// 				v-model:value={record.cateFile}
			// 				uploadMode="file"
			// 				update:value={record.cateFile}
			// 				uploadDynamicReturnUrlApi="/api/webapp/dev/file/uploadDynamicReturnUrl"
			// 				uploadNumber={100}
			// 				uploadResultCategory="array"
			// 				completeResult={true}
			// 				disabled={!record.isEdit}></XnUpload>
			// 		)
			// 		return html
			// 	}
			// }
		]
		if (!props.disabled) {
			arr.push({ title: '操作', dataIndex: 'action', align: 'center', width: 150, fixed: 'right' })
		}
		return arr
	})
	const disposalColumns = computed(() => {
		let arr = [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '退出时间',
				dataIndex: 'exitTime',
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-date-picker
							v-model:value={record.exitTime}
							valueFormat="YYYY-MM-DD"
							placeholder="请选择时间"
							style="width:100%;margin-left:-12px;"
							allowClear
						/>
					) : (
						text.slice(0, 10)
					)
					return html
				}
			},
			{
				title: '退出程度',
				dataIndex: 'exitDegree',
				customRender: ({ text, record }) => {
					if (record.isEdit) {
						return (
							<Select
								v-model:value={record.exitDegree}
								options={cancel_progress}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择退出程度"
								allowClear
							/>
						)
					} else {
						return cancel_progress.find((o) => o.value == text)?.label
					}
				}
			},
			{
				title: '退出方式',
				dataIndex: 'exitWay',
				customRender: ({ text, record }) => {
					if (record.isEdit) {
						return (
							<Select
								v-model:value={record.exitWay}
								options={cancel_type}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择退出方式"
								allowClear
							/>
						)
					} else {
						return cancel_type.find((o) => o.value == text)?.label
					}
				}
			},
			{
				title: '退出原因',
				dataIndex: 'exitReason',
				customRender: ({ text, record }) => {
					if (record.isEdit) {
						return (
							<Select
								v-model:value={record.exitReason}
								options={cancel_cause}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择退出原因"
								allowClear
							/>
						)
					} else {
						return cancel_cause.find((o) => o.value == text)?.label
					}
				}
			},

			{
				title: '备注',
				dataIndex: 'remark',
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input v-model:value={record.remark} style="margin-left:-12px;" placeholder="请输入备注" allowClear />
					) : (
						text
					)
					return html
				}
			},
			{
				title: '证明文件',
				dataIndex: 'proveFile',
				width: 300,
				customRender: ({ text, record }) => {
					let html = !record.proveFile ? (
						''
					) : (
						<OrderUpload
							v-if={props.tabActive == 3 && record.proveFile}
							v-model:value={record.proveFile}
							uploadText=""
							bgColor=""
							readonly={true}
						/>
					)
					return html
				}
			}
		]
		// if (!props.disabled) {
		// 	arr.push({ title: '操作', dataIndex: 'action', align: 'center', width: 150 })
		// }
		return arr
	})
	const addRow = () => {
		if (total.value >= 100) return message.error('营收占比不能超过100!')
		if (formState.value.entitycategoryList.filter((item) => item.industryCateg == 'NO1').length)
			return message.warning('只能保留一条行业门类是无的数据！请修改数据')
		formState.value.entitycategoryList.push({
			industryCateg: null,
			industryLarge: null,
			industryMiddle: null,
			categoryCode: null,
			categoryCodeName: null,
			operatingRevenue: 0,
			revenueProportion: 0,
			isEdit: true
		})
	}
	const categoryAddBtn = computed(() => {
		const NO11Arr = filter(formState.value.entitycategoryList, (item) => item.industryCateg == 'NO1')
		const L7212Arr = filter(formState.value.entitycategoryList, (item) => item.categoryCode == 'L7212')

		return NO11Arr.length || total.value >= 100 || formState.value.isSpvNew == 'Y'
	})
	const getSmOptions = async () => {
		bizEntitycategoryApi
			.categoryCodeList({ shEntityCategoryVOs: formState.value.entitycategoryList })
			.then((res) => {
				// formState.value.categoryCode = null

				console.log(industry_categ_sm.value)
				if (res.length == 1) {
					industry_categ_sm.value = res
					formState.value.categoryCode = res[0].code
					const sm = findId(industryTreeData, formState.value.categoryCode)
					const md = findId(industryTreeData, sm.parentId, 'id')
					industry_categ_md.value = [{ label: md.dictLabel, value: md.dictValue }]
					formState.value.industryMiddle = md.dictValue

					const lg = findId(industryTreeData, md.parentId, 'id')
					industry_categ_lg.value = [{ label: lg.dictLabel, value: lg.dictValue }]
					formState.value.industryLarge = lg.dictValue

					const mx = findId(industryTreeData, lg.parentId, 'id')
					formState.value.industryCateg = mx.dictValue
				} else if (res.length > 1) {
					let obj = {}
					res.map((item) => {
						obj[item.code] = item
					})
					industry_categ_sm.value = []
					Object.keys(obj).map((key) => {
						industry_categ_sm.value.push(obj[key])
					})
					message.success('存在多条推荐分类，请手动选择！')
				}
			})
			.catch((e) => {
				console.log(e)
			})
	}
	const handleSave = async (record, index) => {
		if (!record.industryCateg) return message.error('请选择行业门类!')
		if (!record.industryLarge) return message.error('请选择行业大类!')
		if (!record.industryMiddle) return message.error('请选择行业中类!')
		if (!record.categoryCode) return message.error('请选择行业小类!')
		if (formState.value.isSpvNew != 'Y' && record.industryCateg != 'NO1' && record.categoryCode != 'L7211') {
			if (record.revenueProportion < 5) return message.warning('该行营收占比小于5，无需填报行业明细信息，请删除本行!')
		}
		if (record.industryCateg != 'NO1') {
			if (!record.operatingRevenue) return message.error('请输入营业收入!')
		}
		if (total.value > 100) return message.error('营收占比不能超过100!')
		const NO11Arr = filter(formState.value.entitycategoryList, (item) => item.industryCateg == 'NO1')
		const L7212Arr = filter(formState.value.entitycategoryList, (item) => item.categoryCode == 'L7212')
		if (NO11Arr.length == 1 || L7212Arr.length == 1) getSmOptions()
		if (formState.value.entitycategoryList.length > 1 && (NO11Arr.length || L7212Arr.length)) {
			Modal.confirm({
				title: '提示',
				content: '是否删除其他行业信息?',
				okText: '是',
				cancelText: '否',
				onOk: () => {
					let arr =
						record.industryCateg == 'NO1'
							? NO11Arr
							: record.categoryCode == 'L7212'
							  ? L7212Arr
							  : formState.value.entitycategoryList

					let delArr = formState.value.entitycategoryList.filter((item) => {
						if (item.id != arr[0].id && item.categoryCode != arr[0].categoryCode) return item
					})
					delArr.map((item) => {
						if (item.id) formState.value.entitycategoryDeleteIds.push(item.id)
					})
					formState.value.entitycategoryList = [arr[0]]
					getSmOptions()
					record.isEdit = false
				},
				onCancel: () => {}
			})
		} else {
			record.isEdit = false
		}
	}
	const editRow = (record, index) => {
		formState.value.entitycategoryList[index]['isEdit'] = true
	}
	const delRow = (record, index) => {
		if (record.id) formState.value?.entitycategoryDeleteIds.push(record.id)
		formState.value.entitycategoryList.splice(index, 1)
		if (formState.value.entitycategoryList.length == 0) {
			formState.value.industryCateg = null
			formState.value.industryLarge = null
			formState.value.industryMiddle = null
			formState.value.categoryCode = null
			industry_categ_sm.value = []
			return
		}
		getSmOptions()
	}
	const addRow2 = () => {
		formState.value.entityDisposalList.push({
			id: '',
			exitTime: '',
			exitDegree: null,
			exitWay: null,
			exitReason: null,
			remark: '',
			isEdit: true
		})
	}
	const handleSave2 = async (record) => {
		record.isEdit = false
	}
	const editRow2 = (record, index) => {
		record.isEdit = true
	}
	const delRow2 = (record, index) => {
		formState.value.entityDisposalList.splice(index, 1)
	}
	const submitForm = async () => {
		return new Promise((resolve, reject) => {
			Object.keys(formState.value).forEach((key) => {
				if (formState.value[key] && typeof formState.value[key] === 'string')
					formState.value[key] = formState.value[key].trim()
			})
			const errObj = { key: props.keys }
			if (
				formState.value.hasBusiness == 'N' &&
				formState.value.isSpvNew == 'N' &&
				formState.value.isFinaHire == 'N' &&
				formState.value.isotherSubher == 'N'
			) {
				message.warning('是否为SPV公司、是否为金融租赁项目公司、是否其他不实质开展经营公司必须有一个为：是')
				formRef.value.scrollToField('hasBusiness')
				return reject(errObj)
			}
			formRef.value
				.validate()
				.then(() => {
					const form = cloneDeep(formRef.value.getFieldsValue())

					if (form.entitycategoryList?.length < 1) {
						message.warning('请添加行业信息!')
						formRef.value.scrollToField('categoryCode')
						return reject(errObj)
					}
					if (form.entitycategoryList.filter((item) => item.isEdit == true)?.length) {
						message.error('行业存在未保存的信息!')
						formRef.value.scrollToField('categoryCode')
						return reject(errObj)
					}
					if (total.value > 100) {
						message.error('行业信息营收占比不能超过100!')
						formRef.value.scrollToField('categoryCode')
						return reject(errObj)
					}
					// let cateFileNum = 0
					// form.entitycategoryList.map((item) => {
					// 	if (item.cateFile?.length > 0) cateFileNum += 1
					// })
					// if (!cateFileNum) {
					// 	message.warning('行业信息表内缺少附件，请在任意一行上传附件!')
					// 	formRef.value.scrollToField('categoryCode')
					// 	return reject(errObj)
					// }
					form.entitycategoryList = form.entitycategoryList.map((item, index) => {
						// item.cateFile = JSON.stringify(item.cateFile)
						item.sortCode = index + 1
						return item
					})
					form.entityDisposalList = form.entityDisposalList.filter(
						(item) => item.exitTime || item.exitDegree || item.exitWay || item.exitReason || item.remark
					)
					form.entityDisposalList.map((item, index) => {
						item.sortCode = index + 1
						return item
					})
					form.entitycategoryDeleteIds = form.entitycategoryDeleteIds?.join(',') || ''
					formRef.value.clearValidate()
					resolve(form)
				})
				.catch((error) => {
					console.log(error)
					if (error.errorFields?.length) {
						message.warning(error.errorFields[0].errors[0])
						formRef.value && formRef.value.scrollToField(error.errorFields[0].name[0])
					}
					error.key = props.keys
					reject(error)
				})
		})
	}
	const apprFinalfileShow = computed(() => {
		return props.tabActive == 3 && formState.value.apprFinalfile
	})
	const apprFinalfileRef = ref()
	// 注入注册方法
	const { register, unregister } = inject('validatorRegistry')
	onMounted(() => {
		register(props.keys, submitForm)
	})
	onUnmounted(() => {
		unregister(props.keys)
	})
	const lookLoading = ref(false)
	const downLoading = ref(false)
	const open = ref(false)
	const previewFileInfo = ref({
		src: '',
		fileType: 'xlsx',
		name: '国民经济行业分类注释'
	})
	const lookFileFn = () => {
		open.value = true
		downFileFn(false)
	}
	const downFileFn = (type = true) => {
		type ? (downLoading.value = true) : (lookLoading.value = true)
		bizEntitycategoryApi
			.downFile()
			.then((res) => {
				type ? downloadUtil.resultDownload(res) : (previewFileInfo.value.src = res.data)
			})
			.finally(() => {
				lookLoading.value = false
				downLoading.value = false
			})
	}
	const handleOk = () => {
		open.value = false
		previewFileInfo.value.src = ''
	}
	const selectNFn = () => {
		if (
			formState.value.isSpvNew == 'N' &&
			formState.value.isFinaHire == 'N' &&
			formState.value.isotherSubher == 'N' &&
			formState.value.hasBusiness == 'N'
		) {
			message.warning('是否为SPV公司、是否为金融租赁项目公司、是否其他不实质开展经营公司必须有一个为：是')
		}
	}
	watch(
		() => props.isAdd,
		(val) => {
			if (val) industry_categ_sm.value = []
		}
	)
	watch(
		() => props.data,
		(val) => {
			formState.value = cloneDeep(val)
			formState.value.entitycategoryDeleteIds = cloneDeep(val?.entitycategoryDeleteIds || [])
			if (formState.value.categoryCode) {
				const sm = findId(industryTreeData, formState.value.categoryCode)
				industry_categ_sm.value = [{ name: sm.dictLabel, code: sm.dictValue }]
				const md = findId(industryTreeData, formState.value.industryMiddle)
				industry_categ_md.value = [{ label: md.dictLabel, value: md.dictValue }]
				const lg = findId(industryTreeData, formState.value.industryLarge)
				industry_categ_lg.value = [{ label: lg.dictLabel, value: lg.dictValue }]
			}
			formRef.value.clearValidate()
		},
		{ immediate: true, deep: true }
	)
	const clearValidate = () => {
		formRef.value.clearValidate()
	}
	defineExpose({
		formState,
		clearValidate
	})
</script>
<style lang="less" scoped>
	.dropDown {
		position: absolute;
	}
</style>

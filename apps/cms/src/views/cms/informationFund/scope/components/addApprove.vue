<template>
	<xn-form-container
		title="信息校验"
		:width="1000"
		:visible="visible"
		:destroy-on-close="true"
		:maskClosable="false"
		@close="onClose"
	>
		<div class="flex-1">
			<a-table
				ref="tableRef"
				:scroll="{ y: 500 }"
				:pagination="false"
				:columns="columns"
				:data-source="tableData"
				:row-key="(record) => record.name"
				:row-selection="table.rowSelection"
				bordered
			>
				<template #bodyRender="{ column, record }">
					<!-- <template v-if="column.dataIndex === 'category'">
						{{ $TOOL.dictTypeData('ORG_CATEGORY', record.category) }}
					</template> -->
				</template>
			</a-table>
		</div>
		<template #footer>
			<a-button type="primary" class="xn-mr8" @click="onSubmit">确认填报</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="addApprove" lang="jsx">
	import { map, forEach, find } from 'lodash-es'
	import tool from '@/utils/tool'
	const currency = tool.dictList('currency')
	const emit = defineEmits(['successful'])
	const Registration = tool.dictList('Registration Area')
	const arr = ref([])
	forEach(Registration, (item) => {
		if (item?.children) arr.value = [...arr.value, ...item.children]
	})
	// 默认是关闭状态
	const visible = ref(false)
	const tableData = ref([])
	const tableRef = ref()
	// 列表选择配置
	const columns = [
		{
			title: '字段',
			dataIndex: 'title',
			width: 150
		},
		{
			title: '系统信息',
			dataIndex: 'oldValue',
			customRender: ({ record, text }) => {
				let str = text
				if (record.name == 'registcurrency') {
					str = find(currency, (item) => item.value === text)?.label
				} else if (record.name == 'registarea2') {
					str = find(arr.value, (item) => item.value === text)?.label || str
				} else if (record.name == 'establishedDate') {
					str = str.substring(0, 10)
				}
				return str
			}
		},
		{
			title: '工商信息',
			dataIndex: 'value',
			customRender: ({ record, text }) => {
				let str = text
				if (record.name == 'registcurrency') {
					str = find(currency, (item) => item.value === text)?.label
				} else if (record.name == 'registarea2') {
					str = find(arr.value, (item) => item.value === text)?.label || str
				}
				return str
			}
		}
	]
	const selectedRowKeys = ref([])
	const selectedRowsArr = ref([])
	const selectedRowsObj = ref({})
	// 列表选择配置
	const table = {
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
				selectedRowsArr.value = selectedRows
			}
		}
	}
	// 打开抽屉
	const onOpen = (record) => {
		visible.value = true
		tableData.value = record
		console.log(record)
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		selectedRowKeys.value = []
		selectedRowsArr.value = []
		selectedRowsObj.value = {}
	}
	// 重置
	const reset = () => {
		tableRef.value.refresh(true)
	}
	const onSubmit = () => {
		map(selectedRowsArr.value, (item) => {
			selectedRowsObj.value[item.name] = item.value
		})
		emit('successful', selectedRowsObj.value)
		onClose()
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style lang="less" scoped>
	.ant-modal-body {
		display: flex;
		flex-flow: column nowrap;
	}
</style>

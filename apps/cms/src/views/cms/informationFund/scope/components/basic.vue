a-form-itema-form-item
<template>
	<!-- 基本信息 -->
	<a-form
		ref="formRef"
		:labelCol="{
			style: {
				width: '150px'
			}
		}"
		:wrapper-col="{
			span: 10
		}"
		:model="formState"
		:rules="formRules"
		:scrollToFirstError="true"
		:disabled="disabled"
	>
		<Collapse title="基本信息">
			<template #content>
				<a-form-item
					ref="simpOrgName"
					label="企业全称（简体中文）"
					name="simpOrgName"
					:wrapper-col="{
						span: 10
					}"
				>
					<div class="flex gap-1 items-center">
						<a-input v-model:value="formState.simpOrgName" allowClear placeholder="请输入企业全称（简体中文）">
							<!-- @change="formState.tradOrgName = convertText(formState.simpOrgName)" -->
						</a-input>
						<a-button
							v-if="!disabled"
							type="primary"
							size="small"
							style="font-size: 12px"
							@click="formState.tradOrgName = convertText(formState.simpOrgName)"
						>
							生成繁体
						</a-button>
						<!-- <a-checkbox v-model:checked="checked"><div class="w-[180px]">不纳入登记范围管理</div> </a-checkbox> -->
					</div>
				</a-form-item>
				<a-form-item ref="tradOrgName" label="企业全称（繁体中文）" name="tradOrgName">
					<div class="flex gap-1 items-center">
						<a-input v-model:value="formState.tradOrgName" disabled allowClear placeholder="请输入企业全称（繁体中文）">
							<!-- @change="formState.simpOrgName = convertText(formState.tradOrgName, false)" -->
						</a-input>
						<a-button
							v-if="!disabled"
							type="primary"
							size="small"
							style="font-size: 12px"
							@click="formState.simpOrgName = convertText(formState.tradOrgName, false)"
						>
							生成简体
						</a-button>
					</div>
				</a-form-item>
				<a-form-item ref="foreOrgName" label="企业全称（外文）" name="foreOrgName">
					<a-input v-model:value="formState.foreOrgName" disabled allowClear placeholder="请输入企业全称（外文）" />
				</a-form-item>
				<div class="grid grid-cols-3 gap-4">
					<a-form-item
						ref="registarea1"
						label="注册地区"
						name="registarea1"
						:wrapper-col="{
							span: 24
						}"
					>
						<Select
							class="w-full"
							:options="Registration"
							v-model:value="formState.registarea1"
							@change="
								(value) => {
									console.log(value)
									if (value == 'RA200000' || value == 'RA300000') {
										formState.legalPersonName = '不适用'
									} else {
										formState.legalPersonName = ''
									}
									formState.registarea2 = null
									formState.registtype = value == 'RA100000' ? 'LT01' : null
									formState.registcode = ''
								}
							"
							placeholder="请选择注册地区1"
							disabled
						/>
					</a-form-item>
					<a-form-item
						ref="registarea2"
						label=""
						name="registarea2"
						:wrapper-col="{
							span: 15
						}"
					>
						<Select
							:options="Registration.find((item) => item.value === formState.registarea1)?.children || []"
							v-model:value="formState.registarea2"
							placeholder="请选择地区2"
							disabled
						/>
					</a-form-item>
				</div>
				<a-row>
					<a-col :span="12"> </a-col>
				</a-row>

				<a-form-item ref="registrationLocation" label="住所" name="registrationLocation">
					<a-input v-model:value="formState.registrationLocation" disabled allowClear placeholder="请输入住所" />
				</a-form-item>
				<a-row>
					<a-col :span="12">
						<a-form-item ref="registtype" label="注册证照类型" name="registtype">
							<Select
								:options="License"
								v-model:value="formState.registtype"
								allowClear
								placeholder="请选择注册证照类型"
								@change="
									(value) => {
										console.log(value)
									}
								"
								disabled
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item ref="registcode" label="注册证照代码" name="registcode">
							<a-input v-model:value="formState.registcode" disabled allowClear placeholder="请输入注册证照代码" />
						</a-form-item>
					</a-col>
				</a-row>
				<a-form-item ref="legalPersonName" label="法定代表人" name="legalPersonName">
					<a-input v-model:value="formState.legalPersonName" disabled allowClear placeholder="请输入法定代表人" />
				</a-form-item>
				<a-row>
					<a-col :span="12">
						<a-form-item ref="establishedDate" label="成立时间" name="establishedDate">
							<a-date-picker
								class="w-full"
								v-model:value="formState.establishedDate"
								valueFormat="YYYY-MM-DD"
								allowClear
								placeholder="请选择成立时间"
								@change="console.log(formState.establishedDate)"
								disabled
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item ref="enterpriseNatureNew" label="类型" name="enterpriseNatureNew">
							<a-input v-model:value="formState.enterpriseNatureNew" disabled allowClear placeholder="请输入类型" />
						</a-form-item>
					</a-col>
				</a-row>
				<a-row>
					<a-col :span="12">
						<a-form-item ref="registcurrency" label="注册资本币种" name="registcurrency">
							<Select
								:options="currency"
								v-model:value="formState.registcurrency"
								disabled
								placeholder="请选择注册资本币种"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item ref="registeredCapital" label="注册资本" name="registeredCapital">
							<a-input-number
								class="w-full"
								v-model:value="formState.registeredCapital"
								:min="0"
								:precision="2"
								:step="0.01"
								:stringMode="true"
								allowClear
								placeholder="请以个位为单位进行填报"
								:formatter="formatNumberWithThoundsandSeparator"
								:parser="(value) => value.replace(/,/g, '')"
								addon-after="元"
								disabled
							/>
						</a-form-item>
					</a-col>
				</a-row>
				<a-form-item
					ref="mainBusiness"
					label="经营范围"
					name="mainBusiness"
					:wrapper-col="{
						span: 17
					}"
				>
					<a-textarea
						v-model:value="formState.mainBusiness"
						auto-size
						allowClear
						placeholder="请按照营业执照或公司注册文件填写"
					/>
				</a-form-item>
				<a-form-item ref="isListed" label="是否上市" name="isListed">
					<Select
						:options="isOrNo"
						v-model:value="formState.isListed"
						@change="
							(value) => {
								if (value == 'N' && formState.entityListedList.length > 0) {
									Modal.confirm({
										title: '取消上市',
										content: '取消后，将无法恢复上市信息，是否确认取消上市？',
										okText: '确认',
										onCancel: () => {
											formState.isListed = 'Y'
										},
										onOk: () => {
											formState.entityListedList.map((item) => {
												if (item.id) formState.entityListedDeleteIds.push(item.id)
											})
											formState.entityListedDeleteIds = [...new Set(formState.entityListedDeleteIds)]
											formState.entityListedList = []
										}
									})
								}
							}
						"
						placeholder="请选择是否上市"
						disabled
					/>
				</a-form-item>
				<a-form-item
					label="上市信息"
					:wrapper-col="{
						span: 24
					}"
					v-if="formState.isListed == 'Y'"
				>
					<a-button
						class="mb-2"
						type="primary"
						@click="listedAddRow"
						v-if="!disabled && formState.isListed == 'Y'"
						:disabled="formState.isListed != 'Y'"
						>新增</a-button
					>
					<a-table
						:columns="listedColumns"
						:data-source="formState.entityListedList"
						:pagination="false"
						size="small"
						:row-key="(record) => record.id"
					>
						<template #bodyCell="{ column, record, index }">
							<template v-if="column.dataIndex === 'action'">
								<a-space style="gap: 0">
									<template #split>
										<a-divider type="vertical" />
									</template>
									<a-button type="link" danger size="small" v-if="record?.isEdit" @click="listedHandleSave(record)">
										保存
									</a-button>
									<a-button type="link" danger size="small" v-else @click="listedEditRow(record, index)">
										编辑
									</a-button>
									<a-popconfirm title="确定要删除吗？" @confirm="listedDelRow(record, index)">
										<a-button type="link" danger size="small">删除</a-button>
									</a-popconfirm>
								</a-space>
							</template>
						</template>
					</a-table>
				</a-form-item>
				<a-form-item
					label="公司章程"
					name="artAssociat"
					:wrapper-col="{
						span: 24
					}"
				>
					<OrderUpload v-model:value="formState.artAssociat" uploadText="请上传公司章程" :readonly="disabled" />
				</a-form-item>
				<div style="display: none">
					<a-form-item label="无董监高" name="noneManagerFlag"></a-form-item>
					<a-form-item label="董监高" name="entitymanagerList"></a-form-item>
					<a-form-item label="董监高删除" name="entitymanagerDeleteIds"></a-form-item>
					<a-form-item label="上市" name="entityListedList"></a-form-item>
					<a-form-item label="上市删除" name="entityListedDeleteIds"></a-form-item>
				</div>
			</template>
		</Collapse>

		<Collapse title="董监高">
			<template #content>
				<div orientation class="w-full gap-4 mb-2 flex items-center" v-if="!disabled">
					<a-button type="primary" @click="addRow">新增</a-button>
					<div class="flex-1 flex justify-start">
						<a-checkbox
							v-model:checked="yesOrNo"
							:disabled="formState.entitymanagerList.length > 0"
							@change="
								(e) => {
									formState.noneManagerFlag = e.target.checked ? 'Y' : 'N'
								}
							"
						>
							无董监高
						</a-checkbox>
					</div>
				</div>
				<a-table
					:columns="columns"
					:data-source="formState.entitymanagerList"
					:pagination="false"
					size="small"
					:row-key="(record) => record.id"
				>
					<template #bodyCell="{ column, record, index }">
						<template v-if="column.dataIndex === 'action'">
							<a-space style="gap: 0">
								<template #split>
									<a-divider type="vertical" />
								</template>
								<a-button type="link" danger size="small" v-if="record?.isEdit" @click="handleSave(record)">
									保存
								</a-button>
								<a-button type="link" danger size="small" v-else @click="editRow(record, index)"> 编辑 </a-button>
								<a-popconfirm title="确定要删除吗？" @confirm="delRow(record, index)">
									<a-button type="link" danger size="small">删除</a-button>
								</a-popconfirm>
								<a-button type="link" danger size="small" :disabled="index === 0" @click="onMove('prev', index)">
									上移
								</a-button>
								<a-button
									type="link"
									danger
									size="small"
									:disabled="index === formState.entitymanagerList.length - 1"
									@click="onMove('next', index)"
								>
									下移
								</a-button>
							</a-space>
						</template>
					</template>
				</a-table>
			</template>
		</Collapse>
	</a-form>
</template>
<script setup lang="jsx">
	import { message, Modal } from 'ant-design-vue'
	import { convertText } from '@/utils/opencc'
	import { map, cloneDeep } from 'lodash-es'
	import tool from '@/utils/tool'
	import { formatNumberWithThoundsandSeparator } from '@/utils/formatCurrency'
	const isOrNo = tool.dictList('WHETHER')
	const Registration = tool.dictList('Registration Area')
	const License_type = tool.dictList('License Type')
	const currency = tool.dictList('currency')
	const market_listed = tool.dictList('market_listed')
	const position = tool.dictList('position')
	const License = computed(() => {
		if (formState.value.registarea1 == 'RA100000') {
			License_type.map((item) => {
				item.disabled = !(item.value == 'LT01')
				return item
			})
		} else if (formState.value.registarea1 == 'RA200000' || formState.value.registarea1 == 'RA300000') {
			License_type.map((item) => {
				item.disabled = item.value == 'LT01'
				return item
			})
		}
		return License_type
	})
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		busineseForm: {
			type: Object,
			default: () => {}
		},
		disabled: {
			type: Boolean,
			default: false
		},
		keys: {
			type: String,
			default: ''
		}
	})
	const formRef = ref()
	const formState = ref({
		noneManagerFlag: false,
		entitymanagerList: [],
		entityListedList: []
	})

	const formRules = ref({
		simpOrgName: [{ required: true, message: '请输入企业全称（简体中文）', trigger: 'blur' }],
		tradOrgName: [{ required: true, message: '请输入企业全称（繁体中文）', trigger: 'blur' }],
		foreOrgName: [{ required: true, message: '请输入企业全称（外文）', trigger: 'blur' }],
		registarea1: [{ required: true, message: '请选择注册地区1', trigger: 'blur' }],
		registarea2: [{ required: true, message: '请选择注册地区2', trigger: 'blur' }],
		registrationLocation: [{ required: true, message: '请输入住所', trigger: 'blur' }],
		registtype: [{ required: true, message: '请选择注册证照类型', trigger: 'blur' }],
		registcode: [{ required: true, message: '请输入注册证照代码', trigger: 'blur' }],
		legalPersonName: [{ required: true, message: '请输入法定代表人', trigger: 'blur' }],
		establishedDate: [{ required: true, message: '请选择成立时间', trigger: 'blur' }],
		enterpriseNatureNew: [{ required: true, message: '请输入类型', trigger: 'blur' }],
		registcurrency: [{ required: true, message: '请选择注册资本币种', trigger: 'blur' }],
		registeredCapital: [{ required: true, message: '请以个位为单位进行填报', trigger: 'blur' }],
		mainBusiness: [{ required: true, message: '请按照营业执照或公司注册文件填写', trigger: 'blur' }],
		isListed: [{ required: true, message: '请选择是否上市', trigger: 'blur' }]
	})
	// 表格
	const listedColumns = computed(() => {
		let arr = [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '市场',
				dataIndex: 'market',
				ellipsis: true,
				customRender: ({ text, record }) => {
					if (record.isEdit) {
						return (
							<Select
								v-model:value={record.market}
								options={market_listed}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择市场"
								allowClear
							/>
						)
					} else {
						return market_listed.find((o) => o.value == text)?.label
					}
				}
			},
			{
				title: '股票简称',
				dataIndex: 'stock',
				ellipsis: true,
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input
							v-model:value={record.stock}
							onBlur={() => {
								if (!text.trim()) message.error('请输入股票简称！')
							}}
							style="margin-left:-12px;"
							placeholder="请输入股票简称"
							allowClear
						/>
					) : (
						text
					)
					return html
				}
			},
			{
				title: '股票代码',
				dataIndex: 'stockCode',
				ellipsis: true,
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input
							v-model:value={record.stockCode}
							onBlur={() => {
								if (!text.trim()) message.error('请输入股票代码！')
							}}
							style="margin-left:-12px;"
							placeholder="请输入股票代码"
							allowClear
						/>
					) : (
						text
					)
					return html
				}
			}
		]
		if (!props.disabled && formState.value.isListed == 'Y') {
			arr.push({ title: '操作', dataIndex: 'action', align: 'center', width: 280 })
		}
		return arr
	})
	const listedAddRow = () => {
		formState.value.entityListedList.push({
			id: '',
			market: null,
			stock: '',
			stockCode: '',
			isEdit: true
		})
	}
	const listedHandleSave = async (record) => {
		if (!record.market) return message.error('股票市场不能为空!')
		if (!record.stock.trim()) return message.error('股票简称不能为空!')
		if (!record.stockCode.trim()) return message.error('股票代码不能为空!')
		record.isEdit = false
	}
	const listedEditRow = (record, index) => {
		record.isEdit = true
		formState.value.entityListedList[index]['isEdit'] = true
	}
	const listedDelRow = (record, index) => {
		formState.value.entityListedList.splice(index, 1)
		if (record.id) formState.value.entityListedDeleteIds.push(record.id)
	}
	const columns = computed(() => {
		let arr = [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '姓名',
				dataIndex: 'name',
				ellipsis: true,
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input
							v-model:value={record.name}
							status={record?.status}
							onBlur={() => {
								record.status = !text.trim() ? 'error' : ''
								if (!text.trim()) message.error('请输入姓名！')
							}}
							style="margin-left:-12px;"
							placeholder="请输入姓名"
							allowClear
						/>
					) : (
						text
					)
					return html
				}
			},
			{
				title: '职务',
				dataIndex: 'position',
				ellipsis: true,
				customRender: ({ text, record }) => {
					if (record.isEdit) {
						return (
							<Select
								v-model:value={record.position}
								mode="multiple"
								options={position}
								style="width:100%;margin-left:-12px;"
								placeholder="请选择职务"
								allowClear
							/>
						)
					} else {
						let obj = []
						map(record.position, (item) => {
							obj.push(position.find((o) => o.value == item)?.label)
						})
						return obj.length ? obj.join('、') : '无'
					}
				}
			}
		]
		if (!props.disabled) {
			arr.push({ title: '操作', dataIndex: 'action', align: 'center', width: 280 })
		}
		return arr
	})
	const addRow = () => {
		formState.value.entitymanagerList.push({
			id: '',
			name: '',
			position: [],
			status: '',
			isEdit: true
		})
	}
	const handleSave = async (record) => {
		if (!record.name) return message.error('姓名不能为空!')
		if (!record.position.length) return message.error('职务不能为空!')
		record.isEdit = false
	}
	const editRow = (record, index) => {
		record.isEdit = true
		formState.value.entitymanagerList[index]['isEdit'] = true
	}
	const delRow = (record, index) => {
		formState.value.entitymanagerList.splice(index, 1)
		if (record.id) formState.value.entitymanagerDeleteIds.push(record.id)
	}
	const onMove = (type, index) => {
		switch (type) {
			case 'prev':
				if (index === 0) return
				formState.value.entitymanagerList.splice(
					index - 1,
					2,
					formState.value.entitymanagerList[index],
					formState.value.entitymanagerList[index - 1]
				)
				break
			case 'next':
				if (index === formState.value.entitymanagerList.length - 1) return
				formState.value.entitymanagerList.splice(
					index,
					2,
					formState.value.entitymanagerList[index + 1],
					formState.value.entitymanagerList[index]
				)
				break
		}
	}
	const yesOrNo = computed(() => {
		return formState.value.noneManagerFlag === 'Y'
	})
	const submitForm = async () => {
		return new Promise((resolve, reject) => {
			// const formObj = cloneDeep(formRef.value.getFieldsValue())
			Object.keys(formState.value).forEach((key) => {
				if (formState.value[key] && typeof formState.value[key] === 'string')
					formState.value[key] = formState.value[key].trim()
			})
			formRef.value
				.validate()
				.then(() => {
					const errorObj = { key: props.keys }
					if (formState.value.isListed == 'Y' && formState.value.entityListedList?.length == 0) {
						message.error('上市信息不能为空，请添加数据!')
						return reject(errorObj)
					}
					if (!yesOrNo.value && formState.value.entitymanagerList?.length == 0) {
						message.error('董监高不能为空，请添加数据!')
						return reject(errorObj)
					}
					const form = cloneDeep(formRef.value.getFieldsValue())
					map(form.entityListedList, (item, index) => {
						item.sortCode = index + 1
						return item
					})
					map(form.entitymanagerList, (item, index) => {
						item.position = item.position?.join()
						item.sortCode = index + 1
						return item
					})
					let obj = {
						entitymanagerDeleteIds: form?.entitymanagerDeleteIds.join(','),
						entityListedDeleteIds: form?.entityListedDeleteIds.join(',')
					}
					formRef.value.clearValidate()
					resolve({ ...form, ...obj })
				})
				.catch((error) => {
					console.log(error)
					if (error.errorFields?.length) {
						formRef.value.scrollToField(error.errorFields[0].name[0])
						message.warning(error.errorFields[0].errors[0])
					}
					error.key = props.keys
					reject(error)
				})
		})
	}
	// 注入注册方法
	const { register, unregister } = inject('validatorRegistry')
	onMounted(() => {
		register(props.keys, submitForm)
	})
	onUnmounted(() => {
		unregister(props.keys)
	})
	watch(
		() => props.data,
		(newVal) => {
			formState.value = cloneDeep(newVal)
			formState.value.entityListedDeleteIds = cloneDeep(newVal?.entityListedDeleteIds || [])
			formState.value.entitymanagerDeleteIds = cloneDeep(newVal?.entitymanagerDeleteIds || [])
			formRef.value.clearValidate()
		},
		{ immediate: true, deep: true }
	)
	watch(
		() => props.busineseForm,
		(newVal) => {
			formState.value = { ...formState.value, ...cloneDeep(newVal) }
		},
		{ deep: true }
	)
	const clearValidate = () => {
		formRef.value.clearValidate()
	}
	const checked = computed(() => {
		return formState.value.isRemove == 'Y'
	})
	defineExpose({
		formState,
		clearValidate
	})
</script>
<style lang="less" scoped></style>

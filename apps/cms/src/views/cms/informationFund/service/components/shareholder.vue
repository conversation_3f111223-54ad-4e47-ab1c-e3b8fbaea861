<template>
	<!-- 股权关系 -->
	<a-form
		ref="formRef"
		:labelCol="{
			style: {
				width: '80px'
			}
		}"
		:model="formState"
		:rules="formRules"
		:disabled="disabled"
		:scrollToFirstError="true"
	>
		<a-form-item
			ref="fillingStandards"
			label="机构类别"
			name="fillingStandards"
			:wrapper-col="{
				span: 24
			}"
		>
			<a-radio-group
				v-model:value="formState.fillingStandards"
				@change="
					(e) => {
						bus.emit('shareholderForm', e.target.value)
					}
				"
				disabled
			>
				<a-radio :style="radioStyle" v-for="item in filling_standards" :value="item.value" :key="item.value">
					{{ item.label }}
				</a-radio>
			</a-radio-group>
		</a-form-item>
		<div style="display: none">
			<a-form-item label="机构类别" name="entityinternalinvestmentList"></a-form-item>
			<a-form-item label="删除股权关系" name="entityinternalinvestmentDeleteIds"></a-form-item>
		</div>
		<Collapse title="内部股东">
			<template #content>
				<a-space orientation>
					<a-button type="primary" @click="addRow" v-if="!disabled" :disabled="total >= 100">新增</a-button>
				</a-space>
				<a-table
					class="mt-2"
					:columns="columns"
					:data-source="formState.entityinternalinvestmentList"
					:pagination="false"
					size="small"
					:row-key="(record) => record.id"
					:scroll="{ x: 1000 }"
				>
					<template #bodyCell="{ column, record, index }">
						<template v-if="column.dataIndex === 'action'">
							<a-space style="gap: 0">
								<template #split>
									<a-divider type="vertical" />
								</template>
								<a-button type="link" danger size="small" v-if="record?.isEdit" @click="handleSave(record, index)">
									保存
								</a-button>
								<a-button type="link" danger size="small" v-else @click="editRow(record, index)"> 编辑 </a-button>

								<a-popconfirm title="确定要删除吗？" @confirm="delRow(record, index)">
									<a-button type="link" danger size="small" :disabled="index == 0">删除</a-button>
								</a-popconfirm>
							</a-space>
						</template>
					</template>
					<template #summary>
						<a-table-summary fixed>
							<a-table-summary-row>
								<a-table-summary-cell :col-span="1" :index="0"></a-table-summary-cell>
								<a-table-summary-cell :col-span="1" :index="1">中信方持股比例合计</a-table-summary-cell>
								<a-table-summary-cell :col-span="1"></a-table-summary-cell>
								<a-table-summary-cell :col-span="disabled ? 2 : 3">
									<div class="ml-[12px]">{{ total }}</div>
								</a-table-summary-cell>
							</a-table-summary-row>
						</a-table-summary>
					</template>
				</a-table>
			</template>
		</Collapse>
	</a-form>
</template>
<script setup lang="jsx">
	import { message } from 'ant-design-vue'
	import { cloneDeep, map, filter } from 'lodash-es'
	import tool from '@/utils/tool'
	import bus from '@/utils/bus'

	import { required, rules } from '@/utils/formRules'
	import { useOrgTreeStore } from '@/store'
	const OrgTreeStore = useOrgTreeStore()
	const isOrNo = tool.dictList('WHETHER')
	const filling_standards = tool.dictList('filling_standards')
	const relationship = tool.dictList('relationship')

	const radioStyle = ref({
		display: 'flex',
		height: '33px',
		lineHeight: '33px'
	})

	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		disabled: {
			type: Boolean,
			default: false
		},
		isAdd: {
			type: Boolean,
			default: false
		},
		keys: {
			type: String,
			default: ''
		},
		treeSelectNode: {
			type: Object,
			default: () => {}
		},
		moveNodeData: {
			type: Object,
			default: () => {}
		}
	})

	const formRef = ref()
	const formState = ref({
		entityinternalinvestmentList: []
	})
	const formRules = ref({
		fillingStandards: [{ required: true, message: '请选择填报标准', trigger: 'blur' }]
	})
	const fieldNames = ref({
		children: 'children',
		label: 'simpOrgName',
		value: 'id'
	})
	const columns = computed(() => {
		let arr = [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				fixed: 'left',
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '内部股东企业全称',
				dataIndex: 'investorId',
				fixed: 'left',
				// width: 100,
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let html = (
						<TreeSelect
							class="w-full"
							v-model:value={record.investorId}
							fieldNames={fieldNames.value}
							treeData={OrgTreeStore.selectTreeOrgFilling}
							style="width: 100%;margin-left:0px;"
							dropdownMatchSelectWidth={500}
							placeholder="请选择企业"
							disabled={index == 0 ? true : !record.isEdit}
							onSelect={(value, node) => {
								record.isConsolidation = node.isConsolidation
								console.log(record)
								if (
									filter(formState.value.entityinternalinvestmentList, (item) => item.investorId == value)?.length > 1
								) {
									record.investorId = null
									record.isConsolidation = ''
									console.log(record)
									message.warning('不能选择相同股东企业')
								}
								if (props.treeSelectNode.id == value) {
									record.investorId = null
									record.isConsolidation = ''
									message.warning('不能选择自身作为内部股东')
								}
							}}
						/>
					)
					return html
				}
			},
			{
				title: '与内部股东的财务关系',
				dataIndex: 'relationship',
				ellipsis: true,
				// width: 130,
				customRender: ({ text, record }) => {
					let arr = cloneDeep(relationship)
					if (
						filter(formState.value.entityinternalinvestmentList, (item) => item.relationship == 'relationship_1')
							?.length > 0
					) {
						map(arr, (item) => {
							item.disabled = item.value == 'relationship_1'
						})
					}
					const html = record.isEdit ? (
						<a-select
							style="width:100%;margin-left:-12px;"
							options={arr}
							v-model:value={record.relationship}
							placeholder="请选择财务关系"
							allowClear
						/>
					) : (
						relationship.find((item) => item.value === text)?.label
					)
					return html
				}
			},

			{
				title: '内部股东持股比例(%)',
				dataIndex: 'shareRate',
				// width: 130,
				customRender: ({ text, record }) => {
					let html = record.isEdit ? (
						<a-input-number
							v-model:value={record.shareRate}
							status={record?.status}
							onBlur={() => {
								record.status = !text.trim() ? 'error' : ''
								if (!text.trim()) message.error('请输入持股比例！')
							}}
							min={0}
							max={100}
							step={0.0000001}
							precision={7}
							stringMode={true}
							style="margin-left:-12px;"
							placeholder="请输入数字"
							allowClear
						/>
					) : (
						text
					)
					return html
				}
			},
			{
				title: '是否持股比例最高的内部股东',
				dataIndex: 'isHightShareholder',
				ellipsis: true,
				width: 230,
				customRender: ({ text, record }) => {
					const html = record.isEdit ? (
						<a-select
							style="width:100%;"
							options={isOrNo}
							v-model:value={record.isHightShareholder}
							placeholder="请选择是否是最高股东"
							allowClear
							disabled
						/>
					) : (
						isOrNo.find((item) => item.value === text)?.label
					)
					return html
				}
			}
		]
		if (!props.disabled) {
			arr.push({ title: '操作', dataIndex: 'action', align: 'center', width: 130, fixed: 'right' })
		}
		return arr
	})
	const total = computed(() => {
		let sum = 0
		formState.value.entityinternalinvestmentList.forEach((item) => {
			sum += Number(item.shareRate)
		})
		return parseFloat(sum.toPrecision(10))
	})
	const addRow = () => {
		if (total.value >= 100) return message.error('持股比例合计不能超过100!')
		formState.value.entityinternalinvestmentList.push({
			id: '',
			investorId: null,
			relationship: null,
			shareRate: '',
			isHightShareholder: formState.value.entityinternalinvestmentList?.length >= 1 ? 'N' : 'Y',
			isConsolidation: '',
			isEdit: true
		})
	}
	const handleSave = async (record, index) => {
		if (!record.investorId) return message.error('请选择内部股东企业全称!')
		if (!record.relationship) return message.error('请选择与内部股东的财务关系!')
		if (!record.shareRate) return message.error('请输入内部股东持股比例!')
		if (!record.isHightShareholder) return message.error('请选择是否是持股比例最高的内部股东!')
		if (record.isHightShareholder == 'Y') {
			formState.value.entityinternalinvestmentList.unshift(
				formState.value.entityinternalinvestmentList.splice(index, 1)[0]
			)
		}
		if (total.value > 100) return message.error('内部股东持股比例不能超过100%!')
		const isConsolidationArr = filter(
			formState.value.entityinternalinvestmentList,
			(item) => item?.isConsolidation == '1'
		)
		if (isConsolidationArr.length) {
			let sum = 0
			isConsolidationArr.forEach((item) => {
				sum += Number(item.shareRate)
			})
			bus.emit('isitanInstiChange', sum > 50 ? 'Y' : 'N')
		} else {
			bus.emit('isitanInstiChange', 'N')
		}
		record.isEdit = false
	}
	const editRow = (record, index) => {
		record.isEdit = true
		formState.value.entityinternalinvestmentList[index].isEdit = true
	}
	const delRow = (record, index) => {
		if (record.id) formState.value.entityinternalinvestmentDeleteIds.push(record.id)
		formState.value.entityinternalinvestmentList.splice(index, 1)
	}
	const submitForm = async () => {
		return new Promise((resolve, reject) => {
			let errObj = { key: props.keys }
			Object.keys(formState.value).forEach((key) => {
				if (formState.value[key] && typeof formState.value[key] === 'string')
					formState.value[key] = formState.value[key].trim()
			})
			if (filter(formState.value.entityinternalinvestmentList, (item) => item.isEdit)?.length) {
				message.error('请先保存所有内部股东信息!')
				return reject(errObj)
			}
			if (
				filter(formState.value.entityinternalinvestmentList, (item) => item.relationship == 'relationship_1')?.length >
				1
			) {
				message.error('内部股东信息只能有一个子公司!')
				return reject(errObj)
			}
			if (total.value > 100) {
				message.error('持股⽐例合计不能超过100!')
				return reject(errObj)
			}
			formRef.value
				.validate()
				.then(() => {
					const form = cloneDeep(formRef.value.getFieldsValue())
					const obj = {
						id: props.isAdd ? '' : formState.value.id,
						status: props.isAdd ? '0' : formState.value?.status,
						entityId: props.isAdd ? '' : formState.value?.entityId,
						entityinternalinvestmentDeleteIds: form.entityinternalinvestmentDeleteIds.join(','),
						entityinternalinvestmentList: form.entityinternalinvestmentList?.map((item, index) => {
							item.shareRate = parseFloat((item.shareRate / 100).toPrecision(10))
							item.sortCode = index + 1
							return item
						}),
						// 所属管理主体id
						manageCompanyId: formState.value?.manageCompanyId,
						organizationParentId: props.isAdd ? props.treeSelectNode.id : formState.value?.organizationParentId
					}
					formRef.value.clearValidate()
					resolve({ ...form, ...obj })
				})
				.catch((error) => {
					console.log(error)
					if (error.errorFields?.length) {
						formRef.value.scrollToField(error.errorFields[0].name[0])
						message.warning(error.errorFields[0].errors[0])
					}
					reject({ ...error, ...errObj })
				})
		})
	}
	// 注入注册方法
	const { register, unregister } = inject('validatorRegistry')
	onMounted(() => {
		register(props.keys, submitForm)
	})
	onUnmounted(() => {
		unregister(props.keys)
	})
	watch(
		() => props.data,
		(val) => {
			formState.value = cloneDeep(val)
			formState.value.entityinternalinvestmentDeleteIds = cloneDeep(val?.entityinternalinvestmentDeleteIds || [])
			formRef.value.clearValidate()
		},
		{ immediate: true, deep: true }
	)
	watch(
		() => props.moveNodeData,
		(val) => {
			if (val) {
				formState.value.entityinternalinvestmentDeleteIds = [formState.value.entityinternalinvestmentList[0].id]
				formState.value.entityinternalinvestmentList[0] = {
					id: '',
					investorId: val.id,
					relationship: null,
					shareRate: '',
					isHightShareholder: 'Y',
					isEdit: true
				}
				formState.value.organizationParentId = val.id
			}
		},
		{ deep: true }
	)
	const clearValidate = () => {
		formRef.value.clearValidate()
	}
	defineExpose({
		formState,
		clearValidate
	})
</script>
<style lang="less" scoped></style>

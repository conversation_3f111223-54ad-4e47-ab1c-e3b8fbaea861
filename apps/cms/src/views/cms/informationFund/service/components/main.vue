<template>
	<a-row class="flex-1 flex overflow-hidden w-full gap-3">
		<a-col class="h-full flex flex-col bg-white rounded-lg overflow-auto overflow-y-hidden" v-if="showAll">
			<auditTree
				class="flex-1 overflow-hidden"
				ref="TreeRef"
				:selectLeastOne="treeAttr.selectLeastOne"
				:treeData="treeData"
				:loading="treeAttr.loading"
				:keys="treeAttr.keys"
				:title="treeAttr.title"
				:expanded="treeAttr.expanded"
				:selected="treeAttr.selected"
				:searchDisabled="isAdd || !disabled"
				:disabled="!disabled"
				@treeSelect="treeSelect"
				@autoSelectNodeData="autoSelectNodeData"
				:collapseStyle="{ top: '43.2%' }"
				placeholder="请输入公司名称"
			>
				<template #treeType>
					<Select
						class="w-full"
						v-model:value="searchValue"
						:options="searchOptions"
						@change="
							(value) => {
								console.log(value)
								emits('treeType', value)
								treeSelectNode = {}
								TreeRef.treeSelectAttr.selectedKeys = []
								data = {}
								loadTreeData()
							}
						"
						:disabled="!disabled"
						:allowClear="false"
						placeholder="请选择架构类型"
					></Select>
				</template>
				<template #right-icon="{ data, status }">
					<span class="px-[5px] text-[10px] text-center rounded-lg" v-if="status == 1">(移除)</span>
					<LockOutlined v-if="data.lockStatus == 1" />
					<CloseOutlined v-if="data.isRemove == 'Y'" class="mr-1" />
				</template>
			</auditTree>
		</a-col>

		<a-col flex="1" class="h-full pl-4 py-2 overflow-hidden relative bg-white rounded-lg s-table">
			<div class="text-[#d6000f] absolute right-4 top-4" v-if="showAll">
				{{ props.searchForm?.isHistory ? '查看版本：' : '当前版本：' }}{{ curVersionDateComputed }}
			</div>
			<a-spin :spinning="loading" wrapperClassName="h-full overflow-hidden">
				<a-tabs
					v-model:activeKey="tabActive"
					class="h-full"
					:destroyInactiveTabPane="true"
					@change="
						(activeKey) => {
							tabActive = activeKey
						}
					"
				>
					<a-tab-pane key="0" tab="工商信息" :forceRender="true">
						<basic ref="tabsBasicRef" :data="data" :busineseForm="busineseForm" :disabled="disabled" keys="0"></basic>
					</a-tab-pane>
					<a-tab-pane key="1" tab="内部股权信息" :forceRender="true">
						<shareholder
							ref="tabsShareholderRef"
							keys="1"
							:data="data"
							:disabled="disabled"
							:treeSelectNode="treeSelectNode"
							:isAdd="isAdd"
							:moveNodeData="props.moveNodeData"
						></shareholder>
					</a-tab-pane>
					<a-tab-pane key="2" tab="经营信息" :forceRender="true">
						<businessData ref="tabsBusinessDataRef" :data="data" :disabled="disabled" keys="2"></businessData>
					</a-tab-pane>
					<a-tab-pane key="3" tab="管理信息" :forceRender="true">
						<disposeOf
							ref="tabsDisposeOfRef"
							:data="data"
							:disabled="disabled"
							keys="3"
							:isAdd="isAdd"
							:tabActive="tabActive"
						></disposeOf>
					</a-tab-pane>
				</a-tabs>
			</a-spin>
		</a-col>
	</a-row>
</template>
<script setup lang="jsx">
	import { defineAsyncComponent } from 'vue'

	import { message, Modal } from 'ant-design-vue'
	import { cloneDeep, map } from 'lodash-es'
	import basic from './basic.vue'
	import shareholder from './shareholder.vue'
	import businessData from './businessData.vue'
	import disposeOf from './disposeOf.vue'
	import auditTree from '@/components/Tree/auditTree.vue'

	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	import bizEntitymanagerApi from '@/api/biz/bizEntitymanagerApi.js'
	import bizEntityinternalinvestmentApi from '@/api/biz/bizEntityinternalinvestmentApi.js'
	import bizEntitycategoryApi from '@/api/biz/bizEntitycategoryApi.js'
	import bizEntityDisposalApi from '@/api/biz/bizEntityDisposalApi.js'
	import bizEntityListedApi from '@/api/biz/bizEntityListedApi.js'
	import { abstractCheckboxGroupProps } from 'ant-design-vue/es/checkbox/interface'
	import tool from '@/utils/tool'
	import { useOrgTreeStore } from '@/store'
	const OrgTreeStore = useOrgTreeStore()
	const props = defineProps({
		isAdd: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
		approveForm: {
			type: Object,
			default: () => {}
		},
		busineseForm: {
			type: Object,
			default: () => {}
		},
		moveNodeData: {
			type: Object,
			default: () => {}
		},
		searchForm: {
			type: Object,
			default: () => {}
		},
		showAll: {
			type: Boolean,
			default: true
		},
		initData: {
			type: Object,
			default: () => {}
		}
	})
	const emits = defineEmits(['isSave', 'loadingFn', 'treeType', 'treeClick'])
	const route = useRoute()
	const TreeRef = ref()
	const tabsBasicRef = ref()
	const tabsShareholderRef = ref()
	const tabsBusinessDataRef = ref()
	const tabsDisposeOfRef = ref()
	const treeValue = ref(null)
	const loading = ref(false)
	const { roleCodeList } = tool.data.get('USER_INFO')
	// 财务，董办用户
	const isGroup = computed(() => {
		return roleCodeList.includes('financeUser') || roleCodeList.includes('directorUser')
	})
	// 集团，填报用户
	const isSelect = computed(() => {
		return roleCodeList.includes('groupUser') || roleCodeList.includes('fillInUser')
	})
	const searchOptions = ref([
		{ label: '股权架构', value: '0', disabled: !hasPerm('shareholding') },
		{ label: '合并架构', value: '1', disabled: !hasPerm('merge') },
		{ label: '管理架构', value: '2', disabled: !hasPerm('manage') }
	])

	const tabKey = ref('0')
	const searchValue = ref(hasPerm('shareholding') ? '0' : hasPerm('merge') ? '1' : hasPerm('manage') ? '2' : '0')
	const { tabActive } = inject('validatorRegistry')
	const data = ref({})
	const getSelectTree = () => {
		bizInstitutionApi.getStructureOrgnList({ isWhereFilling: '1', entityId:treeSelectNode.value.id }).then((res) => {
			OrgTreeStore.setSelectTreeOrgFilling(res)
		})
	}
	const getSelectTreeAll = () => {
		bizInstitutionApi.getStructureOrgnList({ isWhereFilling: '0' }).then((res) => {
			OrgTreeStore.setSelectTreeOrg(res)
		})
	}
	watch(
		() => props.isAdd,
		async (newVal) => {
			if (newVal) {
				const level = await bizInstitutionApi.getLegalEntityLevel({ organizationParentId: treeSelectNode.value.id })

				const obj = {
					...{
						entityListedList: [],
						entityListedDeleteIds: [],
						entitymanagerList: [],
						entitymanagerDeleteIds: [], // 董监高删除
						entityinternalinvestmentList: [],
						entityinternalinvestmentDeleteIds: [], //股权关系删除
						entitycategoryList: [],
						entitycategoryDeleteIds: [], //行业小类删除
						entityDisposalList: [],
						legalEntityLevel: level
					},
					...props.approveForm
				}
				data.value = obj
				getSelectTree()
				treeAttr.selected = [treeSelectNode.value.id]
			}
		}
	)
	const treeAttr = reactive({
		keys: 'id',
		title: 'simpOrgName',
		loading: false,
		selectLeastOne: true,
		selected: [], // 默认选中
		expanded: [], // 默认展开
		checked: [] // 默认勾选
	})
	// watch(
	// 	() => treeValue.value,
	// 	(newVal) => {
	// 		treeAttr.selected = [newVal]
	// 		console.log(treeAttr.selected)
	// 	}
	// )
	const treeData = ref([])
	const OrgTreeObj = ref({})
	// 加载左侧的树
	const loadTreeData = async (versionId = '') => {
		// 处理树是否可编辑
		const disposeTree = async (tree) => {
			for (let i = 0; i < tree.length; i++) {
				tree[i].disabled = tree[i]?.isClickable == 1 ? false : true
				if (tree[i]?.children) {
					disposeTree(tree[i]?.children)
				}
			}
		}
		treeAttr.loading = true
		bizInstitutionApi
			.getStructureTree({ structureType: searchValue.value, versionId })
			.then(async (resData) => {
				if (resData?.length) {
					await disposeTree(resData)
					let res = []
					let index = 3
					if (resData.length) {
						resData.map((item) => {
							index++
							if (item.entityCode == '09999') {
								res[0] = item
							} else if (item.entityCode == '11486') {
								res[1] = item
							} else if (item.entityCode == '09998') {
								res[2] = item
							} else {
								res[index] = item
							}
						})
					}
					treeData.value = res
					treeAttr.expanded.push(res[0]?.id)
					treeAttr.loading = false
					// localStorage.setItem('treeData', JSON.stringify(res))
				} else {
					treeData.value = []
					treeAttr.loading = false
				}
			})
			.catch(() => {
				treeAttr.loading = false
			})
		// 获取select树
		getSelectTreeAll()
	}
	const oldData = ref({})

	const formatEntitymanagerList = (entitymanagerList) => {
		return map(entitymanagerList, (item) => {
			item.position = item.position?.split?.(',')
			return item
		})
	}
	const formatEntityinternalinvestmentList = (entityinternalinvestmentList) => {
		return map(entityinternalinvestmentList, (item) => {
			item.shareRate = parseFloat((item.shareRate * 100).toPrecision(12))
			return item
		})
	}
	// 点击查询详情
	const getDetail = async (id, ywId, versionId) => {
		tabActive.value = '0'
		tabKey.value = '0'
		const detail = await bizInstitutionApi.getDetail({ id: ywId, versionId })

		// 获取上市信息
		const TableListed = await bizEntityListedApi.getPage({
			size: 100,
			versionId: detail?.versionId,
			entityId: detail?.entityId
		})
		// 获取董监高
		const TableManager = await bizEntitymanagerApi.getPage({
			size: 100,
			versionId: detail?.versionId,
			entityId: detail?.entityId
		})
		const TableInternalinvestment = await bizEntityinternalinvestmentApi.getPage({
			versionId: detail?.versionId,
			entityId: detail?.entityId,
			size: 100
		})
		const TableCategory = await bizEntitycategoryApi.getList({
			versionId: detail?.versionId,
			entityId: detail?.entityId,
			size: 100
		})
		const TableDisposal = await bizEntityDisposalApi.getList({
			versionId: detail?.versionId,
			entityId: detail?.entityId,
			ywId: detail?.ywId
		})
		oldData.value = {
			...detail,
			...{
				entityListedList: cloneDeep(TableListed?.records || []),
				entitymanagerList: cloneDeep(TableManager?.records || []),
				entityinternalinvestmentList: cloneDeep(TableInternalinvestment?.records || []),
				entitycategoryList: TableCategory || [],
				entityDisposalList: TableDisposal || []
			}
		}

		const obj = {
			entityListedList: TableListed?.records || [],
			entitymanagerList: formatEntitymanagerList(TableManager?.records || []),
			entityinternalinvestmentList: formatEntityinternalinvestmentList(TableInternalinvestment?.records || []),
			entitycategoryList: TableCategory || [],
			entityDisposalList: TableDisposal || []
		}
		data.value = { ...detail, ...obj }
		getSelectTree()
	}
	const versionId = ref('')
	const curVersionDate = ref('')
	const curVersionDateComputed = computed(() => {
		return versionId.value ? `${props.searchForm?.year}年${props.searchForm?.month}月` : curVersionDate.value
	})
	// 详情局部loading
	const getDetailLoading = async (id, ywId, versionId = '') => {
		try {
			loading.value = true
			await getDetail(id, ywId, versionId)
		} catch (error) {
			console.log(error)
		} finally {
			loading.value = false
		}
	}
	// 点击树查询
	const treeSelectNode = ref({})
	const treeSelect = ({ selectedKeys, data }) => {
		clearTabsValidate()
		const emitsFn = (status) => {
			emits('isSave', {
				id: OrgTreeObj.value.id,
				ywId: OrgTreeObj.value.ywId,
				status
			})
		}
		if (!props.disabled) {
			Modal.confirm({
				title: '提示',
				content: '是否保存修改的内容？',
				okText: '是',
				cancelText: '否',
				onOk: () => {
					// 先提交在获取详情
					emitsFn(true)
				},
				onCancel: async () => {
					emitsFn(false)
					// 直接获取详情
					await getDetailLoading(selectedKeys.toString(), data.node?.ywId, versionId.value || '')
				}
			})
		} else {
			// 获取详情接口
			selectedKeys.length &&
				data.node?.ywId &&
				getDetailLoading(selectedKeys.toString(), data.node?.ywId, versionId.value || '')
		}
		treeSelectNode.value = data.node
		console.log(treeSelectNode.value)
		emits('treeClick', treeSelectNode.value)
		if (selectedKeys.length > 0) {
			OrgTreeObj.value = {
				id: selectedKeys.toString(),
				ywId: data.node.ywId,
				name: data.node.name
			}
		} else {
			OrgTreeObj.value = {}
		}
	}
	const autoSelectNodeData = (data) => {
		console.log('自动选中：', data)
		getDetailLoading(data?.id, data?.ywId, versionId.value || '')
		treeSelectNode.value = data
	}
	const getNode = () => {
		return treeSelectNode.value
	}
	const clearSelectTree = () => {
		treeSelectNode.value = {}
		data.value = {}
		treeAttr.selected = []
	}

	onMounted(async () => {
		if (!props.searchForm?.isHistory && props.showAll) {
			await loadTreeData()
			const res = await bizInstitutionApi.getCurVersionDate()
			curVersionDate.value = res ? `${res?.year}年${res?.period}月` : ''
			// 历史对比调用此页面，可能查询实时版本
			nextTick(() => {
				if (props.searchForm?.treeId) treeAttr.selected = [props.searchForm?.treeId]
			})
		}
		if (route.query?.id) {
			const { id, ywId } = route.query
			treeAttr.selected = [id]
			TreeRef.value.treeCollapse = false
			await getDetailLoading(id, ywId)
		}
	})
	watch(
		() => props.searchForm,
		async (val) => {
			if (val.date) {
				if (TreeRef.value) TreeRef.value.treeSelectAttr.selectedKeys = []
				versionId.value = await bizInstitutionApi.getSelectVersionId(val)
				versionId.value ? await loadTreeData(versionId.value) : message.warning('当前日期暂无历史版本！')
				data.value = {}
				nextTick(() => {
					if (val?.treeId) treeAttr.selected = [val.treeId]
				})
			}
		},
		{
			immediate: true,
			deep: true
		}
	)

	watch(
		() => props.initData,
		async (val) => {
			if (!props.showAll && val) {
				console.log('initData:', val)
				getSelectTree()
				getSelectTreeAll()
				const initialData = cloneDeep(val) || {}
				const entitymanagerList = formatEntitymanagerList(initialData.entitymanagerList)
				const entityinternalinvestmentList = formatEntityinternalinvestmentList(
					initialData.entityinternalinvestmentList
				)
				// const entitycategoryList = initialData.entitycategoryList
				// const entityDisposalList = initialData.entityDisposalList
				data.value = {
					...initialData,
					entitymanagerList,
					entityinternalinvestmentList
					// entitycategoryList,
					// entityDisposalList
				}
			}
		},
		{
			immediate: true,
			deep: true
		}
	)
	const getTabsForm = (index) => {
		let form = {}
		switch (index) {
			case 0:
				form = tabsBasicRef.value.formState
				break
			case 1:
				form = tabsShareholderRef.value.formState
				break
			case 2:
				form = tabsBusinessDataRef.value.formState
				break
			case 3:
				form = tabsDisposeOfRef.value.formState
				break
		}
		return form
	}
	const clearTabsValidate = () => {
		tabsBasicRef.value && tabsBasicRef.value.clearValidate()
		tabsShareholderRef.value && tabsShareholderRef.value.clearValidate()
		tabsBusinessDataRef.value && tabsBusinessDataRef.value.clearValidate()
		tabsDisposeOfRef.value && tabsDisposeOfRef.value.clearValidate()
	}
	defineExpose({
		// 获取选中的树节点
		getNode,
		getDetail,
		getDetailLoading,
		loadTreeData,
		clearSelectTree,
		data,
		oldData,
		versionId,
		loading,
		getTabsForm,
		clearTabsValidate,
		treeAttr,
		curVersionDate
	})
</script>
<style lang="less" scoped>
	:deep(.ant-tabs) {
		.ant-tabs-content-holder {
			overflow: auto;
		}
		.ant-tabs-tab {
			padding-left: 0 !important;
			padding-right: 0 !important;
			font-size: 16px;
		}
	}
	:deep(.ant-spin-container) {
		height: 100%;
	}
	:deep(.ant-input-number) {
		width: 100% !important;
	}
</style>

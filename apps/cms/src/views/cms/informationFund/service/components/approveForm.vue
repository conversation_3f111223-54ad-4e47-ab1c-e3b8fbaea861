<template>
	<xn-form-container title="新建下级" :width="750" :visible="visible" :destroy-on-close="true" @close="onClose">
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<p class="ml-[-10px] mb-[5px] text-16 font-bold">基本信息填报</p>
			<div class="grid grid-cols-3 gap-3">
				<a-form-item ref="registarea1" label="注册地区" name="registarea1" class="col-span-2">
					<Select
						class="w-full"
						:options="Registration"
						v-model:value="formData.registarea1"
						@change="
							(value) => {
								console.log(value)
								if (value == 'RA200000' || value == 'RA300000') {
									formData.legalPersonName = '不适用'
								} else {
									formData.legalPersonName = ''
								}
								formData.registarea2 = null
								formData.registtype = value == 'RA100000' ? 'LT01' : null
								formData.registcode = ''
							}
						"
						placeholder="请选择注册地区"
					/>
				</a-form-item>
			</div>
			<div class="grid grid-cols-2 gap-3">
				<a-form-item ref="registtype" label="注册证照类型" name="registtype">
					<Select
						:options="License"
						v-model:value="formData.registtype"
						allowClear
						placeholder="请选择注册证照类型"
						@change="
							(value) => {
								console.log(value)
							}
						"
					/>
				</a-form-item>
				<a-form-item ref="registcode" label="注册证照代码" name="registcode">
					<a-input v-model:value="formData.registcode" allowClear placeholder="请输入注册证照代码" :maxlength="18" />
				</a-form-item>
			</div>
			<p class="ml-[-10px] mb-0 text-16 font-bold">机构类别</p>
			<a-form-item ref="fillingStandards" label="" name="fillingStandards">
				<a-radio-group v-model:value="formData.fillingStandards" class="flex flex-col">
					<a-radio
						:style="{
							display: 'flex',
							height: '33px',
							lineHeight: '33px'
						}"
						v-for="(item, index) in filling_standards"
						:value="item.value"
						:key="item.value"
					>
						{{ item.label }}
					</a-radio>
				</a-radio-group>
			</a-form-item>
			<p class="ml-[-10px] mb-[5px] text-16 font-bold">审批信息</p>
			<div class="grid grid-cols-3 gap-3">
				<a-form-item label="审批类型" name="apprType" class="col-span-2">
					<Select :options="appr_type" v-model:value="formData.apprType" allowClear placeholder="请选择审批类型" />
				</a-form-item>
			</div>
			<div class="grid grid-cols-3 gap-3">
				<a-form-item label="审批备案时间" name="apprFilitime" class="col-span-2">
					<a-date-picker
						class="w-full"
						v-model:value="formData.apprFilitime"
						valueFormat="YYYY-MM-DD"
						allowClear
						placeholder="请选择审批备案时间"
					/>
				</a-form-item>
			</div>
			<div class="grid grid-cols-3 gap-3">
				<a-form-item label="审批请示名称" name="apprRequname" class="col-span-2">
					<a-input v-model:value="formData.apprRequname" allowClear placeholder="请输入审批请示名称" />
				</a-form-item>
			</div>
			<a-form-item label="最终决策文件" name="apprFinalfile">
				<OrderUpload v-model:value="formData.apprFinalfile" uploadText="请上传最终决策文件" />
			</a-form-item>
		</a-form>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
			<qt-button type="primary" @click="(next) => onSubmit(next)">保存</qt-button>
		</template>
		<a-modal v-model:open="open" title="新建下级" :width="400" :maskClosable="false" :centered="true">
			请确认是否在{{ curVersionTime }}新增下级
			<template #footer>
				<a-button
					type="primary"
					class="mr-2"
					@click="
						() => {
							open = false
							clearInterval(interval)
							time = 3
						}
					"
					:disabled="time > 0"
				>
					{{ time > 0 ? time + '秒' : '确认' }}
				</a-button>
				<a-button
					@click="
						() => {
							open = false
							onClose()
						}
					"
				>
					取消
				</a-button>
			</template>
		</a-modal>
	</xn-form-container>
	<AddApprove ref="addApproveRef" @successful="addApproveSuccessful" />
</template>

<script setup>
	import { message, Modal } from 'ant-design-vue'
	import { required } from '@/utils/formRules'
	import AddApprove from './addApprove.vue'
	import tool from '@/utils/tool'
	import { getListArr } from '@/utils/businessVerification'
	const Registration = tool.dictList('Registration Area')
	const filling_standards = tool.dictList('filling_standards')
	const License_type = tool.dictList('License Type')
	const License = computed(() => {
		if (formData.value.registarea1 == 'RA100000') {
			License_type.map((item) => {
				item.disabled = !(item.value == 'LT01')
				return item
			})
		} else if (formData.value.registarea1 == 'RA200000' || formData.value.registarea1 == 'RA300000') {
			License_type.map((item) => {
				item.disabled = item.value == 'LT01'
				return item
			})
		}
		return License_type
	})

	const addApproveRef = ref()
	// 定义emit事件
	const emit = defineEmits(['successful'])
	// 默认是关闭状态
	const time = ref(3)
	let interval = null
	const open = ref(false)
	const visible = ref(false)
	const formRef = ref()
	// 表单数据，也就是默认给一些数据
	const formData = ref({})
	const curVersionTime = ref('')
	// 打开抽屉
	const onOpen = (record) => {
		visible.value = true
		open.value = true
		curVersionTime.value = record
		interval = setInterval(() => {
			if (time.value >= 0) {
				time.value--
			} else {
				clearInterval(interval)
			}
		}, 1000)
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		formData.value = {}
		clearInterval(interval)
		time.value = 3
	}
	// 默认要校验的
	const formRules = {
		registarea1: [required('请选择注册地区')],
		registtype: [required('请选择注册证照类型')],
		registcode: [required('请输入注册证照代码')],
		fillingStandards: [required('请选择机构类别')],
		apprType: [required('请选择审批类型')],
		apprFilitime: [required('请选择审批备案时间')],
		apprRequname: [required('请输入审批请示名称')],
		apprFinalfile: [required('请上传最终决策文件')]
	}
	const appr_type = tool.dictList('appr_type')
	const addApproveSuccessful = (data) => {
		emit('successful', { ...formData.value, ...data })
		onClose()
	}

	// 验证并提交数据
	const onSubmit = (next) => {
		Object.keys(formData.value).forEach((key) => {
			if (formData.value[key] && typeof formData.value[key] === 'string')
				formData.value[key] = formData.value[key].trim()
		})
		formRef.value
			.validate()
			.then(async () => {
				if (formData.value.registarea1 == 'RA100000') {
					try {
						const arr = await getListArr({ registCode: formData.value?.registcode.trim(), isAdd: '1' })
						if (arr.length == 0) {
							Modal.confirm({
								title: '提示',
								content: '未找到有效的工商登记信息，请手动填写或检查统一社会信用代码是否正确',
								okText: '手动填写',
								cancelText: '取消',
								centered: true,
								onOk: () => {
									emit('successful', formData.value)
									onClose()
									next()
								},
								onCancel: () => {
									next()
								}
							})
						}
						arr.length && addApproveRef.value.onOpen(arr)
					} catch (e) {
						console.log(e)
						next()
					}
				} else {
					emit('successful', formData.value)
					onClose()
					next()
				}
			})
			.catch((error) => {
				console.log(error)
				if (error?.errorFields?.length) {
					message.error(error?.errorFields[0]?.errors[0])
					formRef.value && formRef.value.scrollToField(error?.errorFields[0]?.name)
				}
			})
			.finally(() => next())
	}
	defineExpose({
		onOpen
	})
</script>

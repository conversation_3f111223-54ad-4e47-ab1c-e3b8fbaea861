<template>
	<a-modal
		title="架构移动"
		:width="600"
		:bodyStyle="{ height: '600px !important', maxHeight: '600px !important', display: 'flex', flexDirection: 'column' }"
		v-model:open="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<auditTree
			ref="TreeRef"
			class="flex-1"
			:selectLeastOne="treeAttr.selectLeastOne"
			:treeData="treeData"
			:loading="treeAttr.loading"
			:keys="treeAttr.keys"
			:title="treeAttr.title"
			:expanded="treeAttr.expanded"
			:selected="treeAttr.selected"
			@treeSelect="treeSelect"
			@autoSelectNodeData="autoSelectNodeData"
			placeholder="请输入公司名称"
			:draggable="false"
		/>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">取消</a-button>
			<qt-button type="primary" @click="(next) => onSubmit(next)">确定</qt-button>
		</template>
	</a-modal>
</template>

<script setup>
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import auditTree from '@/components/Tree/auditTree.vue'
	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	// 定义emit事件
	const emit = defineEmits({ successful: null })

	const treeAttr = reactive({
		keys: 'id',
		title: 'simpOrgName',
		loading: false,
		selectLeastOne: true,
		selected: [], // 默认选中
		expanded: [], // 默认展开
		checked: [] // 默认勾选
	})
	const treeData = ref([])
	const treeNode = ref({})
	// 默认是关闭状态
	const visible = ref(false)
	const initNode = ref({})
	// 打开抽屉
	const onOpen = (node, type) => {
		visible.value = true
		treeNode.value[treeAttr.keys] = ''
		console.log(node)
		initNode.value = node
		loadTreeData(node.id, node.parentId, type)
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		treeNode.value[treeAttr.keys] = ''
	}
	const loadTreeData = async (id, parentId, type) => {
		// 处理树是否可编辑
		const disposeTree = async (tree) => {
			for (let i = 0; i < tree.length; i++) {
				tree[i].disabled = tree[i]?.isClickable == 1 ? false : true
				if (tree[i][treeAttr.keys] == parentId) {
					tree[i].disabled = true
				}
				if (tree[i][treeAttr.keys] == id) {
					tree[i].disabled = true
					delete tree[i].children
				}
				if (tree[i]?.children) {
					disposeTree(tree[i]?.children)
				}
			}
		}
		treeAttr.loading = true
		bizInstitutionApi
			.getStructureTree({ structureType: '0', isWhereFilling: '1' })
			// .getStructureOrgnList({ isWhereFilling: '1' })
			.then(async (res) => {
				if (res?.length) {
					await disposeTree(res)
					treeData.value = res
					treeAttr.expanded.push(res[0][treeAttr.keys])
					treeAttr.loading = false
					// localStorage.setItem('treeData', JSON.stringify(res))
				}
			})
			.finally(() => {
				treeAttr.loading = false
			})
	}

	const treeSelect = ({ selectedKeys, data }) => {
		treeNode.value = data.node
	}

	const autoSelectNodeData = (data) => {
		treeNode.value = data
	}
	const onSubmit = (next) => {
		next()
		if (!treeNode.value[treeAttr.keys]) return message.error('请选择机构')
		emit('successful', cloneDeep(treeNode.value))
		onClose()
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>

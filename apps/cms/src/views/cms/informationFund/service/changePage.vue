<template>
	<xn-form-container
		title="变更查询"
		:width="1200"
		:maxHeight="0.9"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="searchFormRef" name="advanced_search" class="ant-advanced-search-form mb-3" :model="searchFormState">
			<p>请选择2023年6月及以后期间对比或者2023年5月及以前期间进行对比</p>
			<div class="grid grid-cols-4 gap-4">
				<a-form-item name="searchKey" label="版本变更日期1">
					<a-date-picker
						v-model:value="searchFormState.searchKey"
						picker="month"
						valueFormat="YYYY-MM"
						placeholder="请选择变更时间"
					/>
				</a-form-item>
				<a-form-item name="searchKey2" label="版本变更日期2">
					<a-date-picker
						v-model:value="searchFormState.searchKey2"
						picker="month"
						valueFormat="YYYY-MM"
						placeholder="请选择变更时间"
					/>
				</a-form-item>
				<a-form-item name="searchKey3" label="查询范围">
					<TreeSelect
						v-model:value="searchFormState.searchKey3"
						:tree-data="treeData"
						:field-names="{
							children: 'children',
							label: 'name',
							value: 'value'
						}"
						searchKey="name"
					/>
				</a-form-item>
				<a-form-item name="searchKey4" label="变更类型">
					<Select v-model:value="searchFormState.searchKey3" :options="option" />
				</a-form-item>
				<a-form-item name="searchKey5" label="企业名称">
					<a-input v-model:value="searchFormState.searchKey5" placeholder="请输入企业名称" />
				</a-form-item>
				<div class="flex gap-2">
					<a-button shape="default" type="primary" class="snowy-button-left" @click="reset"> 比较 </a-button>
					<a-button shape="default" type="primary" class="snowy-button-left" @click="reset"> 重置筛选 </a-button>
				</div>
			</div>
		</a-form>
		<div class="flex-1">
			<SearchTable
				ref="tableRef"
				:columns="columns"
				:api="orgApi.bizOrgPage"
				:hasSearch="false"
				:expand-row-by-click="true"
				bordered
				:row-key="(record) => record.id"
				:searchInfo="searchFormState"
			>
				<template #bodyRender="{ column, record }">
					<!-- <template v-if="column.dataIndex === 'category'">
						{{ $TOOL.dictTypeData('ORG_CATEGORY', record.category) }}
					</template> -->
				</template>
			</SearchTable>
		</div>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="dictForm" lang="jsx">
	import { CheckCircleOutlined } from '@ant-design/icons-vue'
	import { required } from '@/utils/formRules'
	import orgApi from '@/api/sys/orgApi'
	// 默认是关闭状态
	const style = reactive({
		color: 'green'
	})
	const visible = ref(false)
	const searchFormState = ref({})
	const searchFormRef = ref()
	const tableRef = ref()
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		}
	}
	const columns = [
		{
			title: '序号',
			width: 80,
			fixed: 'left',
			align: 'center',
			dataIndex: 'id',
			customRender: ({ index }) => `${index + 1}`
		},
		{
			title: '企业唯一编码',
			dataIndex: 'id',
			width: 150
		},
		{
			title: '企业全称(简体中文)',
			dataIndex: 'name',
			width: 240
		},
		{
			title: '持股比例最高的内部股东',
			dataIndex: 'people',
			width: 240
		},
		{
			title: '所属管理主体',
			dataIndex: 'a',
			width: 150
		},
		{
			title: '法人层级',
			dataIndex: 'people2',
			align: 'center',
			width: 100
		},
		{
			title: '管理层级',
			dataIndex: 'p',
			align: 'center',
			width: 100
		},
		{
			title: '关注字段变更',
			dataIndex: 'v',
			align: 'center',
			width: 150,
			customRender: ({ text, record }) => {
				return <CheckCircleOutlined style={style} />
			}
		},
		{
			title: '工商信息',
			dataIndex: 'q',
			width: 200
		},
		{
			title: '股权关系',
			dataIndex: 'w',
			width: 150
		},
		{
			title: '管理职能',
			dataIndex: 'e',
			width: 150
		},
		{
			title: '人员信息',
			dataIndex: 'r',
			width: 150
		},
		{
			title: '新建',
			dataIndex: 's',
			align: 'center',
			width: 100,
			customRender: ({ text, record }) => {
				return <CheckCircleOutlined style={style} />
			}
		}
	]
	const option = ref([
		{
			label: '选项1',
			value: '1'
		},
		{
			label: '选项2',
			value: '2'
		},
		{
			label: '选项3',
			value: '3'
		}
	])
	const treeData = ref([
		{
			name: 'parent 1',
			value: 'parent 1',
			children: [
				{
					name: 'parent 1-0',
					value: 'parent 1-0',
					children: [
						{
							name: 'my leaf',
							value: 'leaf1'
						},
						{
							name: 'your leaf',
							value: 'leaf2'
						}
					]
				},
				{
					name: 'parent 1-1',
					value: 'parent 1-1'
				}
			]
		}
	])

	// 打开抽屉
	const onOpen = (record, type, parentId) => {
		visible.value = true
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style lang="less" scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.ant-picker {
		width: 100%;
	}
	.ant-modal-body {
		display: flex;
		flex-flow: column nowrap;
	}
</style>

<template>
	<xn-form-container title="处置" :width="750" :visible="visible" :destroy-on-close="true" @close="onClose">
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<p class="ml-[-10px] mb-[5px] text-16 font-bold">处置情形</p>
			<a-form-item label="" name="type" class="col-span-2">
				<Select
					class="w-full"
					:options="Disposal_situation"
					v-model:value="formData.type"
					placeholder="请选择处置情形"
					@change="typeChange"
				/>
			</a-form-item>
			<p class="ml-[-10px] mb-0 text-16 font-bold" v-if="formData.type && formData.type != 'Disposal_situation1'">
				机构类别变更选择
			</p>
			<a-form-item label="" name="nowDisposalType" v-if="formData.type && formData.type != 'Disposal_situation1'">
				<a-radio-group v-model:value="formData.nowDisposalType" class="flex flex-col">
					<a-radio
						:style="{
							display: 'flex',
							height: '33px',
							lineHeight: '33px'
						}"
						v-for="(item, index) in filling_standards"
						:value="item.value"
						:key="item.value"
						:disabled="item.disabled"
					>
						{{ item.label }}
					</a-radio>
				</a-radio-group>
			</a-form-item>
			<p class="ml-[-10px] mb-[5px] text-16 font-bold">处置信息填报</p>
			<a-form-item label="退出时间" name="exitTime" class="col-span-2">
				<a-date-picker
					class="w-full"
					v-model:value="formData.exitTime"
					valueFormat="YYYY-MM-DD"
					allowClear
					placeholder="请选择退出时间"
				/>
			</a-form-item>
			<a-form-item label="退出程度" name="exitDegree" class="col-span-2">
				<Select
					class="w-full"
					:options="cancel_progress"
					v-model:value="formData.exitDegree"
					placeholder="请选择退出程度"
					:disabled="typeComputed"
				/>
			</a-form-item>
			<a-form-item label="退出方式" name="exitWay" class="col-span-2">
				<Select
					class="w-full"
					:options="cancel_type"
					v-model:value="formData.exitWay"
					:disabled="typeComputed"
					placeholder="请选择退出方式"
				/>
			</a-form-item>
			<a-form-item label="退出原因" name="exitReason" class="col-span-2">
				<Select
					class="w-full"
					:options="cancel_cause"
					v-model:value="formData.exitReason"
					placeholder="请选择退出原因"
					:disabled="typeComputed"
				/>
			</a-form-item>
			<a-form-item label="备注" name="remark" class="col-span-2">
				<a-textarea v-model:value="formData.remark" allowClear placeholder="请输入备注" />
			</a-form-item>
			<a-form-item label="证明文件" name="proveFile">
				<OrderUpload v-model:value="formData.proveFile" uploadText="请上传证明文件" />
			</a-form-item>
		</a-form>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
			<qt-button type="primary" @click="(next) => onSubmit(next)" :disabled="submitBtn">保存</qt-button>
		</template>
	</xn-form-container>
</template>

<script setup>
	import { message, Modal } from 'ant-design-vue'
	import { required } from '@/utils/formRules'
	import bizEntityDisposalApi from '@/api/biz/bizEntityDisposalApi.js'
	import bizInstitutionApi from '@/api/biz/bizInstitutionApi'
	import tool from '@/utils/tool'
	const Disposal_situation = ref(tool.dictList('Disposal_situation'))
	const filling_standards = ref(tool.dictList('filling_standards'))
	const cancel_progress = tool.dictList('cancel_progress')
	const cancel_type = tool.dictList('cancel_type')
	const cancel_cause = tool.dictList('cancel_cause')
	const addApproveRef = ref()
	// 定义emit事件
	const emit = defineEmits({ successful: null })
	const typeResult = ref(false)
	const submitBtn = computed(() => {
		console.log(formData.value.type == 'Disposal_situation1' && typeResult.value)
		return (
			(formData.value.type == 'Disposal_situation1' || formData.value.type == 'Disposal_situation3') && typeResult.value
		)
	})
	const visible = ref(false)
	const formRef = ref()
	// 表单数据，也就是默认给一些数据
	const formData = ref({})

	const oldType = ref('')
	// 打开抽屉
	const onOpen = (record, orgId) => {
		visible.value = true
		console.log(record)

		formData.value = {
			...formData.value,
			...{
				ywId: record.id,
				entityId: record.entityId,
				versionId: record.versionId,
				beforeDisposalType: record.fillingStandards,
				parentId: record.organizationParentId
			}
		}
		filling_standards.value.map((item) => {
			item.disabled = item.value == record.fillingStandards
			return item
		})
		Disposal_situation.value.map((item) => {
			item.disabled = item.value == 'Disposal_situation3' && record.fillingStandards == 'filling_standards_4'
			return item
		})
		bizInstitutionApi.getFillingByGDVersion({ entityId: record.entityId }).then((res) => {
			oldType.value = res
		})
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		formData.value = {}
	}
	const typeComputed = computed(() => {
		return formData.value.type == 'Disposal_situation3'
	})
	// 默认要校验的
	const formRules = computed(() => {
		return {
			type: [required('请选择处置情形')],
			nowDisposalType: [required('请选择机构类别变更')],
			exitTime: [required('请选择退出时间')],
			exitDegree: [required('请选择退出程度')],
			exitWay: [required('请选择退出方式')],
			exitReason: [required('请选择退出原因')],
			remark: typeComputed.value ? [required('请输入备注')] : [],
			proveFile: typeComputed.value ? [] : [required('请上传证明文件')]
		}
	})

	const typeChange = async (value) => {
		if (value == 'Disposal_situation1') {
			const res = await bizEntityDisposalApi.isTrueByOrgDisposal({
				ywId: formData.value.ywId,
				entityId: formData.value.entityId,
				versionId: formData.value.versionId
			})
			if (
				res.isOtherConsolidation == 1 ||
				res.isOtherParentunit == 1 ||
				res.isOtherInternal == 1 ||
				res.isOtherFilier == 1 ||
				res.isOrgnaizationChild == 1
			) {
				typeResult.value = true
				Modal.warning({
					title: '无法执行操作',
					content: h('div', {}, [
						res.isOtherConsolidation == 1 ? h('p', '本机构是其他机构上级并表单位，无法处置!') : '',
						res.isOtherParentunit == 1 ? h('p', '本机构是其他机构上级管理单位，无法处置!') : '',
						res.isOtherInternal == 1 ? h('p', '本机构是其他机构内部股东，无法处置!') : '',
						res.isOtherFilier == 1 ? h('p', '本机构是其他机构填报人所在机构，无法处置!') : '',
						res.isOrgnaizationChild == 1 ? h('p', '本机构有股权架构下级机构，无法处置!') : ''
					]),
					okText: '确认'
				})
			} else {
				typeResult.value = false
				message.success('校验通过!')
			}
		} else if (value == 'Disposal_situation2') {
			if (value == 'Disposal_situation2') {
				filling_standards.value.map((item) => {
					item.disabled = item.value == 'filling_standards_4' || item.value == formData.value.beforeDisposalType
					return item
				})
			}
			formData.value.exitDegree = null
			formData.value.exitWay = null
			formData.value.exitReason = null
		} else if (value == 'Disposal_situation3') {
			filling_standards.value.map((item) => {
				item.disabled = item.value != 'filling_standards_4'
				return item
			})
			formData.value.exitDegree = 'cancel_progress_3'
			formData.value.exitWay = 'cancel_type_5'
			formData.value.exitReason = 'cancel_cause_6'
			bizEntityDisposalApi
				.isTrueRemoveByOrgDisposal({
					ywId: formData.value.ywId,
					entityId: formData.value.entityId,
					versionId: formData.value.versionId
				})
				.then((res) => {
					console.log(res)
					typeResult.value = true
					if (
						res.isOtherConsolidation == 1 ||
						res.isOtherParentunit == 1 ||
						res.isOtherInternal == 1 ||
						res.isOtherFilier == 1 ||
						res.isOrgnaizationChild == 1
					) {
						Modal.warning({
							title: '无法执行操作',
							content: h('div', {}, [
								res.isOtherConsolidation == 1 ? h('p', '本机构是其他机构上级并表单位，无法处置!') : '',
								res.isOtherParentunit == 1 ? h('p', '本机构是其他机构上级管理单位，无法处置!') : '',
								res.isOtherInternal == 1 ? h('p', '本机构是其他机构内部股东，无法处置!') : '',
								res.isOtherFilier == 1 ? h('p', '本机构是其他机构填报人所在机构，无法处置!') : '',
								res.isOrgnaizationChild == 1 ? h('p', '本机构有股权架构下级机构，无法处置!') : '',
								res.isTrueRemoveChild == 1 ? h('p', '本机构下级机构存在非”不纳入登记范围”的机构，无法处置!') : ''
							]),
							okText: '确认'
						})
					} else {
						typeResult.value = false
						message.success('校验通过!')
					}
				})
		} else {
			formData.value.exitDegree = null
			formData.value.exitWay = null
			formData.value.exitReason = null
		}
	}
	// 验证并提交数据
	const onSubmit = (next) => {
		formRef.value
			.validate()
			.then(() => {
				const params = {
					...formData.value,
					...{
						nowDisposalType: formData.value.type == 'Disposal_situation1' ? 'remove' : formData.value.nowDisposalType,
						disposalTypeChange: !oldType.value
							? ''
							: oldType.value == formData.value.nowDisposalType
							  ? ''
							  : oldType.value?.slice(-1)
					}
				}
				bizEntityDisposalApi
					.save(params)
					.then((res) => {
						console.log(res)
						emit('successful', {
							disabled: formData.value.type != 'Disposal_situation2',
							// tabActive: formData.value.type != 'Disposal_situation2' ? '3' : '0',
							status: res?.isDelete == 1
						})
						onClose()
					})
					.finally(() => {
						next()
					})
			})
			.catch((error) => {
				if (error?.errorFields?.length) {
					message.error(error?.errorFields[0]?.errors[0])
					formRef.value && formRef.value.scrollToField(error?.errorFields[0]?.name)
				}
				console.log(error)
				next()
			})
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>

<template>
	<xn-form-container
		title="历史版本"
		:width="1200"
		maxHeight="none"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-spin :spinning="loading">
			<div class="grid grid-cols-4 gap-4 items-center ml-3">
				<a-form-item name="searchKey" label="版本变更日期">
					<a-date-picker
						v-model:value="searchDate"
						picker="month"
						valueFormat="YYYY-MM"
						:disabled-date="disabledDate"
						placeholder="请选择变更时间"
						:allowClear="false"
						@change="() => mainRef.clearSelectTree()"
					/>
				</a-form-item>
				<div class="flex gap-2">
					<QtButton ghost type="primary" @click="(next) => exportFn(next)">
						<template #icon><ExportOutlined /></template>导出
					</QtButton>
				</div>
			</div>
			<div class="h-[500px] flex flex-col">
				<Main ref="mainRef" class="flex-1" :disabled="true" :searchForm="searchForm" v-if="dateArr.length" />
				<a-empty :image="simpleImage" v-else />
			</div>
		</a-spin>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="dictForm" lang="jsx">
	import { message, Empty } from 'ant-design-vue'
	import { ExportOutlined } from '@ant-design/icons-vue'
	import downloadUtil from '@/utils/downloadUtil.js'
	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	import bizVersionApi from '@/api/biz/bizVersionApi.js'
	import Main from './components/main.vue'

	const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
	const mainRef = ref()
	const visible = ref(false)
	const searchDate = ref()
	const searchForm = computed(() => {
		let obj = {
			year: searchDate.value ? searchDate.value?.split('-')[0] : '',
			month: searchDate.value ? Number(searchDate.value?.split('-')[1]).toString() : '',
			date: searchDate.value ? searchDate.value : '',
			isHistory: searchDate.value ? true : false
		}
		return obj
	})
	const dateArr = ref([])
	const loading = ref(false)
	// 打开抽屉
	const onOpen = async () => {
		visible.value = true
		loading.value = true
		bizVersionApi
			.listYearMonth()
			.then((res) => {
				if (res.length) {
					dateArr.value = res?.map((item) => {
						const arr = item.split('-')
						item = Number(arr[1]) < 10 ? `${arr[0]}-0${arr[1]}` : `${arr[0]}-${arr[1]}`
						return item
					})
					searchDate.value = dateArr.value[0]
				} else {
					message.warning('暂无历史版本')
				}
			})
			.finally(() => {
				loading.value = false
			})
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		dateArr.value = []
		searchDate.value = null
	}
	// 打开抽屉
	const open = (entityId) => {
		visible.value = true
		// loading.value = true
		mainRef.value.getDetail(null, entityId)
	}

	const disabledDate = (current) => {
		const dateStr = current.format('YYYY-MM')
		// console.log(current)
		return !dateArr.value.includes(dateStr)
	}
	const exportFn = (next) => {
		if (!mainRef.value.getNode()?.id) {
			next()
			return message.warning('请选择机构!')
		}
		const data = mainRef.value?.data
		console.log(data)
		if (!data?.versionId || !data?.entityId) {
			next()
			return message.warning('未获取到版本号或者版本对应股权主键!')
		}
		bizInstitutionApi
			.exportShEntityInfo({ versionId: data?.versionId, exportType: 2, entityId: data?.entityId })
			.then((res) => {
				downloadUtil.resultDownload(res)
			})
			.finally(() => {
				next()
			})
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen,
		open
	})
</script>
<style lang="less" scoped>
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.ant-picker {
		width: 100%;
	}
	.ant-modal-body {
		display: flex;
		flex-flow: column nowrap;
	}
</style>

<template>
	<a-skeleton
		:loading="!plateCountVos"
		active
		:title="false"
		:paragraph="{ rows: 13 }"
		class="flex flex-1 flex-row justify-center items-center px-2 box-border"
	>
		<div class="flex flex-col w-full box-border" id="access">
			<div class="px-3 bg-[#fff7f7] pt-3"><CardTitle title="板块 (并表)" /></div>
			<div
				class="w-full flex flex-row flex-1 justify-center items-center overflow-hidden card-plate"
				:class="!keys(plateCountVos)?.length ? 'bg-[#fff7f7]' : ''"
			>
				<a-empty v-if="keys(plateCountVos)?.length == 0"></a-empty>
				<div
					class="flex overflow-x-auto flex-row w-full h-full justify-between items-center grid grid-cols-2 plate-card-container"
					v-else
				>
					<div
						class="plate-card flex-1 shrink-0 flex flex-col justify-center items-center"
						v-for="(item, index) in sectorData"
						:key="item.value || index"
					>
						<img
							style="width: 20px; height: 20px"
							class="rounded-sm"
							:src="plateCountVos[item.value]?.icon || iconMap[item.value]"
						/>
						<div class="flex justify-center items-center">
							<div class="access-title mt-3">{{ plateCountVos[item.value]?.sectorName || item.label }}</div>
						</div>
						<div class="custom-number mt-2">{{ plateCountVos[item.value]?.count || '一' }}</div>
					</div>
				</div>
			</div>
		</div>
	</a-skeleton>
</template>

<script setup>
	import { map, cloneDeep, keyBy, keys } from 'lodash-es'
	import bkbb from '@/views/cms/home/<USER>/CustomContent/images/bkbb.png'
	import icon_zhjrfw from '@/views/cms/home/<USER>/CustomContent/images/icon_zhjrfw.png'
	import icon_xjzz from '@/views/cms/home/<USER>/CustomContent/images/icon_xjzz.png'
	import icon_xjcl from '@/views/cms/home/<USER>/CustomContent/images/icon_xjcl.png'
	import icon_xxf from '@/views/cms/home/<USER>/CustomContent/images/icon_xxf.png'
	import icon_xxczh from '@/views/cms/home/<USER>/CustomContent/images/icon_xxczh.png'
	import icon_ptgs from '@/views/cms/home/<USER>/CustomContent/images/icon_ptgs.png'
	import dqbb from '@/views/cms/home/<USER>/CustomContent/images/dqbb.png'
	import icon_jw from '@/views/cms/home/<USER>/CustomContent/images/icon_jw.png'
	import icon_jn from '@/views/cms/home/<USER>/CustomContent/images/icon_jn.png'
	import tool from '@/utils/tool'
	import CardTitle from './CardTitle.vue'

	const props = defineProps({
		areaCountVos: {
			type: Array,
			default: () => []
		},
		groupPlateCountVos: {
			type: Array,
			default: () => []
		}
	})
	const plateCountVos = ref(null)
	const areaVos = ref({})
	const loading = ref(false)
	const sectorData = ref([])
	const iconMap = {
		sector_1: icon_zhjrfw,
		sector_2: icon_xjzz,
		sector_3: icon_xjcl,
		sector_4: icon_xxf,
		sector_5: icon_xxczh,
		sector_6: icon_ptgs
	}
	watch(
		() => props.groupPlateCountVos,
		(newVal) => {
			if (newVal && newVal.length > 0) {
				sectorData.value = tool.dictList('homepage_block_type') || []
				const keyBySector = keyBy(sectorData.value, 'value') || {}
				plateCountVos.value = keyBy(
					map(cloneDeep(newVal), (val) => {
						val.sectorName = keyBySector[val.item]?.label || ''
						val.icon = iconMap[val.item] || ''
						return val
					}),
					'item'
				)
			} else {
				plateCountVos.value = {}
			}
		},
		{ deep: true }
	)
	watch(
		() => props.areaCountVos,
		(newVal) => {
			sectorData.value = tool.dictList('homepage_block_type') || []
			if (newVal && newVal.length > 0) {
				areaVos.value = keyBy(cloneDeep(newVal), 'item') || {}
			}
		},
		{ deep: true }
	)
	loading.value = true
	// api
	// 	.substationTodayCount()
	// 	.then((res) => {
	// 		todayNumbers.value = res?.todayNumbers
	// 		todayTimes.value = res?.todayTimes
	// 		loading.value = false
	// 	})
	// 	.finally(() => {
	// 		loading.value = false
	// 	})
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		// min-height: 116px;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-size: 16px;
		font-weight: 400;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		color: #2a2c3990;
	}
	.card-plate {
		border-radius: 0 0 0.5rem 0.5rem;
	}
	.plate-card-container {
		width: 100%;
		// background-color: #696b7430;
		gap: 1px;
	}
	.plate-card {
		width: 100%;
		height: 100%;
		min-height: 120px;
		background: #fff7f7;
		//box-shadow: -19px -12px 20px 15px rgb(234 238 243 / 32%);
		// background: rgba(255, 255, 255, 0.7);
	}
</style>

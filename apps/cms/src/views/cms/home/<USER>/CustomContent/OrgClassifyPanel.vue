<template>
	<a-skeleton
		:loading="!typeVos"
		active
		:title="false"
		:paragraph="{ rows: 6 }"
		class="flex flex-1 flex-row justify-center items-center px-2 box-border"
	>
		<div class="flex flex-col box-border w-full h-full p-3">
			<div class="flex items-center">
				<CardTitle title="机构类别" class="shrink-0" />
				<div class="flex-1 flex justify-center items-center text-[13px] text-[#d3000f] font-[800] flex-wrap">
					纳入登记范围的机构总数: {{ totalOrgCount || '一' }}
				</div>
			</div>
			<div
				class="chart-data relative flex-1 w-full"
				:class="!showChart ? 'flex flex-col justify-center items-center' : ''"
			>
				<template v-if="showChart">
					<v-chart class="h-full w-full" :option="classifyOption" autoresize />
				</template>
				<a-empty v-else></a-empty>
			</div>
		</div>
	</a-skeleton>
</template>
<script setup>
	import { map, cloneDeep, keyBy, keys } from 'lodash-es'
	import icon_qjcbb from '@/views/cms/home/<USER>/CustomContent/images/icon_qjcbb.png'
	import icon_szglcgbb from '@/views/cms/home/<USER>/CustomContent/images/icon_szglcgbb.png'
	import icon_bbqy from '@/views/cms/home/<USER>/CustomContent/images/icon_bbqy.png'
	import tool from '@/utils/tool'
	import CardTitle from './CardTitle.vue'

	import { LegendComponent, GridComponent, DatasetComponent, TooltipComponent } from 'echarts/components'
	import VChart from 'vue-echarts'
	import { use } from 'echarts/core'
	import { CanvasRenderer } from 'echarts/renderers'
	import { PieChart } from 'echarts/charts'

	use([PieChart, LegendComponent, CanvasRenderer, DatasetComponent, GridComponent, TooltipComponent])
	const fillingStandardsData = ref([])
	const showChart = ref(false)
	const totalOrgCount = ref(0)
	const props = defineProps({
		typeCountVos: {
			type: Array,
			default: () => []
		}
	})

	const iconMap = {
		filling_standards_1: icon_qjcbb,
		filling_standards_2: icon_szglcgbb,
		filling_standards_3: icon_bbqy
	}
	const typeVos = ref(null)
	watch(
		() => props.typeCountVos,
		(newVal) => {
			if (newVal && newVal.length > 0) {
				fillingStandardsData.value = tool.dictList('homepage_org_type')
				let sum = 0
				const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
				typeVos.value = map(cloneDeep(newVal), (val) => {
					val.name = keyByTypeVos[val.item]?.label || ''
					val.icon = iconMap[val.item] || ''
					val.value = val.count
					if (val.item !== 'filling_standards_4') {
						sum += val.count || 0
					}
					return val
				})
				totalOrgCount.value = sum
				showChart.value = true
			} else {
				typeVos.value = []
				showChart.value = false
			}
		},
		{ deep: true }
	)
	const classifyOption = computed(() => {
		return {
			tooltip: {
				trigger: 'item'
			},
			color: ['#FF534F', '#6b85ed', '#e9c5b1', '#00BFA5'],
			legend: {
				orient: 'vertical', //设置图例的方向
				left: 'left',
				top: 'center',
				itemGap: 15, //设置图例的间距
				itemWidth: 14, // 设置图例图形宽度
				itemHeight: 14, // 设置图例图形高度
				textStyle: {
					fontSize: 12,
					color: '#333',
					fontWeight: 600
				},
				formatter: function (name) {
					return name
				},
				rich: {
					symbol: {
						width: 14,
						height: 14,
						borderRadius: 7,
						align: 'left',
						backgroundColor: {
							image:
								'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ij48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMSIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+PC9zdmc+'
						}
					}
				}
			},
			series: [
				{
					type: 'pie',
					radius: ['30%', '80%'],
					center: ['71%', '50%'],
					avoidLabelOverlap: false,
					itemStyle: {
						borderColor: '#fff',
						borderWidth: 0,
						shadowBlur: 3,
						shadowOffsetX: 2,
						shadowColor: 'rgba(218,41,37,0.4)'
					},
					label: {
						position: 'inner',
						formatter: '{text|{c}}',
						rich: {
							text: {
								color: '#fff',
								lineHeight: 22,
								align: 'center',
								borderType: [5, 10],
								borderDashOffset: 5,
								fontSize: 12,
								fontWeight: 700
							}
						}
					},
					emphasis: {
						label: {
							show: true,
							fontSize: '10',
							fontWeight: 'bold'
						}
					},
					labelLine: {
						show: false
					},
					data: typeVos.value
				}
			]
		}
	})
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		// min-height: 116px;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-weight: 500;
		font-size: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	/deep/ .ant-statistic .ant-statistic-content {
		color: #fff;
		font-size: 40px;
	}
	.short-divider {
		border-color: #000;
		height: 16px;
		border-width: 1px;
		margin: 0 4px;
	}
</style>

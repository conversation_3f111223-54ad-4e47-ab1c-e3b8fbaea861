<template>
	<div class="flex w-full gap-5" id="access">
		<div class="left-panel flex flex-col justify-center items-center overflow-hidden rounded-lg gap-5 box-border">
			<div class="org-panel left-panel w-full flex flex-row justify-center items-center overflow-hidden rounded-lg">
				<NewAndDisposeOrgPanel />
			</div>
			<div class="left-panel w-full flex flex-row justify-center items-center overflow-hidden rounded-lg gap-5">
				<div class="org-panel h-full flex-1 flex flex-row justify-center items-center overflow-hidden rounded-lg">
					<!-- <OrgClassifyPanel :typeCountVos="typeCountVos" /> -->
					<ManageLevelChart :dataVos="managementLevelVos" />
				</div>
				<div class="org-panel h-full flex-1 flex flex-row justify-center items-center overflow-hidden rounded-lg">
					<!-- <AreaClassifyPanel :areaCountVos="areaCountVos" /> -->
					<LegalEntityChart :dataVos="legalEntityLevelVos" />
				</div>
			</div>
		</div>
		<div
			class="right-panel flex flex-row justify-center items-center overflow-hidden rounded-lg"
			:class="!groupPlateCountVos?.length ? 'bg-[#fff7f7]' : 'bg-[#dddadacc]'"
		>
			<PlatePanel :groupPlateCountVos="groupPlateCountVos" />
		</div>
	</div>
</template>
<script setup>
	import { map, cloneDeep, keyBy, keys } from 'lodash-es'
	import icon_qjcbb from '@/views/cms/home/<USER>/CustomContent/images/icon_qjcbb.png'
	import icon_szglcgbb from '@/views/cms/home/<USER>/CustomContent/images/icon_szglcgbb.png'
	import icon_bbqy from '@/views/cms/home/<USER>/CustomContent/images/icon_bbqy.png'
	import tool from '@/utils/tool'

	import PlatePanel from '@/views/cms/home/<USER>/CustomContent/PlatePanel.vue'
	import LegalEntityChart from '@/views/cms/home/<USER>/CustomContent/LegalEntityChart.vue'
	import ManageLevelChart from '@/views/cms/home/<USER>/CustomContent/ManageLevelChart.vue'
	import NewAndDisposeOrgPanel from '@/views/cms/home/<USER>/CustomContent/NewAndDisposeOrgPanel.vue'

	const fillingStandardsData = ref([])
	const loading = ref(false)

	const props = defineProps({
		groupPlateCountVos: {
			type: Array,
			default: () => []
		},
		legalEntityLevelVos: {
			type: Array,
			default: () => []
		},
		managementLevelVos: {
			type: Array,
			default: () => []
		}
	})

	const iconMap = {
		filling_standards_1: icon_qjcbb,
		filling_standards_2: icon_szglcgbb,
		filling_standards_3: icon_bbqy
	}
	const typeVos = ref(null)
	const incrAndDecrVos = ref(null)
	watch(
		() => props.typeCountVos,
		(newVal) => {
			if (newVal && newVal.length > 0) {
				fillingStandardsData.value = tool.dictList('homepage_org_type')
				const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
				typeVos.value = keyBy(
					map(cloneDeep(newVal), (val) => {
						val.name = keyByTypeVos[val.item]?.label || ''
						val.icon = iconMap[val.item] || ''
						return val
					}),
					'item'
				)
			} else {
				typeVos.value = {}
			}
		},
		{ deep: true }
	)

	loading.value = true
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		// min-height: 116px;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-weight: 500;
		font-size: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	/deep/ .ant-statistic .ant-statistic-content {
		color: #fff;
		font-size: 40px;
	}
	.short-divider {
		border-color: #000;
		height: 16px;
		border-width: 1px;
		margin: 0 4px;
	}
	.left-panel {
		flex: 2.06;
	}
	.right-panel {
		flex: 1.01;
		// background: #e9dfdf;
	}
</style>

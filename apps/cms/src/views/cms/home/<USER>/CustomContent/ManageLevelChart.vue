<template>
	<a-skeleton
		:loading="!industryData"
		active
		:title="false"
		:paragraph="{ rows: 6 }"
		class="flex flex-1 flex-row justify-center items-center px-2 box-border"
	>
		<div class="flex flex-col box-border w-full h-full p-3">
			<CardTitle title="管理层级（并表）" />
			<div
				class="chart-data relative flex-1 w-full"
				:class="!showChart ? 'flex flex-col justify-center items-center' : ''"
			>
				<template v-if="showChart">
					<v-chart class="h-full w-full" :option="industryOption" autoresize />
				</template>
				<a-empty v-else></a-empty>
			</div>
		</div>
	</a-skeleton>
</template>
<script setup>
	import { ref, computed, watch, onMounted } from 'vue'
	import { map, find, maxBy, sortBy, keyBy } from 'lodash-es'
	import VChart from 'vue-echarts'
	import { use } from 'echarts/core'
	import { BarChart, LineChart } from 'echarts/charts'
	import { CanvasRenderer } from 'echarts/renderers'
	import {
		LegendComponent,
		GridComponent,
		DatasetComponent,
		TooltipComponent,
		DataZoomComponent
	} from 'echarts/components'

	import tool from '@/utils/tool'
	import portraitApi from '@/api/biz/portraitApi.js'
	import CardTitle from './CardTitle.vue'

	const isOrNo = tool.dictList('WHETHER')
	const props = defineProps({
		dataVos: {
			type: Array,
			default: () => []
		}
	})

	use([
		LineChart,
		LegendComponent,
		CanvasRenderer,
		BarChart,
		DatasetComponent,
		GridComponent,
		TooltipComponent,
		DataZoomComponent
	])
	const showChart = ref(false)
	const industryData = ref(null)
	const industryXaxisData = ref([])
	const industryYaxisData = ref([])

	const handleCategoryMouseOver = (e) => {
		initCategoryData(e.data)
	}
	const handleCategoryMouseOut = (e) => {
		initCategoryData(props.infoData || {})
	}
	const handleCategoryHighlight = (e, ...rest) => {
		if (e?.type == 'highlight') {
			const selectedData = keyByIndustryData.value?.[e?.name]
			initCategoryData(selectedData)
		}
	}
	const handleCategoryDownplay = (e) => {
		if (e?.type == 'downplay') {
			initCategoryData(props.infoData || {})
		}
	}

	const handleProfitMouseOver = (e) => {
		if (e.componentType == 'xAxis') {
			// basicInfo.value = initBasicInfo(originProfitData.value[e.dataIndex] || {})
		}
	}
	const handleProfitMouseOut = (e) => {
		basicInfo.value = initBasicInfo(props.infoData || {})
	}
	const initBasicInfo = (infoData = {}) => {
		const loss_type = tool.dictList('loss_type')
		const lossType = find(loss_type, (item) => item.value == infoData.lossType)?.label || ''
		return [
			{
				label: '是否亏损',
				value: infoData.hasLoss ? (infoData.hasLoss == 'Y' ? '是' : '否') : '-',
				type: 'hasLoss'
			},
			{
				label: '亏损类型',
				value: infoData.hasLoss ? (infoData.hasLoss == 'Y' ? lossType || '-' : '-') : '-',
				type: 'lossType'
			}
		]
	}
	const basicInfo = ref(initBasicInfo({}))
	const currentSelectedCategory = ref({
		first: '-',
		second: '-',
		third: '-',
		fourth: '-'
	})
	const initCategoryData = (infoData = {}) => {}
	const keyByIndustryData = computed(() => keyBy(industryData.value, 'name'))
	const initIndustryData = (data = []) => {
		const totalName = []
		const totalValue = []
		const managementLevel = keyBy(tool.dictList('management_level'), 'value') || {}
		const totalData = map(
			sortBy(data, (item) => +item.item),
			(item, index) => {
				const name = managementLevel[item.item]?.label
				const value = item.count
				totalName.push(name ? `层级${name}` : '-')
				totalValue.push(value)
				const temp = {
					name,
					value,
					...item
				}
				return temp
			}
		)
		industryXaxisData.value = totalName
		industryYaxisData.value = totalValue
		industryData.value = totalData
	}
	const viewportLength = computed(() => {
		return industryXaxisData.value.length > 6 ? 30 : 100
	})
	const industryOption = computed(() => ({
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		xAxis: {
			type: 'category',
			data: industryXaxisData.value,
			axisTick: {
				show: false
			},
			axisLabel: {
				// width: 30,
				// overflow: 'truncate',
				// ellipsis: '..'
				interval: 0,
				fontSize: 11,
				formatter: (val) => {
					let txt = val
					if (val.length > 4) {
						txt = val.substr(0, 4) + '...'
					}
					return txt
				}
			},
			nameTextStyle: {
				overflow: 'truncate'
			},
			nameTruncate: {
				maxWidth: 20
			}
		},
		yAxis: {
			type: 'value'
		},
		color: ['#FF534F'],
		legend: {
			orient: 'horizontal',
			left: 'left',
			borderType: 'dashed',
			borderRadius: '50%',
			offset: 20,
			fontSize: 16
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: 10,
			top: '13%',
			containLabel: true
		},
		series: [
			{
				type: 'bar',
				tooltip: {
					show: true
				},
				barWidth: 15,
				barGap: '300%',
				barCategoryGap: '300%',
				label: {
					show: true,
					position: 'top',
					fontSize: 12,
					color: '#FF534F',
					fontWeight: 600
					// formatter(params) {
					// 	const { value } = params || {}
					// 	return `<div style="width: 20px;height: 20px;border-radius: 50%;background-color: #6385f4;display: inline-flex;justify-content: center;align-items: center;"><div style="width: 16px;height: 16px;border-radius: 50%;background-color: white;display: inline-block;box-sizing: border-box;"></div></div>`
					// }
				},
				markPoint: {
					label: {
						formatter(params) {
							const { value } = params || {}
							return `<div style="width: 20px;height: 20px;border-radius: 50%;background-color: #6385f4;display: inline-flex;justify-content: center;align-items: center;"><div style="width: 16px;height: 16px;border-radius: 50%;background-color: white;display: inline-block;box-sizing: border-box;"></div></div>`
						}
					}
				},
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 6,
					shadowColor: 'rgba(218,41,37,0.4)',
					borderRadius: [2, 2, 0, 0]
				},
				data: industryYaxisData.value,
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 6,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				}
			}
		],
		dataZoom:
			industryXaxisData.value.length > 6
				? [
						{
							type: 'slider', //滑动条型数据区域缩放组件
							realtime: true, //拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
							start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
							end: viewportLength.value, // 数据窗口范围的结束百分比。范围是：0 ~ 100
							height: 2,
							endValue: 11, //数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
							fillerColor: '#FF534F', // 滚动条颜色
							borderColor: '#99999940',
							moveHandleSize: 0,
							handleStyle: {
								color: '#fff',
								borderColor: '#fff'
							},
							brushStyle: {
								color: '#333333',
								borderColor: '#fff'
							},
							handleSize: 0, // 两边手柄尺寸
							showDetail: false, // 拖拽时是否展示滚动条两侧的文字
							top: '96%', //组件离容器上侧的距离
							zoomLock: true // 是否只平移不缩放
							// moveOnMouseMove: true, //开启鼠标移动窗口平移
							// zoomOnMouseWheel: true //开启鼠标滚轮缩放
						},
						{
							type: 'inside', //内置型数据区域缩放组件
							// start: 0,
							// end: 10,
							endValue: 11, // 最多12个
							zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
							moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
							moveOnMouseMove: true // 开启鼠标移动窗口平移
						}
				  ]
				: []
	}))
	// const industryOption = computed(() => ({
	// 	tooltip: {
	// 		trigger: 'axis',
	// 		show: true,
	// 		formatter(params, ...rest) {
	// 			const index = params?.[0]?.dataIndex
	// 			const arr = map(params, (item, index) => {
	// 				return `<div style="font-size: 12px;display:flex;justify-content;start; align-items:center;">
	// 					<div style="width: 8px;height: 8px; border-radius: 2px;background-color:#FF534F;margin-right: 4px;"></div>
	// 					${item.name}:<span style="font-weight: 700;margin-left: 5px;">${item.value}</span>
	// 					</div>`
	// 			})
	// 			return arr.join('')
	// 		}
	// 	},
	// 	legend: {
	// 		show: false
	// 	},
	// 	grid: {
	// 		left: '3%',
	// 		right: '7%',
	// 		bottom: '2%',
	// 		top: '15%',
	// 		containLabel: true
	// 	},
	// 	axisPointer: {
	// 		trigger: 'mouseover'
	// 	},
	// 	xAxis: {
	// 		triggerEvent: true,
	// 		type: 'category',
	// 		boundaryGap: false,
	// 		axisTick: {
	// 			show: false
	// 		},
	// 		data: industryXaxisData.value
	// 	},
	// 	yAxis: {
	// 		triggerEvent: true,
	// 		type: 'value'
	// 	},
	// 	series: [
	// 		{
	// 			type: 'line',
	// 			smooth: true,
	// 			color: '#FF534F', // 折线1的颜色
	// 			itemStyle: {
	// 				color: '#FF534F',
	// 				normal: {
	// 					borderColor: '#FF534F',
	// 					borderWidth: 2,
	// 					color: '#fff',
	// 					label: { show: true, color: '#FF534F', fontWeight: 500 }
	// 				}
	// 			},
	// 			symbolSize: 13,
	// 			symbol: 'circle',
	// 			lineStyle: {
	// 				width: 5,
	// 				color: '#FF534F',
	// 				shadowColor: 'rgba(0,0,0,0.3)',
	// 				shadowBlur: 10,
	// 				shadowOffsetY: 8
	// 			},
	// 			data: industryYaxisData.value
	// 		}
	// 	]
	// }))
	watch(
		() => props.dataVos,
		(newVal) => {
			if (newVal && newVal.length > 0) {
				initIndustryData(newVal)
				showChart.value = true
			} else {
				industryData.value = []
				showChart.value = false
			}
			// basicInfo.value = initBasicInfo(newVal || [])
		},
		{ deep: true }
	)
</script>
<style lang="less" scoped>
	.business-data {
		display: inline-block;
		vertical-align: top;
		position: relative;
		width: 100%;
		height: 518px;
		margin-top: 5px;
		padding: 20px;
		box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
		border-radius: 6px;
		overflow: hidden;
	}
	.chart-data {
		width: 100%;
		height: 100%;
	}
	.card-style {
		height: 518px;
		display: inline-block;
		vertical-align: top;
		padding: 10px 15px;
		margin: 10px 0 0 0;
		box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
		border-radius: 6px;
		.info-item {
			display: inline-block;
			vertical-align: top;
			margin-bottom: 10px;
			.label {
				color: #aaa;
				font-size: 14px;
			}
			.value {
				color: #555;
				font-size: 16px;
				font-weight: bold;
			}
		}
	}
	.card-style:not(:first-child) {
		margin-left: 10px;
	}
	.chart-text {
		font-size: 12px;
		font-family: PingFang SC;
		font-weight: 400;
		color: #999999;
		text-align: center;
	}
	.chart-value {
		font-size: 12px;
		font-family: PingFang SC;
		font-weight: 500;
		color: #2a2c39;
		display: inline-flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		text-align: left;
		flex: 1;
	}
	:deep(.ant-col) {
		text-align: center;
	}
</style>

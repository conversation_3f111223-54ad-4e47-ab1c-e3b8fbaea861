<template>
	<div class="flex flex-row justify-start items-center box-border h-[20px]">
		<img :src="iconChartTitle" class="w-7 h-5" />
		<span class="title-text ml-1">{{ props.title }}</span>
	</div>
</template>
<script setup>
	import iconChartTitle from '@/views/cms/home/<USER>/CustomContent/images/icon_chart_title.png'
	const props = defineProps({
		title: {
			type: String,
			default: ''
		}
	})
</script>
<style lang="less" scoped>
	.title-text {
		display: inline-block;
		font-family: PingFang SC;
		font-weight: 600;
		font-size: 14px;
		color: #d3000f;
	}
</style>

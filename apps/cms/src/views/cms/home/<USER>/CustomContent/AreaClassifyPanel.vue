<template>
	<a-skeleton
		:loading="!areaVos"
		active
		:title="false"
		:paragraph="{ rows: 6 }"
		class="flex flex-1 flex-row justify-center items-center px-2 box-border"
	>
		<div class="flex flex-col box-border w-full h-full p-3">
			<CardTitle title="地区（并表）" />
			<div class="flex flex-row w-full flex-1 justify-center items-center">
				<!-- <div class="chart-data relative" :class="!showChart ? 'flex flex-col justify-center items-center' : ''"> -->
				<template v-if="showChart">
					<v-chart class="h-full w-full px-2 box-border" :option="areaOption" autoresize />
				</template>
				<a-empty v-else></a-empty>
				<!-- </div> -->
				<!-- <div class="chart-data relative flex-1" :class="!showChart ? 'flex flex-col justify-center items-center' : ''">
					<template v-if="showChart">
						<v-chart class="h-full w-full px-2 box-border" :option="innerOption" autoresize />
					</template>
					<a-empty v-else></a-empty>
				</div>
				<div class="chart-data relative flex-1" :class="!showChart ? 'flex flex-col justify-center items-center' : ''">
					<template v-if="showChart">
						<v-chart class="h-full w-full px-2 box-border" :option="outterOption" autoresize />
					</template>
					<a-empty v-else></a-empty>
				</div> -->
			</div>
		</div>
	</a-skeleton>
</template>
<script setup>
	import { map, cloneDeep, keyBy, keys } from 'lodash-es'
	import icon_qjcbb from '@/views/cms/home/<USER>/CustomContent/images/icon_qjcbb.png'
	import icon_szglcgbb from '@/views/cms/home/<USER>/CustomContent/images/icon_szglcgbb.png'
	import icon_bbqy from '@/views/cms/home/<USER>/CustomContent/images/icon_bbqy.png'
	import tool from '@/utils/tool'
	import CardTitle from './CardTitle.vue'

	import { LegendComponent, GridComponent, DatasetComponent, TooltipComponent } from 'echarts/components'
	import VChart from 'vue-echarts'
	import { use } from 'echarts/core'
	import { CanvasRenderer } from 'echarts/renderers'
	import { GaugeChart, BarChart } from 'echarts/charts'

	use([BarChart, GaugeChart, LegendComponent, CanvasRenderer, DatasetComponent, GridComponent, TooltipComponent])
	const fillingStandardsData = ref([])
	const showChart = ref(false)
	const props = defineProps({
		areaCountVos: {
			type: Array,
			default: () => []
		}
	})

	const iconMap = {
		filling_standards_1: icon_qjcbb,
		filling_standards_2: icon_szglcgbb,
		filling_standards_3: icon_bbqy
	}
	const areaVos = ref(null)
	const incrVos = ref(null)
	const decrVos = ref(null)
	const sectorData = ref([])

	watch(
		() => props.areaCountVos,
		(newVal) => {
			sectorData.value = tool.dictList('homepage_block_type') || []
			if (newVal && newVal.length > 0) {
				showChart.value = true
				const vos = keyBy(cloneDeep(newVal), 'item') || {}
				areaVos.value = vos
				incrVos.value = vos['inside']?.count || 0
				decrVos.value = vos['outside']?.count || 0
			} else {
				showChart.value = false
				areaVos.value = {}
			}
		},
		{ deep: true }
	)
	const viewportLength = computed(() => {
		return industryXaxisData.value.length > 7 ? 35 : 90
	})
	const areaOption = computed(() => ({
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			},
			// position: ['40%', '-5%'],
			textStyle: {
				lineHeight: 17
			},
			extraCssText: 'max-width:160px; white-space:pre-wrap;word-break:break-all;'
		},
		xAxis: {
			type: 'category',
			data: ['境内', '境外'],
			axisTick: {
				show: false
			},
			axisLabel: {
				// width: 30,
				// overflow: 'truncate',
				// ellipsis: '..'
				interval: 0,
				fontSize: 11,
				formatter: (val) => {
					let txt = val
					if (val.length > 3) {
						txt = val.substr(0, 3) + '...'
					}
					return txt
				}
			},
			nameTextStyle: {
				overflow: 'truncate'
			},
			nameTruncate: {
				maxWidth: 20
			}
		},
		yAxis: {
			type: 'value'
		},
		color: ['#FF534F', '#6385F4', '#FFB400', '#00BFA5', '#85bedb', '#2f6ea3', '#14366a', '#999999'],
		legend: {
			orient: 'horizontal',
			left: 'left',
			borderType: 'dashed',
			borderRadius: '50%',
			offset: 20,
			fontSize: 16
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: 5,
			top: '15%',
			containLabel: true
		},
		series: [
			{
				type: 'bar',
				tooltip: {
					show: true
				},
				barWidth: 80,
				barGap: '300%',
				barCategoryGap: '300%',
				label: {
					show: true,
					position: 'top',
					fontSize: 15,
					fontWeight: 600
				},
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 6,
					borderRadius: [2, 2, 0, 0]
				},
				data: [{
					value: incrVos.value,
					itemStyle: {
						shadowColor: 'rgba(218,41,37,0.4)',
						color: '#FF534F'
					},
					label: {
						color: '#FF534F',
					},
					textStyle: {
						align: 'center',
						fontWeight: 800,
						color: '#2A2C39',
						fontSize: 13
					}
				}, 
				{
					value: decrVos.value,
					itemStyle: {
						shadowColor: '#6385F440',
						color: '#6385F4'
					},
					label: {
						color: '#6385F4',
					},
					textStyle: {
						align: 'center',
						fontWeight: 800,
						color: '#2A2C39',
						fontSize: 13
					}
				}],
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 6,
						shadowColor: 'rgba(0, 0, 0, 0.5)'
					}
				}
			}
		]
	}))
	// const innerOption = computed(() => {
	// 	// const max = incrVos.value < 100 ? 100 : incrVos.value / 0.7
	// 	const max = incrVos.value + decrVos.value
	// 	return {
	// 		series: [
	// 			{
	// 				type: 'gauge',
	// 				startAngle: 220,
	// 				endAngle: -140,
	// 				radius: '90%',
	// 				color: ['#FF534F'],
	// 				pointer: {
	// 					show: false
	// 				},
	// 				min: 0,
	// 				max: max,
	// 				progress: {
	// 					show: true,
	// 					overlap: false,
	// 					roundCap: true,
	// 					clip: false,
	// 					itemStyle: {
	// 						borderWidth: 0,
	// 						shadowBlur: 3,
	// 						shadowOffsetX: 2,
	// 						shadowColor: '#FF534F40'
	// 					}
	// 				},
	// 				axisLine: {
	// 					lineStyle: {
	// 						width: 12
	// 					}
	// 				},
	// 				splitLine: {
	// 					show: false,
	// 					distance: 0,
	// 					length: 10
	// 				},
	// 				axisTick: {
	// 					show: false
	// 				},
	// 				axisLabel: {
	// 					show: false,
	// 					distance: 50
	// 				},
	// 				data: [
	// 					{
	// 						value: incrVos.value,
	// 						name: '境内',
	// 						title: {
	// 							color: '#2a2c3990',
	// 							fontWeight: 400,
	// 							fontSize: 16,
	// 							offsetCenter: ['0%', '-20%']
	// 						},
	// 						detail: {
	// 							color: '#2A2C39',
	// 							fontSize: 22,
	// 							fontWeight: 800,
	// 							valueAnimation: true,
	// 							offsetCenter: ['0%', '20%'],
	// 							shadowBlur: 3,
	// 							shadowOffsetX: 2,
	// 							shadowColor: 'rgba(218,41,37,0.4)'
	// 						}
	// 					}
	// 				],
	// 				title: {
	// 					fontSize: 14
	// 				},
	// 				detail: {
	// 					width: 50,
	// 					height: 14,
	// 					fontSize: 14,
	// 					color: 'inherit',
	// 					borderColor: 'inherit',
	// 					formatter: '{value}'
	// 				}
	// 			}
	// 		]
	// 	}
	// })
	// const outterOption = computed(() => {
	// 	// const max = decrVos.value < 100 ? 100 : decrVos.value / 0.7
	// 	const max = incrVos.value + decrVos.value
	// 	return {
	// 		series: [
	// 			{
	// 				type: 'gauge',
	// 				startAngle: 220,
	// 				endAngle: -140,
	// 				radius: '90%',
	// 				color: ['#6385F4'],
	// 				pointer: {
	// 					show: false
	// 				},
	// 				min: 0,
	// 				max: max,
	// 				progress: {
	// 					show: true,
	// 					overlap: false,
	// 					roundCap: true,
	// 					clip: false,
	// 					itemStyle: {
	// 						borderWidth: 0,
	// 						borderColor: '#464646',
	// 						shadowBlur: 3,
	// 						shadowOffsetX: 2,
	// 						shadowColor: '#6385F440'
	// 					}
	// 				},
	// 				axisLine: {
	// 					lineStyle: {
	// 						width: 12
	// 					}
	// 				},
	// 				splitLine: {
	// 					show: false,
	// 					distance: 0,
	// 					length: 10
	// 				},
	// 				axisTick: {
	// 					show: false
	// 				},
	// 				axisLabel: {
	// 					show: false,
	// 					distance: 50
	// 				},
	// 				data: [
	// 					{
	// 						value: decrVos.value,
	// 						name: '境外',
	// 						title: {
	// 							color: '#2a2c3990',
	// 							fontWeight: 400,
	// 							fontSize: 16,
	// 							offsetCenter: ['0%', '-20%']
	// 						},
	// 						detail: {
	// 							color: '#2A2C39',
	// 							fontSize: 22,
	// 							fontWeight: 800,
	// 							valueAnimation: true,
	// 							offsetCenter: ['0%', '20%'],
	// 							shadowBlur: 3,
	// 							shadowOffsetX: 2,
	// 							shadowColor: 'rgba(218,41,37,0.4)'
	// 						}
	// 					}
	// 				],
	// 				title: {
	// 					fontSize: 14
	// 				},
	// 				detail: {
	// 					width: 50,
	// 					height: 14,
	// 					fontSize: 14,
	// 					color: 'inherit',
	// 					borderColor: 'inherit',
	// 					formatter: '{value}'
	// 				}
	// 			}
	// 		]
	// 	}
	// })
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		// min-height: 116px;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-weight: 500;
		font-size: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	/deep/ .ant-statistic .ant-statistic-content {
		color: #fff;
		font-size: 40px;
	}
	.short-divider {
		border-color: #000;
		height: 16px;
		border-width: 1px;
		margin: 0 4px;
	}
	.chart-data {
		:deep(.ant-empty .ant-empty-image svg) {
			width: 100px !important;
		}
	}
</style>

<template>
	<div class="flex w-full gap-5 justify-center items-center" id="access">
		<a-input-group class="w-full !flex justify-center items-center input-search-area" compact>
			<a-auto-complete
				ref="autoCompleteRef"
				:dropdownMatchSelectWidth="true"
				style="width: 70%"
				v-model:value="query"
				:options="dataSource"
				@search="handleSearch"
				@focus="onFocus"
				@clear="onClear"
				@blur="onBlur"
				:open="isOpen"
				@select="handleGoToPortrait"
				size="large"
				:allowClear="false"
				@compositionstart="onCompositionStart"
				@compositionend="onCompositionEnd"
				placeholder="请输入你要查询的公司名称"
			>
				<template #option="item">
					<div style="display: flex; justify-content: space-between" v-if="item.id != 'clear'">
						<WordHighlighter :splitBySpace="true" :query="query" highlightClass="highlight-style">
							{{ item.label }}
						</WordHighlighter>
					</div>
					<div v-else class="full flex justify-end items-center h-[25px]">
						<span type="text" class="text-[#999999] mr-5" @click="handleClearHistory">清空历史</span>
						<span type="text" class="text-[#999999]" @click="handleClosePanel">关闭历史</span>
					</div>
				</template>
				<!-- <a-input size="large"></a-input> -->
				<!-- <a-input-search class="input-search-area" size="large" placeholder="请输入你要查询的公司名称">
				<template #enterButton>
					<a-button type="primary" class="btn-container" enter-button
						><template #icon> <img :src="iconSearch" class="w-5 h-5 mr-2" /> </template>立即查询</a-button
					>
				</template>
			</a-input-search> -->
			</a-auto-complete>
			<a-button size="large" type="primary" @click="handleClickSearch" enter-button
				><template #icon> <img :src="iconSearch" class="w-5 h-5 mr-2" /> </template>立即查询</a-button
			>
		</a-input-group>
	</div>
</template>
<script setup>
	import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
	import { useRouter } from 'vue-router'
	import { map, cloneDeep, keyBy, keys, chunk, filter, debounce, flatMap, trim } from 'lodash-es'
	import WordHighlighter from 'vue-word-highlighter'

	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	import jglb from '@/views/cms/home/<USER>/CustomContent/images/jglb.png'
	import jgcz from '@/views/cms/home/<USER>/CustomContent/images/icon_xzcz.png'
	import icon_qjcbb from '@/views/cms/home/<USER>/CustomContent/images/icon_qjcbb.png'
	import icon_szglcgbb from '@/views/cms/home/<USER>/CustomContent/images/icon_szglcgbb.png'
	import icon_bbqy from '@/views/cms/home/<USER>/CustomContent/images/icon_bbqy.png'
	import icon_jghx2 from '@/views/cms/home/<USER>/CustomContent/images/icon_jghx2.png'
	import iconSearch from '@/views/cms/home/<USER>/CustomContent/images/icon_search.png'
	import tool from '@/utils/tool'
	import { safeJSONParser } from '@citic-institution-web/utils'
	import { homeStore, globalStore } from '@/store'

	const router = useRouter()
	const fillingStandardsData = ref([])
	const loading = ref(false)
	const dataSource = ref([])
	const query = ref('')
	const autoCompleteRef = ref(null)
	const store = homeStore()
	const indexOrgList = computed(() => store.orgTree)
	const institutionalPortraitMenuId = ref('')
	const gStore = globalStore()

	const props = defineProps({
		typeCountVos: {
			type: Array,
			default: () => []
		},
		incrAndDecrCountVos: {
			type: Array,
			default: () => []
		}
	})

	const iconMap = {
		filling_standards_1: icon_qjcbb,
		filling_standards_2: icon_szglcgbb,
		filling_standards_3: icon_bbqy
	}
	const typeVos = ref(null)
	const incrAndDecrVos = ref(null)
	const isOpen = ref(false)
	watch(
		() => props.typeCountVos,
		(newVal) => {
			if (newVal && newVal.length > 0) {
				fillingStandardsData.value = tool.dictList('homepage_org_type')
				const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
				typeVos.value = keyBy(
					map(cloneDeep(newVal), (val) => {
						val.name = keyByTypeVos[val.item]?.label || ''
						val.icon = iconMap[val.item] || ''
						return val
					}),
					'item'
				)
			} else {
				typeVos.value = {}
			}
		},
		{ deep: true }
	)
	watch(
		() => props.incrAndDecrCountVos,
		(newVal) => {
			if (newVal) {
				fillingStandardsData.value = tool.dictList('homepage_org_type')
				const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
				const diffData = {}
				map(cloneDeep(keys(newVal)), (val) => {
					const incrAndDecrObj = {}
					incrAndDecrObj.name = keyByTypeVos[val]?.label || ''
					incrAndDecrObj.icon = iconMap[val] || ''
					const incrAndDecrData = keyBy(newVal?.[val], 'item')
					incrAndDecrObj.incr = incrAndDecrData['incr']?.count
					incrAndDecrObj.decr = incrAndDecrData['decr']?.count
					diffData[val] = incrAndDecrObj
				})
				incrAndDecrVos.value = diffData || {}
			} else {
				incrAndDecrVos.value = {}
			}
		},
		{ deep: true }
	)

	loading.value = true
	const searchResult = (queryStr) => {
		// const indexOrgList = localStorage.getItem('indexOrgList')
		// const indexOrgList = store.orgTree
		const promises = map(chunk(safeJSONParser(indexOrgList.value, []), 400), (chunkItemArr) => {
			return new Promise((resolve, reject) => {
				const result = filter(chunkItemArr, (item) => {
					return item.simpOrgName?.includes?.(queryStr)
				})
				resolve(result)
			})
		})
		Promise.all(promises).then((res) => {
			const allFilterData = map(flatMap(res), (item) => ({
				label: item.simpOrgName,
				value: item.simpOrgName,
				id: item.id
			}))
			dataSource.value = allFilterData
			if (dataSource.value.length === 0) {
				// dataSource.value = [{ label: '暂无数据', value: '暂无数据' }]
			} else {
				isOpen.value = true
			}
		})
	}
	const isComposing = ref(false)

	const onCompositionStart = () => {
		isComposing.value = true
	}

	const onCompositionEnd = () => {
		isComposing.value = false
		handleSearch()
	}
	const onFocus = (val) => {
		if (dataSource.value?.length > 0) isOpen.value = true
	}
	const onClear = (val) => {
		isOpen.value = false
		// fillHistoryToDatasource()
		// autoCompleteRef.value?.focus?.()
		// handleSearch()
	}
	const onBlur = (val) => {
		isOpen.value = false
	}
	const handleSearch = debounce(
		(val) => {
			if (isComposing.value) {
				return
			}
			if (!trim(query.value)) {
				// dataSource.value = []
				fillHistoryToDatasource()
				return
			}
			searchResult(trim(query.value))
		},
		50,
		{ leading: false, trailing: true }
	)
	const handleClickSearch = () => {
		autoCompleteRef.value?.focus?.()
		isOpen.value = true
	}
	const fillHistoryToDatasource = () => {
		const historySearch = localStorage.getItem('historySearch')
		if (historySearch) {
			const historySearchArr = safeJSONParser(historySearch, [])
			if (historySearchArr.length > 0) {
				historySearchArr.push({ label: '清除历史', value: '', id: 'clear' })
				dataSource.value = historySearchArr
			}
		}
	}
	const traverseChild = (menu) => {
		for (let i = 0; i < menu?.length || 0; i++) {
			const childMenu = menu[i]
			if (childMenu?.children?.length > 0) {
				const result = traverseChild(childMenu?.children)
				if (result) return result
			}
			if (childMenu.menuType == 'MENU' && childMenu?.name == 'InstitutionalPortrait') {
				return childMenu
			}
			continue
		}
	}
	onMounted(() => {
		const moduleMenu = router.getMenu() || []
		institutionalPortraitMenuId.value = traverseChild(moduleMenu)?.id
		fillHistoryToDatasource()
		// const indexOrgList = localStorage.getItem('indexOrgList')
		// const indexOrgList = store.orgTree
		if (indexOrgList.value?.length) return
		bizInstitutionApi.getOrgList({ isWhereFilling: 1 }).then((res) => {
			if (res) {
				// localStorage.setItem('indexOrgList', JSON.stringify(res))
				store.updateOrgTree(res)
			}
		})
	})
	const handleGoToPortrait = (value, item) => {
		if (item.id == 'clear') return
		const historySearch = localStorage.getItem('historySearch')
		const historySearchArr = safeJSONParser(historySearch, [])
		if (historySearchArr.length > 0) {
			const index = historySearchArr.findIndex((historyItem) => item.id === historyItem.id)
			if (index > -1) {
				historySearchArr.splice(index, 1)
			}
			historySearchArr.unshift({ label: item.label, value: item.value, id: item.id })
			localStorage.setItem('historySearch', JSON.stringify(historySearchArr))
		} else {
			localStorage.setItem('historySearch', JSON.stringify([{ label: item.label, value: item.value, id: item.id }]))
		}
		console.log(323, item)
		// gStore.setCurrentMenu(institutionalPortraitMenuId.value)
		router.push({ path: '/institutionalPortrait/service', query: { entityId: item.id } })
	}
	const handleClosePanel = (e) => {
		e?.preventDefault?.()
		e.stopPropagation?.()
		isOpen.value = false
		autoCompleteRef.value?.blur?.()
	}
	const handleClearHistory = (e) => {
		e?.preventDefault?.()
		e.stopPropagation?.()
		localStorage.removeItem('historySearch')
		dataSource.value = []
		isOpen.value = false
		autoCompleteRef.value?.blur?.()
	}
	onUnmounted(() => {})
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		// min-height: 116px;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-weight: 500;
		font-size: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	/deep/ .ant-statistic .ant-statistic-content {
		color: #fff;
		font-size: 40px;
	}
	.short-divider {
		border-color: #000;
		height: 16px;
		border-width: 1px;
		margin: 0 4px;
	}
	.input-search-area {
		font-family: 'PangMenZhengDao';
		.ant-input-wrapper {
			height: 100%;
		}
		:deep(.ant-btn-lg) {
			height: 50px;
			box-sizing: border-box;
			border-radius: 0 8px 8px 0;
		}
		:deep(.ant-input-affix-wrapper-lg) {
			height: 50px;
			box-sizing: border-box;
		}
		:deep(.ant-input-lg) {
			height: 50px;
			box-sizing: border-box;
		}
		:deep(.ant-select-lg) {
			height: 50px;
			box-sizing: border-box;
		}
		:deep(.ant-select-selector) {
			height: 50px !important;
			box-sizing: border-box !important;
		}
		:deep(.ant-select-selection-placeholder) {
			line-height: 50px !important;
			box-sizing: border-box !important;
		}
		:deep(.ant-select-selection-search) {
			height: 50px !important;
			box-sizing: border-box !important;
		}
		:deep(.ant-select-selection-search-input) {
			height: 50px !important;
			box-sizing: border-box !important;
		}
	}
	.btn-container {
		background: linear-gradient(-90deg, #e2242a, #fe4b4b);
		padding: 0px 40px;
	}
</style>

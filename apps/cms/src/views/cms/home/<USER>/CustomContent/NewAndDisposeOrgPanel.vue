<template>
	<a-skeleton
		:loading="!incrAndDecrVos"
		active
		:title="false"
		:paragraph="{ rows: 6 }"
		class="flex flex-1 flex-row justify-center items-center px-2 box-border"
	>
		<div class="flex flex-col box-border w-full h-full p-3">
			<div class="flex w-full justify-between items-center">
				<CardTitle title="新增｜处置（当年累计）" />
				<!-- <a-space direction="vertical" :size="12"
					><a-date-picker
						:disabled-date="disabledDate"
						size="small"
						picker="month"
						format="M月"
						:value-format="valFormat"
						placeholder="时间筛选"
						@change="changeDate"
					>
						<template #prevIcon> 时间筛选</template>
					</a-date-picker>
				</a-space> -->
			</div>

			<div
				class="chart-data relative flex-1 w-full"
				:class="!showChart ? 'flex flex-col justify-center items-center' : ''"
			>
				<a-spin
					class="w-full h-full !absolute !flex flex-col justify-center items-center"
					tip="加载中..."
					:spinning="loading"
				>
				</a-spin>
				<template v-if="showChart">
					<v-chart class="h-full w-full" :option="incrAndDecOption" autoresize />
				</template>

				<a-empty v-else></a-empty>
			</div>
		</div>
	</a-skeleton>
</template>
<script setup>
	import { computed, onMounted, ref, watch } from 'vue'
	import dayjs from 'dayjs'
	import VChart from 'vue-echarts'
	import { use } from 'echarts/core'
	import { BarChart } from 'echarts/charts'
	import { CanvasRenderer } from 'echarts/renderers'
	import {
		LegendComponent,
		GridComponent,
		DatasetComponent,
		TooltipComponent,
		DataZoomComponent
	} from 'echarts/components'
	import { map, cloneDeep, keyBy, keys } from 'lodash-es'
	import icon_qjcbb from '@/views/cms/home/<USER>/CustomContent/images/icon_qjcbb.png'
	import icon_szglcgbb from '@/views/cms/home/<USER>/CustomContent/images/icon_szglcgbb.png'
	import icon_bbqy from '@/views/cms/home/<USER>/CustomContent/images/icon_bbqy.png'
	import tool from '@/utils/tool'
	import CardTitle from './CardTitle.vue'
	import homeApi from '@/api/biz/bizHomeApi.js'

	use([LegendComponent, CanvasRenderer, BarChart, DatasetComponent, GridComponent, TooltipComponent, DataZoomComponent])
	const fillingStandardsData = ref([])
	const valFormat = ref(`${dayjs().format('YYYY')}-MM`)
	// const props = defineProps({
	// 	incrAndDecrCountVos: {
	// 		type: Array,
	// 		default: () => []
	// 	}
	// })

	const disabledDate = (current) => {
		// 只能选今年
		return current && (current < dayjs().startOf('year') || current > dayjs().endOf('year'))
	}
	const iconMap = {
		filling_standards_1: icon_qjcbb,
		filling_standards_2: icon_szglcgbb,
		filling_standards_3: icon_bbqy
	}
	const incrAndDecrCountVos = ref([])
	const incrAndDecrVos = ref(null)
	const showChart = ref(false)
	const orgTypes = ref([])
	const incrValues = ref([])
	const decrValues = ref([])
	const loading = ref(false)
	watch(
		incrAndDecrCountVos,
		(newVal) => {
			if (newVal && keys(newVal).length > 0) {
				fillingStandardsData.value = tool.dictList('homepage_org_type')
				const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
				const diffData = {}
				const names = []
				const incrData = []
				const decrData = []
				map(cloneDeep(keys(newVal)), (val) => {
					const incrAndDecrObj = {}
					const name = keyByTypeVos[val]?.label || ''
					names.push(name)
					incrAndDecrObj.name = keyByTypeVos[val]?.label || ''
					incrAndDecrObj.icon = iconMap[val] || ''
					const incrAndDecrData = keyBy(newVal?.[val], 'item')
					const incrCount = incrAndDecrData['incr']?.count || 0
					const decrCount = incrAndDecrData['decr']?.count || 0
					incrAndDecrObj.incr = incrCount
					incrAndDecrObj.decr = decrCount
					incrData.push(incrCount)
					decrData.push(decrCount)
					diffData[val] = incrAndDecrObj
				})
				orgTypes.value = names
				incrValues.value = incrData
				decrValues.value = decrData
				incrAndDecrVos.value = diffData || {}
				showChart.value = true
			} else {
				incrAndDecrVos.value = {}
				showChart.value = false
			}
		},
		{ deep: true }
	)

	const incrAndDecOption = computed(() => {
		return {
			color: ['#6385F4', '#FF534F', '#00BFA5'],
			tooltip: {
				trigger: 'axis',
				axisPointer: {
					type: 'shadow'
				}
			},
			legend: {
				left: 'center',
				top: '0',
				itemGap: 150,
				textStyle: {
					fontWeight: 800,
					color: '#606266'
				},
				shadowBlur: 3,
				shadowOffsetX: 2,
				shadowColor: 'rgba(218,41,37,0.4)'
			},
			grid: [
				{
					show: false,
					left: 20,
					top: '30',
					bottom: '0%',
					containLabel: true,
					width: '38%'
				},
				{
					show: false,
					left: '51.5%',
					top: '30',
					bottom: '6%',
					width: '0%'
				},
				{
					show: false,
					right: 20,
					top: '30',
					bottom: '0%',
					containLabel: true,
					width: '38%'
				}
			],
			xAxis: [
				{
					type: 'value',
					nameLocation: 'middle',
					inverse: true, // 强制设置坐标轴分割间隔
					axisLine: {
						// 坐标轴轴线相关设置
						show: false, // 是否显示坐标轴轴线
						onZero: true, // X 轴或者 Y 轴的轴线是否在另一个轴的 0 刻度上
						lineStyle: {
							color: '#ECEDF0'
						}
					},
					axisTick: {
						show: false
					},
					position: 'bottom',
					axisLabel: {
						show: true,
						color: '#c6d3d7',
						fontSize: 12,
						fontFamily: 'DINPro-Regular'
					},
					splitLine: {
						show: true
					}
				},
				{
					gridIndex: 1,
					show: false
				},
				{
					gridIndex: 2,
					axisLine: {
						show: false, // 是否显示坐标轴轴线
						onZero: true, // X 轴或者 Y 轴的轴线是否在另一个轴的 0 刻度上
						lineStyle: {
							color: '#ECEDF0'
						}
					},
					axisTick: {
						show: false
					},
					position: 'bottom',
					axisLabel: {
						show: true,
						color: '#c6d3d7',
						fontSize: 12,
						fontFamily: 'DINPro-Regular'
					},
					splitLine: {
						show: true
					}
				}
			],
			yAxis: [
				{
					type: 'category',
					inverse: true,
					position: 'right',
					axisLine: {
						show: true,
						lineStyle: {
							color: '#60626610'
						}
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: false
					},
					data: map(orgTypes.value, function (value) {
						return {
							value: value,
							textStyle: {
								align: 'center',
								fontWeight: 800,
								color: '#2A2C39',
								fontSize: 13
							}
						}
					})
				},
				{
					gridIndex: 1,
					type: 'category',
					inverse: true,
					position: 'left',
					axisLine: {
						show: false
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: true,
						textStyle: {
							color: '#606266',
							fontSize: 12,
							fontWeigt: 800,
							fontFamily: 'pmzd_title'
						},
						align: 'center'
					},
					data: map(orgTypes.value, function (value) {
						return {
							value: value,
							textStyle: {
								align: 'center',
								fontWeight: 800,
								color: '#2A2C39',
								fontSize: 13
							}
						}
					})
				},
				{
					gridIndex: 2,
					type: 'category',
					inverse: true,
					position: 'left',
					axisLine: {
						show: true,
						lineStyle: {
							color: '#60626610'
						}
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: false
					},
					data: orgTypes.value
				}
			],
			series: [
				{
					name: '新增',
					type: 'bar',
					barWidth: 22,
					label: {
						show: true,
						position: 'left',
						fontSize: 12,
						color: '#6385F4',
						fontWeight: 600
					},
					itemStyle: {
						normal: {
							label: {
								show: true, // 开启显示
								position: 'insideLeft', // 在上方显示
								textStyle: {
									// 数值样式
									// color: colors[0].borderColor,
									color: '#FFFFFF',
									fontSize: 12,
									fontWeight: 800
								}
							},
							borderWidth: 1,
							borderRadius: [2, 2, 2, 2],
							shadowBlur: 10,
							shadowOffsetX: 2,
							shadowColor: '#6385F490'
						}
					},
					data: map(incrValues.value, (value) => {
						return {
							value: value,
							label: {
								show: !!value
							}
						}
					}),
					animationDuration: 1500
				},
				{
					name: '处置',
					type: 'bar',
					barWidth: 22,
					xAxisIndex: 2,
					yAxisIndex: 2,
					itemStyle: {
						normal: {
							label: {
								show: true, // 开启显示
								position: 'insideRight', // 在上方显示
								textStyle: {
									// 数值样式
									color: '#FFFFFF',
									fontSize: 12,
									fontWeight: 800
								}
							},
							borderWidth: 1,
							borderRadius: [2, 2, 2, 2],
							shadowBlur: 10,
							shadowOffsetX: 2,
							shadowColor: '#FF534F90'
						}
					},
					data: map(decrValues.value, (value) => {
						return {
							value: value,
							label: {
								show: !!value
							}
						}
					}),
					animationDuration: 1500
				}
			]
		}
	})
	const changeDate = (date) => {
		getNewData(date ? date : 'year')
	}
	const getNewData = (yearMonth) => {
		loading.value = true
		homeApi
			.incrAndDecrCount({ yearMonth })
			.then((newVal) => {
				incrAndDecrCountVos.value = newVal
			})
			.finally(() => {
				loading.value = false
			})
	}
	onMounted(() => {
		getNewData('year')
	})
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		// min-height: 116px;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-weight: 500;
		font-size: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	/deep/ .ant-statistic .ant-statistic-content {
		color: #fff;
		font-size: 40px;
	}
	.short-divider {
		border-color: #000;
		height: 16px;
		border-width: 1px;
		margin: 0 4px;
	}
	:deep(.ant-spin) {
		background: rgba(0, 0, 0, 0.05);
	}
</style>

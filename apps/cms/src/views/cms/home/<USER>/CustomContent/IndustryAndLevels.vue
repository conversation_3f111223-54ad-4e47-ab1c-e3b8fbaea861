<template>
	<div class="flex flex-row w-full h-full gap-5" id="access">
		<div class="org-panel h-full flex flex-row justify-center items-center overflow-hidden rounded-lg">
			<!-- <ManageLevelChart :dataVos="managementLevelVos" /> -->
			<OrgClassifyPanel :typeCountVos="typeCountVos" />
		</div>
		<div class="org-panel h-full flex flex-row justify-center items-center overflow-hidden rounded-lg">
			<!-- <LegalEntityChart :dataVos="legalEntityLevelVos" /> -->
			<AreaClassifyPanel :areaCountVos="areaCountVos" />
		</div>
		<div class="org-panel h-full flex flex-row justify-center items-center overflow-hidden rounded-lg">
			<IndustryChart :dataVos="industryCategVos" />
		</div>
	</div>
</template>
<script setup>
	import { map, cloneDeep, keyBy, keys } from 'lodash-es'
	import icon_qjcbb from '@/views/cms/home/<USER>/CustomContent/images/icon_qjcbb.png'
	import icon_szglcgbb from '@/views/cms/home/<USER>/CustomContent/images/icon_szglcgbb.png'
	import icon_bbqy from '@/views/cms/home/<USER>/CustomContent/images/icon_bbqy.png'
	import tool from '@/utils/tool'
	import IndustryChart from '@/views/cms/home/<USER>/CustomContent/IndustryChart.vue'
	import OrgClassifyPanel from '@/views/cms/home/<USER>/CustomContent/OrgClassifyPanel.vue'
	import AreaClassifyPanel from '@/views/cms/home/<USER>/CustomContent/AreaClassifyPanel.vue'

	const fillingStandardsData = ref([])
	const loading = ref(false)

	const props = defineProps({
		typeCountVos: {
			type: Array,
			default: () => []
		},
		incrAndDecrCountVos: {
			type: Array,
			default: () => []
		},
		industryCategVos: {
			type: Array,
			default: () => []
		},
		areaCountVos: {
			type: Array,
			default: () => []
		}
	})

	const iconMap = {
		filling_standards_1: icon_qjcbb,
		filling_standards_2: icon_szglcgbb,
		filling_standards_3: icon_bbqy
	}
	const typeVos = ref(null)
	const incrAndDecrVos = ref(null)
	watch(
		() => props.typeCountVos,
		(newVal) => {
			if (newVal && newVal.length > 0) {
				fillingStandardsData.value = tool.dictList('homepage_org_type')
				const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
				typeVos.value = keyBy(
					map(cloneDeep(newVal), (val) => {
						val.name = keyByTypeVos[val.item]?.label || ''
						val.icon = iconMap[val.item] || ''
						return val
					}),
					'item'
				)
			} else {
				typeVos.value = {}
			}
		},
		{ deep: true }
	)
	// watch(
	// 	() => props.incrAndDecrCountVos,
	// 	(newVal) => {
	// 		if (newVal) {
	// 			fillingStandardsData.value = tool.dictList('homepage_org_type')
	// 			const keyByTypeVos = keyBy(fillingStandardsData.value, 'value') || {}
	// 			const diffData = {}
	// 			map(cloneDeep(keys(newVal)), (val) => {
	// 				const incrAndDecrObj = {}
	// 				incrAndDecrObj.name = keyByTypeVos[val]?.label || ''
	// 				incrAndDecrObj.icon = iconMap[val] || ''
	// 				const incrAndDecrData = keyBy(newVal?.[val], 'item')
	// 				incrAndDecrObj.incr = incrAndDecrData['incr']?.count
	// 				incrAndDecrObj.decr = incrAndDecrData['decr']?.count
	// 				diffData[val] = incrAndDecrObj
	// 			})
	// 			incrAndDecrVos.value = diffData || {}
	// 		} else {
	// 			incrAndDecrVos.value = {}
	// 		}
	// 	},
	// 	{ deep: true }
	// )

	loading.value = true
	// api
	// 	.substationTodayCount()
	// 	.then((res) => {
	// 		todayNumbers.value = res?.todayNumbers
	// 		todayTimes.value = res?.todayTimes
	// 		loading.value = false
	// 	})
	// 	.finally(() => {
	// 		loading.value = false
	// 	})
</script>
<style lang="less" scoped>
	@import '../../index.less';
	.pointer {
		cursor: pointer;
	}
	.org-panel {
		flex: 1;
		box-shadow: -19px -12px 20px 15px rgb(234 238 243 / 32%);
		background: rgba(255, 255, 255, 0.7);
		// box-shadow: 0px 0px 124px 15px rgba(197, 224, 251, 0.32);
	}
	#access {
		//margin-bottom: 25px;
		//min-height: 26vh;
		height: 100%;
		overflow: auto;
		color: black;
	}
	.access-title {
		font-weight: 500;
		font-size: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	/deep/ .ant-statistic .ant-statistic-content {
		color: #fff;
		font-size: 40px;
	}
	.short-divider {
		border-color: #000;
		height: 16px;
		border-width: 1px;
		margin: 0 4px;
	}
</style>

/**
 * 管理架构组件常量配置
 */

// 顶级节点颜色配置
export const TOP_LEVEL_NODE_COLORS = {
  '中信集团': '#D20000',
  '中信股份': '#D45100',
  '中信有限': '#485563',
}

// 业务类型颜色配置
export const BUSINESS_TYPE_COLORS = {
  'root': {
    "20140824164901061871": '#D20000',
    "20140822152314001513": '#D45100',
    "20140824164901061873": '#485563'
  },
  'Y': {
    2: '#ddedf8',
    3: '#e1f0da',
    4: '#ffeadf',
    5: '#fff7dc',
  },
  'N': {
    2: '#7f7f7f',
    3: '#a6a6a6',
    4: '#bfbfbf',
    5: '#d9d9d9',
  }
}

// 根节点ID映射
export const ROOT_NODE_LEVEL_MAP = {
  "20140824164901061871": 'group-0',
  "20140822152314001513": 'stock-0',
  "20140824164901061873": 'limit-0'
}

// 根节点ID列表
export const ROOT_NODE_IDS = Object.keys(ROOT_NODE_LEVEL_MAP)

// 缓存配置
export const CACHE_CONFIG = {
  EXPIRY_TIME: 5 * 60 * 1000, // 5分钟
  CLEANUP_INTERVAL: 10 * 60 * 1000, // 10分钟清理一次
}

// 防抖延迟配置
export const DEBOUNCE_DELAY = {
  DATE_CHANGE: 300, // 日期变化防抖延迟
  SEARCH: 500, // 搜索防抖延迟
}

// 思维导图配置
export const MINDMAP_CONFIG = {
  EXPAND_BTN_SIZE: 24,
  LINE_WIDTH: 2,
  LINE_COLOR: '#a6a6a6',
  LINE_RADIUS: 5,
  GENERALIZATION_LINE_WIDTH: 1,
}

// 表格列配置
export const LISTED_VO_LIST_COLUMNS = [
  {
    title: '序号',
    dataIndex: 'index',
    customRender: ({ index }) => `${index + 1}`
  },
  {
    title: '市场',
    dataIndex: 'market',
    customRender: ({ text }) => {
      // 这里需要导入tool工具，但为了避免循环依赖，在组件中处理
      return text
    }
  },
  {
    title: '股票简称',
    dataIndex: 'stock',
  },
  {
    title: '股票代码',
    dataIndex: 'stockCode',
  }
]

// 命令类型枚举
export const COMMAND_TYPES = {
  DETAILS: 'DETAILS',
  FOCUS: 'FOCUS',
  PORTRAIT: 'PORTRAIT',
  UNEXPAND_ALL: 'UNEXPAND_ALL',
  EXPAND_ALL: 'EXPAND_ALL',
  UNEXPAND_TO_LEVEL: 'UNEXPAND_TO_LEVEL',
}

// 结构类型枚举
export const STRUCTURE_TYPES = {
  MANAGEMENT: '1',
  GROUP: '2',
}

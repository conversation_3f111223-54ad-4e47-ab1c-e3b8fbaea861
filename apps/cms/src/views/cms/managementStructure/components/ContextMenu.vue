<template>
  <!-- 右键菜单 -->
  <div 
    v-if="visible" 
    class="context-menu" 
    :style="{ top: `${position.y}px`, left: `${position.x}px` }"
  >
    <ul>
      <li 
        v-if="currentNode?.data.isClickable" 
        class="text-red-600" 
        @click="handleMenuClick('DETAILS')"
      >
        详情
      </li>
      <li 
        v-if="structureType === '1' && currentNode?.data.isClickable" 
        class="text-red-600" 
        @click="handleMenuClick('FOCUS')"
      >
        聚焦
      </li>
      <li 
        v-if="currentNode?.data.isClickable" 
        class="text-red-600" 
        @click="handleMenuClick('PORTRAIT')"
      >
        画像
      </li>
      <li @click="handleMenuClick('UNEXPAND_ALL')">
        全部收起
      </li>
      <li @click="handleMenuClick('EXPAND_ALL')">
        全部展开
      </li>
    </ul>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  currentNode: {
    type: Object,
    default: null
  },
  structureType: {
    type: String,
    default: '1'
  }
})

// Emits
const emit = defineEmits(['menuClick'])

// Event handlers
const handleMenuClick = (action) => {
  emit('menuClick', action)
}
</script>

<style scoped>
.context-menu {
  position: fixed;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 10px;
  border-radius: 4px;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: 8px 12px;
  cursor: pointer;
}

.context-menu li:hover {
  background-color: #f0f0f0;
}
</style>

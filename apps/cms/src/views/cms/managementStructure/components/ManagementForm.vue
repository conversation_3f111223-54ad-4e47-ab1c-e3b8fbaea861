<template>
  <a-form :model="formData" layout="horizontal">
    <a-row :gutter="24">
      <!-- 日期选择器 -->
      <a-col :span="6">
        <a-date-picker
          @change="handleDateChange"
          v-model:value="formData.yearMonth"
          value-format="YYYY-M"
          picker="month"
          class="w-full"
          :disabled-date="disabledDate"
        />
      </a-col>

      <!-- 当前版本按钮 -->
      <a-col :span="3">
        <a-button
          type="primary"
          @click="handleQueryCurrentVersion"
          v-if="currentVersionLabel"
        >
          {{ currentVersionLabel }}
        </a-button>
      </a-col>

      <!-- 机构选择器 -->
      <a-col :span="9">
        <a-form-item
          v-if="formData.type === '1'"
          label="机构"
          name="entityId"
        >
          <a-tree-select
            v-model:value="formData.entityId"
            v-model:searchValue="formData.searchValue"
            show-search
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择机构"
            allow-clear
            :tree-data="orgTreeData"
            :treeDefaultExpandedKeys="orgTreeProps.expanded"
            tree-node-filter-prop="simpOrgName"
            @select="handleOrgChange"
            :field-names="{ children: 'children', label: 'simpOrgName', value: 'id' }"
          >
            <template #title="{ simpOrgName }">
              <template
                v-for="(fragment, i) in simpOrgName
                .toString()
                .split(new RegExp(`(?<=${formData.searchValue})|(?=${formData.searchValue})`, 'i'))"
                :key="i"
              >
                <span
                  v-if="fragment.toString().toLowerCase() === formData.searchValue"
                  style="color: #f9906b"
                >
                  {{ fragment }}
                </span>
                <template v-else>{{ fragment }}</template>
              </template>
            </template>
          </a-tree-select>
        </a-form-item>
      </a-col>

      <!-- 架构类型选择 -->
      <a-col :span="6">
        <a-form-item label="" name="type">
          <a-radio-group
            v-model:value="formData.type"
            button-style="solid"
            class="w-full"
            @change="handleTypeChange"
          >
            <a-radio-button value="1">管理架构</a-radio-button>
            <a-radio-button value="2" v-if="hasPerm('groupManageTree')">
              三层总部直管架构
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<script setup>
import { hasPerm } from '@/utils/permission'

// Props
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  currentVersionLabel: {
    type: String,
    default: ''
  },
  orgTreeData: {
    type: Array,
    default: () => []
  },
  orgTreeProps: {
    type: Object,
    default: () => ({})
  },
  disabledDate: {
    type: Function,
    default: () => false
  }
})

// Emits
const emit = defineEmits([
  'dateChange',
  'queryCurrentVersion',
  'orgChange',
  'typeChange'
])

// Event handlers
const handleDateChange = (date, yearMonth) => {
  emit('dateChange', date, yearMonth)
}

const handleQueryCurrentVersion = () => {
  emit('queryCurrentVersion')
}

const handleOrgChange = (value) => {
  emit('orgChange', value)
}

const handleTypeChange = (e) => {
  emit('typeChange', e.target.value)
}
</script>

<style scoped>
/* 组件特定样式 */
</style>

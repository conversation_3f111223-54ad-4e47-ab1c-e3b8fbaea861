<template>
  <!-- 节点详情面板 -->
  <div 
    v-if="visible" 
    class="context-menu" 
    :style="{ top: `${position.y}px`, left: `${position.x}px` }"
  >
    <a-descriptions 
      bordered 
      :title="selectedNode.data?.text" 
      size="small" 
      layout="vertical"
    >
      <template #extra>
        <CloseOutlined @click="handleClose" />
      </template>
      
      <a-descriptions-item label="所属管理主体">
        {{ selectedNode.manageCompany }}
      </a-descriptions-item>
      
      <a-descriptions-item label="是否上市">
        {{ formatDictData('WHETHER', selectedNode.isListed) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="注册资本（万元）">
        {{ selectedNode.registeredCapital }}
      </a-descriptions-item>
      
      <a-descriptions-item label="管理层级">
        {{ selectedNode.managementLevel }}
      </a-descriptions-item>
      
      <a-descriptions-item label="法定代表人">
        {{ selectedNode.legalPersonName }}
      </a-descriptions-item>
      
      <a-descriptions-item label="法人层级">
        {{ selectedNode.legalEntityLevel }}
      </a-descriptions-item>
      
      <a-descriptions-item label="机构类型" :span="3">
        {{ formatDictData('filling_standards', selectedNode.fillingStandards) }}
      </a-descriptions-item>
      
      <a-descriptions-item 
        label="上市信息" 
        :span="3" 
        v-if="selectedNode.isListed === 'Y'"
      >
        <a-table 
          :columns="listedColumns" 
          :data-source="selectedNode.listedVoList" 
          :pagination="false" 
          size="middle" 
        />
      </a-descriptions-item>
      
      <a-descriptions-item label="内部股东" :span="3">
        <template v-for="item in sortedInvestmentList" :key="item.id">
          股东名称: {{ item.investmentName }}
          <br />
          持股比例: {{ (item.shareRate * 100) }}%
          <br />
        </template>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { sortBy } from 'lodash-es'
import tool from '@/utils/tool'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
  selectedNode: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['close'])

// 上市信息表格列配置
const listedColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    customRender: ({ index }) => `${index + 1}`
  },
  {
    title: '市场',
    dataIndex: 'market',
    customRender: ({ text }) => tool.dictTypeData('market_listed', text)
  },
  {
    title: '股票简称',
    dataIndex: 'stock',
  },
  {
    title: '股票代码',
    dataIndex: 'stockCode',
  }
]

// 计算属性
const sortedInvestmentList = computed(() => {
  return sortBy(props.selectedNode.investmentVoList || [])
})

// 方法
const formatDictData = (type, value) => {
  return tool.dictTypeData(type, value)
}

const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.context-menu {
  position: fixed;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 10px;
  border-radius: 4px;
  max-width: 600px;
  max-height: 500px;
  overflow: auto;
}
</style>

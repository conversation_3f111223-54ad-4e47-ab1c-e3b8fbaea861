// Web Worker for data transformation to avoid blocking main thread
// 专为低端设备设计的数据转换Worker

self.onmessage = function(e) {
  const { type, data, config } = e.data

  try {
    let result = null

    switch (type) {
      case 'TRANSFORM_ALL_DATA':
        result = transformAllGraphDataInWorker(data, config)
        break
      case 'TRANSFORM_GROUP_DATA':
        result = transformGroupGraphDataInWorker(data, config)
        break
      case 'TRANSFORM_MANAGE_DATA':
        result = transformManageGraphDataInWorker(data, config)
        break
      default:
        throw new Error(`Unknown transform type: ${type}`)
    }

    // 发送成功结果
    self.postMessage({
      success: true,
      result: result,
      type: type
    })
  } catch (error) {
    // 发送错误结果
    self.postMessage({
      success: false,
      error: error.message,
      type: type
    })
  }
}

// 简化的数据转换函数（在Worker中运行）
function transformAllGraphDataInWorker(data, config) {
  if (!data) return null

  const { maxNodes = 100 } = config

  // 构建简化的节点结构
  const nodes = []
  
  // 处理投资列表（限制数量）
  if (data.investList) {
    const limitedInvestList = data.investList.slice(0, maxNodes)
    limitedInvestList.forEach(item => {
      nodes.push({
        data: {
          text: item.simpOrgName || item.name || '未知',
          uid: item.entityId,
          isClickable: true,
          ...item
        }
      })
    })
  }

  // 处理详情列表（限制数量）
  if (data.detailList) {
    const limitedDetailList = data.detailList.slice(0, maxNodes)
    limitedDetailList.forEach(item => {
      nodes.push({
        data: {
          text: item.simpOrgName || item.name || '未知',
          uid: item.entityId,
          isClickable: true,
          ...item
        }
      })
    })
  }

  return {
    nodes: nodes.slice(0, maxNodes), // 再次确保不超过限制
    meta: {
      originalCount: (data.investList?.length || 0) + (data.detailList?.length || 0),
      processedCount: nodes.length,
      isLimited: nodes.length < ((data.investList?.length || 0) + (data.detailList?.length || 0))
    }
  }
}

function transformGroupGraphDataInWorker(data, config) {
  if (!data) return null

  const { maxNodes = 100 } = config
  const nodes = []

  // 处理集团树列表
  if (data.groupTreeList) {
    const limited = data.groupTreeList.slice(0, Math.floor(maxNodes / 3))
    limited.forEach(item => {
      nodes.push({
        data: {
          text: item.simpOrgName || item.name || '未知',
          uid: item.entityId,
          isClickable: true,
          type: 'group',
          ...item
        }
      })
    })
  }

  // 处理股份树列表
  if (data.stockTreeList) {
    const limited = data.stockTreeList.slice(0, Math.floor(maxNodes / 3))
    limited.forEach(item => {
      nodes.push({
        data: {
          text: item.simpOrgName || item.name || '未知',
          uid: item.entityId,
          isClickable: true,
          type: 'stock',
          ...item
        }
      })
    })
  }

  // 处理有限树列表
  if (data.limitedTreeList) {
    const limited = data.limitedTreeList.slice(0, Math.floor(maxNodes / 3))
    limited.forEach(item => {
      nodes.push({
        data: {
          text: item.simpOrgName || item.name || '未知',
          uid: item.entityId,
          isClickable: true,
          type: 'limited',
          ...item
        }
      })
    })
  }

  return {
    nodes: nodes.slice(0, maxNodes),
    meta: {
      originalCount: (data.groupTreeList?.length || 0) + (data.stockTreeList?.length || 0) + (data.limitedTreeList?.length || 0),
      processedCount: nodes.length,
      isLimited: true
    }
  }
}

function transformManageGraphDataInWorker(data, config) {
  if (!data) return null

  const { maxNodes = 100 } = config
  const nodes = []

  // 处理投资列表
  if (data.investList) {
    const limited = data.investList.slice(0, maxNodes)
    limited.forEach(item => {
      nodes.push({
        data: {
          text: item.simpOrgName || item.name || '未知',
          uid: item.entityId,
          isClickable: true,
          ...item
        }
      })
    })
  }

  return {
    nodes: nodes,
    meta: {
      originalCount: data.investList?.length || 0,
      processedCount: nodes.length,
      isLimited: nodes.length < (data.investList?.length || 0)
    }
  }
}

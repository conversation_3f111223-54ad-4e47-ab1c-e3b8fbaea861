import { ref, shallowRef, toRaw, nextTick } from 'vue'
import MindMap from 'simple-mind-map'
// 按需加载插件，减少初始化负担
import MiniMap from 'simple-mind-map/src/plugins/MiniMap.js'
import KeyboardNavigation from 'simple-mind-map/src/plugins/KeyboardNavigation.js'
import Select from 'simple-mind-map/src/plugins/Select.js'
import TouchEvent from 'simple-mind-map/src/plugins/TouchEvent.js'
import { ROOT_NODE_IDS } from '@/views/cms/managementStructure/constants'
import { isNil } from 'lodash-es'

// 节点内容缓存
const nodeContentCache = new Map()

/**
 * 思维导图操作组合函数 - 性能优化版本
 */
export function useMindMap() {
  // 只注册必要的插件，减少初始化时间
  MindMap.usePlugin(MiniMap)
    .usePlugin(KeyboardNavigation)
    .usePlugin(Select)
    .usePlugin(TouchEvent)

  const mindMapContainerRef = ref()
  const loading = ref(true)
  let mindMap = null
  const _mindMap = ref()

  // 右键菜单相关数据
  const currentNode = shallowRef(null)
  const showContextMenu = ref(false)
  const showNodeTooltipPanel = ref(false)
  const menuPosition = ref({ x: 0, y: 0 })
  const nodeTooltipPos = ref({ x: 0, y: 0 })
  const selectedNode = ref({})

  /**
   * 初始化思维导图
   */
  const initMindMap = async (onNodeContextMenu, onNodeClick, onDrawClick, onExpandBtnClick, onNodeTreeRenderEnd) => {
    mindMap = new MindMap({
      el: mindMapContainerRef.value,
      data: null,
      disableMouseWheelZoom: true,
      editable: false,
      isUseCustomNodeContent: true,
			openPerformance: true,
			enableFreeDrag: false,
      initRootNodePosition: ['center','15%'],
      expandBtnSize: 24,
      expandBtnStyle: {
        color: '#808080',
        fill: '#fff',
        fontSize: 10,
        strokeColor: '#333333'
      },
      customCreateNodeContent: createCustomNodeContent,
      mousewheelAction: 'zoom',
      layout: 'catalogOrganization',
      themeConfig: {
        lineWidth: 2,
        lineColor: '#a6a6a6',
        lineDasharray: 'none',
        lineStyle: 'straight',
				borderWidth: 0,
				root:{
					hoverRectColor: '#fafafa'
				}
      },
      rootLineKeepSameInCurve: true,
      lineRadius: 5,
      showLineMarker: false,
      isShowCreateChildBtnIcon: false,
      generalizationLineWidth: 1,
    })

    _mindMap.value = toRaw(mindMap)

    // 绑定事件
    mindMap.on('node_contextmenu', onNodeContextMenu)
    mindMap.on('node_click', onNodeClick)
    mindMap.on('draw_click', onDrawClick)
    mindMap.on('expand_btn_click', onExpandBtnClick)
    mindMap.on('node_tree_render_end', onNodeTreeRenderEnd)
  }

  /**
   * 创建自定义节点内容 - 优化版本，使用缓存和批量DOM操作
   */
  const createCustomNodeContent = (node) => {
    const { managementLevel, hasBusiness, text, entityId } = node.nodeData.data

    // 生成缓存键
    const cacheKey = `${entityId}_${managementLevel}_${hasBusiness}_${text}_${node.layerIndex}`

    // 检查缓存
    if (nodeContentCache.has(cacheKey)) {
      return nodeContentCache.get(cacheKey).cloneNode(true)
    }

    const div = document.createElement('div')
    const isChildrenLevel = node.layerIndex === 1
    const layoutClass = isChildrenLevel ? 'vertical-layout' : 'horizontal-layout'

    // 优化类名构建逻辑
    let className
    if ([...ROOT_NODE_IDS,'root-0'].includes(entityId)) {
      className = `${hasBusiness} my-industry-node-level-${managementLevel} ${layoutClass}`
    } else {
      if (isNil(hasBusiness) || isNil(managementLevel)) {
        className = `K my-industy-node-level-default-0 ${layoutClass}`
      } else {
        className = `${hasBusiness} my-industry-node-level-${Math.abs(managementLevel)} ${layoutClass}`
      }
    }

    div.className = className

    // 使用 textContent 而不是 innerHTML，提高性能和安全性
    const nodeDiv = document.createElement('div')
    nodeDiv.className = 'my-industry-node text-center'

    const cardBody = document.createElement('div')
    cardBody.className = 'my-card-body'
    cardBody.textContent = text

    nodeDiv.appendChild(cardBody)
    div.appendChild(nodeDiv)

    // 缓存节点内容，限制缓存大小
    if (nodeContentCache.size > 1000) {
      const firstKey = nodeContentCache.keys().next().value
      nodeContentCache.delete(firstKey)
    }
    nodeContentCache.set(cacheKey, div.cloneNode(true))

    return div
  }

  /**
   * 统计思维导图节点信息
   */
  const analyzeMindMapData = (data) => {
    const stats = {
      totalNodes: 0,
      nodesByLevel: {},
      nodesByType: {},
      maxDepth: 0
    }

    const analyzeNode = (node, depth = 0) => {
      if (!node) return

      stats.totalNodes++
      stats.maxDepth = Math.max(stats.maxDepth, depth)

      // 按层级统计
      stats.nodesByLevel[depth] = (stats.nodesByLevel[depth] || 0) + 1

      // 按类型统计
      const nodeType = node.data?.type || node.type || 'unknown'
      stats.nodesByType[nodeType] = (stats.nodesByType[nodeType] || 0) + 1

      // 递归统计子节点
      if (node.children && Array.isArray(node.children)) {
        node.children.forEach(child => analyzeNode(child, depth + 1))
      }
    }

    if (data) {
      analyzeNode(data)
    }

    return stats
  }

  /**
   * 更新思维导图数据 - 优化版本，使用防抖和批量更新
   */
  let updateTimer = null
  const updateMindMapData = (data, callback) => {
    if (!mindMap) return

    // 统计节点信息
    const stats = analyzeMindMapData(data)
    console.log('📊 思维导图数据统计:')
    console.log(`📊 节点总数: ${stats.totalNodes}`)
    console.log(`📊 最大深度: ${stats.maxDepth}`)
    console.log('📊 节点层级分布:', stats.nodesByLevel)
    console.log('📊 节点类型分布:', stats.nodesByType)

    // 防抖处理，避免频繁更新
    if (updateTimer) {
      clearTimeout(updateTimer)
    }

    updateTimer = setTimeout(() => {
      try {
        mindMap.updateData(data)

        // 渲染完成后统计实际节点
        setTimeout(() => {
          try {
            const renderedNodes = mindMap.renderer?.nodeList || []
            console.log('📊 实际渲染统计:')
            console.log(`📊 实际渲染节点数: ${renderedNodes.length}`)
          } catch (error) {
            console.warn('获取渲染节点统计失败:', error)
          }
        }, 200)

        // 数据更新完成后执行回调
        if (callback && typeof callback === 'function') {
          // 等待一个渲染周期后执行回调，确保DOM更新完成
          setTimeout(callback, 50)
        }
      } catch (error) {
        console.error('更新思维导图数据失败:', error)
      }
    }, 100)
  }

  /**
   * 整体渲染，会进行节点复用思维导图数据 - 优化版本
   */
  const renderMindMap = () => {
    if (mindMap) {
      // 使用 requestAnimationFrame 优化渲染时机
      requestAnimationFrame(() => {
        try {
          mindMap.render()
        } catch (error) {
          console.error('渲染思维导图失败:', error)
        }
      })
    }
  }

  /**
   * 清理缓存
   */
  const clearNodeCache = () => {
    nodeContentCache.clear()
  }



  /**
   * 执行思维导图命令
   */
  const execMindMapCommand = (command, ...args) => {
    if (mindMap) {
      try {
        console.log(`执行思维导图命令: ${command}`, args)
        mindMap.execCommand(command, ...args)
      } catch (error) {
        console.error(`执行命令 ${command} 失败:`, error)
      }
    }
  }

  /**
   * 延迟执行思维导图命令，确保在数据更新和渲染完成后执行
   */
  const execMindMapCommandDelayed = (command, delay = 200, ...args) => {
    return new Promise((resolve) => {
      // 检查思维导图实例是否存在
      if (!mindMap) {
        console.warn('思维导图实例不存在，无法执行命令')
        resolve(false)
        return
      }

      // 等待渲染完成的回调函数
      const executeCommand = () => {
        try {
          console.log(`延迟执行思维导图命令: ${command}`, args)

          // 检查思维导图是否有数据和渲染器
          if (!mindMap.renderer || !mindMap.renderer.root) {
            console.warn('思维导图还没有渲染完成，继续等待...')
            // 递归等待，但限制最大等待次数
            if (executeCommand.retryCount < 20) { // 最多重试20次，即2秒
              executeCommand.retryCount = (executeCommand.retryCount || 0) + 1
              setTimeout(executeCommand, 100)
              return
            } else {
              console.error('等待思维导图渲染超时')
              resolve(false)
              return
            }
          }

          mindMap.execCommand(command, ...args)
          console.log(`命令 ${command} 执行成功`)
          resolve(true)
        } catch (error) {
          console.error(`延迟执行命令 ${command} 失败:`, error)
          resolve(false)
        }
      }

      setTimeout(executeCommand, delay)
    })
  }

  /**
   * 等待渲染完成后执行命令
   */
  const execMindMapCommandAfterRender = (command, ...args) => {
    return new Promise((resolve) => {
      if (!mindMap) {
        console.warn('思维导图实例不存在，无法执行命令')
        resolve(false)
        return
      }

      // 监听渲染完成事件的回调函数
      const onRenderEnd = () => {
        try {
          console.log(`渲染完成后执行命令: ${command}`, args)
          mindMap.execCommand(command, ...args)
          resolve(true)
        } catch (error) {
          console.error(`渲染完成后执行命令 ${command} 失败:`, error)
          resolve(false)
        }
      }

      // 检查是否已经渲染完成
      if (mindMap.renderer && mindMap.renderer.root) {
        // 已经渲染完成，直接执行
        setTimeout(onRenderEnd, 100)
      } else {
        // 还没渲染完成，使用轮询方式等待
        let checkCount = 0
        const maxChecks = 50 // 最多检查5秒

        const checkRenderStatus = () => {
          checkCount++

          if (mindMap.renderer && mindMap.renderer.root) {
            // 渲染完成，执行命令
            onRenderEnd()
          } else if (checkCount >= maxChecks) {
            // 超时，放弃执行
            console.warn(`等待渲染完成超时，无法执行命令: ${command}`)
            resolve(false)
          } else {
            // 继续等待
            setTimeout(checkRenderStatus, 100)
          }
        }

        checkRenderStatus()
      }
    })
  }

  /**
   * 更新思维导图配置
   */
  const updateMindMapConfig = (config) => {
    if (mindMap) {
      mindMap.updateConfig(config)
    }
  }

  /**
   * 清空思维导图
   */
  const clearMindMap = () => {
    if (mindMap) {
      mindMap.clearDraw()
    }
  }

  /**
   * 隐藏右键菜单
   */
  const hideContextMenu = () => {
    menuPosition.value = { x: 0, y: 0 }
    showContextMenu.value = false
    showNodeTooltipPanel.value = false
    currentNode.value = null
  }

  /**
   * 显示右键菜单
   */
  const showContextMenuAt = (x, y, node) => {
    menuPosition.value = { x: x + 10, y: y + 10 }
    showContextMenu.value = true
    currentNode.value = node
  }

  /**
   * 显示节点详情面板
   */
  const showNodeTooltip = (nodeData, containerRef) => {
    selectedNode.value = nodeData
    const basePosition = containerRef.getBoundingClientRect()
    nodeTooltipPos.value.x = basePosition.x
    nodeTooltipPos.value.y = basePosition.y
    showNodeTooltipPanel.value = true
    showContextMenu.value = false
  }

  /**
   * 根据UID查找节点
   */
  const findNodeByUid = (uid) => {
    if (mindMap && mindMap.renderer) {
      return mindMap.renderer.findNodeByUid(uid)
    }
    return null
  }

  /**
   * 将节点移动到中心
   */
  const moveNodeToCenter = (node) => {
    if (mindMap && mindMap.renderer && node) {
      mindMap.renderer.moveNodeToCenter(node, undefined)
    }
  }

  return {
    // 响应式数据
    mindMapContainerRef,
    loading,
    currentNode,
    showContextMenu,
    showNodeTooltipPanel,
    menuPosition,
    nodeTooltipPos,
    selectedNode,

    // 方法
    initMindMap,
    updateMindMapData,
    execMindMapCommand,
    execMindMapCommandDelayed,
    execMindMapCommandAfterRender,
    updateMindMapConfig,
    clearMindMap,
    renderMindMap,
    hideContextMenu,
    showContextMenuAt,
    showNodeTooltip,
    findNodeByUid,
    moveNodeToCenter,
    clearNodeCache,

    // 原始实例
    getMindMap: () => mindMap,
    _mindMap
  }
}

import { groupBy } from 'lodash-es'

// 常量配置
const ROOT_NODE_LEVEL_MAP = {
  "20140824164901061871": 'group-0',
  "20140822152314001513": 'stock-0',
  "20140824164901061873": 'limit-0'
}

const ROOT_NODE_IDS = Object.keys(ROOT_NODE_LEVEL_MAP)

/**
 * 图形数据转换组合函数
 */
export function useGraphDataTransform() {

  /**
   * 统计原始数据信息
   */
  const analyzeRawData = (data, dataType = 'unknown') => {
    const stats = {
      dataType,
      totalItems: 0,
      investListCount: 0,
      upperListCount: 0,
      detailListCount: 0,
      hasVersionId: !!data?.versionId
    }

    if (data) {
      if (data.investList && Array.isArray(data.investList)) {
        stats.investListCount = data.investList.length
        stats.totalItems += data.investList.length
      }

      if (data.upperList && Array.isArray(data.upperList)) {
        stats.upperListCount = data.upperList.length
        stats.totalItems += data.upperList.length
      }

      if (data.detailList && Array.isArray(data.detailList)) {
        stats.detailListCount = data.detailList.length
        stats.totalItems += data.detailList.length
      }
    }

    console.log(`📊 原始数据统计 (${dataType}):`)
    console.log(`📊 总项目数: ${stats.totalItems}`)
    console.log(`📊 投资列表: ${stats.investListCount}`)
    console.log(`📊 上级列表: ${stats.upperListCount}`)
    console.log(`📊 详细列表: ${stats.detailListCount}`)
    console.log(`📊 版本ID: ${data?.versionId || '无'}`)

    return stats
  }

  /**
   * 构建投资树结构 - 添加展开控制
   */
  const buildInvestTree = (node, versionId, currentLevel = 0, maxExpandLevel = 2) => {
    return {
      data: {
        text: node.simpOrgName,
        isClickable: true,
        uid: node.entityId,
        versionId: versionId,
        expand: currentLevel < maxExpandLevel, // 控制节点是否展开
        ...node
      },
      children: (node.detailList || [])
        .filter(v => !ROOT_NODE_IDS.includes(v.entityId))
        .map(child => buildInvestTree(child, versionId, currentLevel + 1, maxExpandLevel))
    }
  }

  /**
   * 收集上级链条节点
   */
  const collectUpperChain = (node, chain = []) => {
    chain.push(node)
    if (ROOT_NODE_IDS.includes(node.entityId)) {
      return chain
    }
    if (node.detailList && node.detailList.length > 0) {
      return collectUpperChain(node.detailList[0], chain)
    }
    return chain
  }

  /**
   * 构建链条树结构 - 添加展开控制
   */
  const buildChainTree = (chain, currentNode, maxExpandLevel = 1) => {
    let root = currentNode
    for (let i = 0; i < chain.length; i++) {
      const node = chain[i]
      root = {
        data: {
          text: node.simpOrgName,
          entityId: node.entityId,
          managementLevel: ROOT_NODE_IDS.includes(node.entityId) ? ROOT_NODE_LEVEL_MAP[node.entityId] : node.managementLevel,
          hasBusiness: ROOT_NODE_IDS.includes(node.entityId) ? 'Y' : node.hasBusiness,
          isClickable: node.isClickable === '1',
          versionId: currentNode.data.versionId,
          expand: i < maxExpandLevel // 控制节点是否展开
        },
        children: [root]
      }
    }
    return root
  }

  /**
   * 转换管理架构数据为思维导图格式 - 添加展开控制
   */
  const transformManageGraphData = (data, maxExpandLevel = 1) => {
    // 统计原始数据
    analyzeRawData(data, '管理架构')

    const versionId = data.versionId
    // 使用接口数据中的managementLevel作为展开层级
    const actualExpandLevel = data.managementLevel ?? maxExpandLevel

    // 当前节点为根节点
    const currentNode = {
      data: {
        text: data.simpOrgName,
        entityId: data.entityId,
        managementLevel: ROOT_NODE_IDS.includes(data.entityId) ? ROOT_NODE_LEVEL_MAP[data.entityId] : data.managementLevel,
        hasBusiness: ROOT_NODE_IDS.includes(data.entityId) ? 'Y' : data.hasBusiness,
        isClickable: true,
        uid: data.entityId,
        versionId: versionId,
        expand: true // 根节点默认展开
      },
      children: (data.investList || [])
        .filter(v => !ROOT_NODE_IDS.includes(v.entityId))
        .map(child => buildInvestTree(child, versionId, 1, actualExpandLevel)) // 从第1级开始计算
    }

    // 处理上级链条
    let graphJsonData = currentNode
    if (data.upperList && data.upperList.length > 0 && !ROOT_NODE_IDS.includes(data.entityId)) {
      const upper = data.upperList[0]
      const chain = collectUpperChain(upper, [])
      graphJsonData = {
        data: {
          text: '',
          hasBusiness: 'Y',
          managementLevel: 'root-0',
          uid: 'root-0',
					entityId: 'root-0',
					fillColor: '#fff',
					expand: true // 根节点默认展开
        },
        children: [buildChainTree(chain, currentNode, actualExpandLevel)]
      }
    }

    return graphJsonData
  }

  /**
   * 转换集团架构数据为思维导图格式 - 添加展开控制
   */
  const transformGroupGraphData = (data, maxExpandLevel = 2) => {
    // 统计原始数据
    analyzeRawData(data, '集团架构')

    // 使用接口数据中的managementLevel作为展开层级
    const actualExpandLevel = data.managementLevel ?? maxExpandLevel
    const graphJsonData = {
      data: {
        text: '',
        hasBusiness: 'Y',
        managementLevel: 'root-0',
        uid: 'root-0',
				entityId: 'root-0',
				fillColor: '#fff',
				expand: true // 根节点默认展开
      },
      children: [
        {
          data: {
            uid: '20140824164901061871',
            text: '中国中信集团有限公司',
            isClickable: true,
            entityId: '20140824164901061871',
						versionId: "00000000000000000000",
            managementLevel: 'group-0',
            hasBusiness: 'Y',
            expand: actualExpandLevel > 1 // 第1级节点根据actualExpandLevel控制
          },
          children: []
        },
        {
          data: {
            uid: '20140822152314001513',
            text: '中国中信股份有限公司',
            isClickable: true,
            entityId: '20140822152314001513',
						versionId: "00000000000000000000",
            managementLevel: 'stock-0',
            hasBusiness: 'Y',
            expand: actualExpandLevel > 1 // 第1级节点根据actualExpandLevel控制
          },
          children: []
        },
        {
          data: {
            uid: '20140824164901061873',
            text: '中国中信有限公司',
            isClickable: true,
            entityId: '20140824164901061873',
						versionId: "00000000000000000000",
            managementLevel: 'limit-0',
            hasBusiness: 'Y',
            expand: actualExpandLevel > 1 // 第1级节点根据actualExpandLevel控制
          },
          children: []
        }
      ]
    }

    // 优化数据处理，减少重复操作和内存分配 - 添加展开控制
    const processGroupData = (dataList, targetIndex) => {
      if (!dataList || dataList.length === 0) return

      const grouped = groupBy(dataList, 'manageCompany')
      const children = []

      // 批量处理，减少数组操作
      for (const [key, values] of Object.entries(grouped)) {
        children.push({
          data: {
            text: key,
            managementLevel: '2',
            hasBusiness: 'Y',
            expand: false // 修正：第2级节点不展开子节点
          },
          children: values.map(vc => ({
            data: {
              text: vc.simpOrgName,
              isClickable: true,
              entityId: vc.entityId,
              managementLevel: vc.managementLevel,
              hasBusiness: vc.hasBusiness,
              versionId: vc.versionId,
              expand: false // 第3级及以下默认不展开
              // 避免使用扩展运算符，减少对象复制开销
            }
          }))
        })
      }

      graphJsonData.children[targetIndex].children = children
    }

    // 批量处理各类型数据，避免重复的 forEach 调用
    processGroupData(data.groupTreeList, 0)    // 集团
    processGroupData(data.stockTreeList, 1)    // 股份
    processGroupData(data.limitedTreeList, 2)  // 有限

    return graphJsonData
  }

  /**
   * 转换全部架构数据为思维导图格式 - 添加展开控制
   */
  const transformAllGraphData = (data, maxExpandLevel = 1) => {
    // 统计原始数据
    analyzeRawData(data, '全部架构')

    // 使用接口数据中的managementLevel作为展开层级
    const actualExpandLevel = data.managementLevel ?? maxExpandLevel
    const convertToMindMapWithUpper = (treeData) => {
      const convertNode = (node, currentLevel = 1) => ({
        data: {
          text: node.simpOrgName,
					versionId: data.versionId,
          isClickable: true,
          expand: false, // 修正：当前层级小于等于maxExpandLevel时展开
          ...node
        },
        children: (node.detailList || []).map(child => convertNode(child, currentLevel + 1))
      })
      return (treeData.investList || []).filter(v => !ROOT_NODE_IDS.includes(v.entityId)).map(node => convertNode(node, 1))
    }

    const graphJsonData = {
      data: {
        text: '',
        hasBusiness: 'Y',
        managementLevel: 'root-0',
        uid: 'root-0',
				entityId: 'root-0',
				fillColor: '#fff',
				expand: true // 根节点默认展开
      },
      children: [
        {
          data: {
            text: "中国中信集团有限公司",
            uid: '20140824164901061871',
            isClickable: true,
            entityId: '20140824164901061871',
						versionId: data.versionId,
            managementLevel: 'group-0',
            hasBusiness: 'Y',
            expand: actualExpandLevel >= 1 // 第1级节点：当actualExpandLevel>=1时展开
          },
          children: convertToMindMapWithUpper(data.groupTree)
        },
        {
          data: {
            text: '中国中信股份有限公司',
            uid: '20140822152314001513',
            isClickable: true,
            entityId: '20140822152314001513',
						versionId: data.versionId,
            managementLevel: 'stock-0',
            hasBusiness: 'Y',
            expand: actualExpandLevel >= 1 // 第1级节点：当actualExpandLevel>=1时展开
          },
          children: convertToMindMapWithUpper(data.stockTree)
        },
        {
          data: {
            text: '中国中信有限公司',
            uid: '20140824164901061873',
            isClickable: true,
            entityId: '20140824164901061873',
						versionId: data.versionId,
            managementLevel: 'limit-0',
            hasBusiness: 'Y',
            expand: actualExpandLevel >= 1 // 第1级节点：当actualExpandLevel>=1时展开
          },
          children: convertToMindMapWithUpper(data.limitedTree)
        }
      ]
    }

    return graphJsonData
  }


  /**
   * 分块处理投资列表数据
   */
  const processInvestListInChunks = (investList, versionId, chunkSize = 50, maxExpandLevel = 2) => {
    if (!investList || investList.length === 0) return []

    // 优化：小数据集直接处理，避免不必要的分块
    if (investList.length <= chunkSize) {
      return investList.map(child => buildInvestTree(child, versionId, 1, maxExpandLevel))
    }

    // 优化：预分配数组大小，减少内存重分配
    const result = new Array(investList.length)
    let resultIndex = 0

    // 分块处理大量数据，使用更高效的循环
    for (let i = 0; i < investList.length; i += chunkSize) {
      const endIndex = Math.min(i + chunkSize, investList.length)

      // 批量处理当前块，避免slice和spread操作
      for (let j = i; j < endIndex; j++) {
        result[resultIndex++] = buildInvestTree(investList[j], versionId, 1, maxExpandLevel)
      }
    }

    return result
  }

  /**
   * 优化的管理架构数据转换 - 支持分块处理和展开控制
   */
  const transformManageGraphDataOptimized = (data, chunkSize = 50, maxExpandLevel = 1) => {
    const versionId = data.versionId

    // 当前节点为根节点
    const currentNode = {
      data: {
        text: data.simpOrgName,
        entityId: data.entityId,
        managementLevel: ROOT_NODE_IDS.includes(data.entityId) ? ROOT_NODE_LEVEL_MAP[data.entityId] : data.managementLevel,
        hasBusiness: ROOT_NODE_IDS.includes(data.entityId) ? 'Y' : data.hasBusiness,
        isClickable: true,
        uid: data.entityId,
        versionId: versionId,
        expand: true // 根节点默认展开
      },
      children: processInvestListInChunks(
        (data.investList || []).filter(v => !ROOT_NODE_IDS.includes(v.entityId)),
        versionId,
        chunkSize,
        maxExpandLevel
      )
    }

    // 处理上级链条
    let graphJsonData = currentNode
    if (data.upperList && data.upperList.length > 0 && !ROOT_NODE_IDS.includes(data.entityId)) {
      const upper = data.upperList[0]
      const chain = collectUpperChain(upper, [])
      graphJsonData = {
        data: {
          text: '',
          hasBusiness: 'Y',
          managementLevel: 'root-0',
          uid: 'root-0',
          entityId: 'root-0',
          fillColor: '#fff',
          expand: true // 根节点默认展开
        },
        children: [buildChainTree(chain, currentNode, maxExpandLevel)]
      }
    }

    return graphJsonData
  }

  return {
    transformManageGraphData,
    transformGroupGraphData,
    transformAllGraphData,
    transformManageGraphDataOptimized,
    processInvestListInChunks,
    ROOT_NODE_IDS,
    ROOT_NODE_LEVEL_MAP
  }
}

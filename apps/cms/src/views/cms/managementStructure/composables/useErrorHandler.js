import { ref } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 错误处理组合函数
 */
export function useErrorHandler() {
  const errors = ref([])
  const isLoading = ref(false)

  /**
   * 包装异步函数，添加统一的错误处理
   */
  const withErrorHandling = (asyncFn, options = {}) => {
    const {
      loadingMessage = '处理中...',
      successMessage = '',
      errorMessage = '操作失败',
      showLoading = true,
      onError = null,
      onSuccess = null
    } = options

    return async (...args) => {
      try {
        if (showLoading) {
          isLoading.value = true
          if (loadingMessage) {
            message.loading(loadingMessage)
          }
        }

        const result = await asyncFn(...args)

        if (successMessage) {
          message.success(successMessage)
        }

        if (onSuccess) {
          onSuccess(result)
        }

        return result
      } catch (error) {
        console.error('操作失败:', error)
        
        // 记录错误
        errors.value.push({
          timestamp: new Date(),
          error: error.message || error,
          context: asyncFn.name || 'unknown'
        })

        // 显示错误消息
        const finalErrorMessage = error.message || errorMessage
        message.error(finalErrorMessage)

        // 自定义错误处理
        if (onError) {
          onError(error)
        }

        throw error
      } finally {
        if (showLoading) {
          isLoading.value = false
          message.destroy()
        }
      }
    }
  }

  /**
   * 清理错误记录
   */
  const clearErrors = () => {
    errors.value = []
  }

  /**
   * 获取最近的错误
   */
  const getRecentErrors = (count = 5) => {
    return errors.value.slice(-count)
  }

  /**
   * 重试机制
   */
  const withRetry = (asyncFn, maxRetries = 3, delay = 1000) => {
    return async (...args) => {
      let lastError
      
      for (let i = 0; i <= maxRetries; i++) {
        try {
          return await asyncFn(...args)
        } catch (error) {
          lastError = error
          
          if (i === maxRetries) {
            throw error
          }
          
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
        }
      }
      
      throw lastError
    }
  }

  /**
   * 网络错误处理
   */
  const handleNetworkError = (error) => {
    if (!navigator.onLine) {
      message.error('网络连接已断开，请检查网络设置')
      return
    }

    if (error.code === 'NETWORK_ERROR') {
      message.error('网络请求失败，请稍后重试')
      return
    }

    if (error.response?.status === 401) {
      message.error('登录已过期，请重新登录')
      // 可以在这里触发重新登录逻辑
      return
    }

    if (error.response?.status === 403) {
      message.error('权限不足，无法执行此操作')
      return
    }

    if (error.response?.status >= 500) {
      message.error('服务器错误，请稍后重试')
      return
    }

    // 默认错误处理
    message.error(error.message || '操作失败')
  }

  return {
    errors,
    isLoading,
    withErrorHandling,
    withRetry,
    clearErrors,
    getRecentErrors,
    handleNetworkError
  }
}

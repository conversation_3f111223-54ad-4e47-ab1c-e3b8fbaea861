import { ref, reactive, computed } from 'vue'
import { cloneDeep, isEmpty, isNil, debounce } from 'lodash-es'
import { message } from 'ant-design-vue'
import bizInstitutionApi from '@/api/biz/bizInstitutionApi'
import portraitApi from '@/api/biz/portraitApi'
import { CACHE_CONFIG, DEBOUNCE_DELAY } from '../constants/index.js'
import { useErrorHandler } from './useErrorHandler.js'
import LZString from 'lz-string'

/**
 * 管理架构数据处理组合函数
 */
export function useManagementData() {
  // 错误处理
  const { withErrorHandling, handleNetworkError } = useErrorHandler()
  // 表单数据
  const structureForm = ref({
    type: '1',
    isCurrentVersion: '1'
  })

  // 版本相关数据
  const currentVersionLabel = ref('')
  const currentVersionValue = ref()

  // 组织树相关数据
  const orgTreeProps = reactive({
    keys: 'id',
    title: 'simpOrgName',
    loading: false,
    selectLeastOne: true,
    expanded: [], // 默认展开
  })
  const orgTreeData = ref([])

  // ==================== 缓存机制 - 使用sessionStorage + 压缩 ====================
  const CACHE_PREFIX = 'management_structure_'
  const CACHE_INDEX_KEY = 'management_cache_index'
  const CACHE_EXPIRY = CACHE_CONFIG.EXPIRY_TIME
  const maxCacheSize = 20 // 最大缓存数量

  /**
   * 生成缓存键
   */
  const generateCacheKey = (prefix, params) => {
    const keyParams = {
      prefix,
      ...params
    }
    return CACHE_PREFIX + btoa(JSON.stringify(keyParams))
  }

  /**
   * 获取缓存索引
   */
  const getCacheIndex = () => {
    try {
      const index = sessionStorage.getItem(CACHE_INDEX_KEY)
      return index ? JSON.parse(index) : []
    } catch (error) {
      console.warn('获取缓存索引失败:', error)
      return []
    }
  }

  /**
   * 更新缓存索引
   */
  const updateCacheIndex = (key, action = 'add') => {
    try {
      let index = getCacheIndex()

      if (action === 'add') {
        index = index.filter(item => item.key !== key)
        index.push({ key, timestamp: Date.now() })

        if (index.length > maxCacheSize) {
          const oldestItem = index.shift()
          sessionStorage.removeItem(oldestItem.key)
        }
      } else if (action === 'remove') {
        index = index.filter(item => item.key !== key)
      }

      sessionStorage.setItem(CACHE_INDEX_KEY, JSON.stringify(index))
    } catch (error) {
      console.warn('更新缓存索引失败:', error)
    }
  }

  /**
   * 获取缓存数据 - 支持压缩数据
   */
  const getCachedData = (key) => {
    try {
      const cached = sessionStorage.getItem(key)
      if (!cached) return null

      const parsedCache = JSON.parse(cached)

      // 检查是否过期
      if (Date.now() - parsedCache.timestamp < CACHE_EXPIRY) {
        // 检查是否为压缩数据
        if (parsedCache.compressed) {
          try {
            const decompressedStr = LZString.decompress(parsedCache.data)
            if (decompressedStr) {
              return JSON.parse(decompressedStr)
            } else {
              sessionStorage.removeItem(key)
              updateCacheIndex(key, 'remove')
              return null
            }
          } catch (decompressError) {
            console.error('解压缩失败:', decompressError)
            sessionStorage.removeItem(key)
            updateCacheIndex(key, 'remove')
            return null
          }
        } else {
          return parsedCache.data
        }
      } else {
        sessionStorage.removeItem(key)
        updateCacheIndex(key, 'remove')
        return null
      }
    } catch (error) {
      console.warn('缓存读取失败:', error)
      sessionStorage.removeItem(key)
      updateCacheIndex(key, 'remove')
      return null
    }
  }

  /**
   * 设置缓存数据 - 支持数据压缩
   */
  const setCachedData = (key, data) => {
    try {
      // 序列化数据
      const dataStr = JSON.stringify(data)
      const originalSize = new Blob([dataStr]).size

      // 使用LZString压缩数据
      const compressedData = LZString.compress(dataStr)
      const compressedSize = new Blob([compressedData]).size

      // 检查压缩后大小
      if (compressedSize > 3 * 1024 * 1024) {
        console.warn('数据过大，跳过缓存:', (compressedSize / 1024 / 1024).toFixed(2), 'MB')
        return
      }

      const cacheData = {
        compressed: true,
        data: compressedData,
        timestamp: Date.now(),
        originalSize: originalSize,
        compressedSize: compressedSize
      }

      sessionStorage.setItem(key, JSON.stringify(cacheData))
      updateCacheIndex(key, 'add')

    } catch (error) {
      console.error('缓存失败:', error)

      // 如果是存储空间不足，清理旧缓存后重试
      if (error.name === 'QuotaExceededError') {
        clearOldestCache()
        try {
          const simpleData = {
            compressed: false,
            data: data,
            timestamp: Date.now()
          }
          sessionStorage.setItem(key, JSON.stringify(simpleData))
          updateCacheIndex(key, 'add')
        } catch (retryError) {
          console.error('重试缓存失败:', retryError)
        }
      }
    }
  }

  /**
   * 清理最旧的缓存项
   */
  const clearOldestCache = () => {
    const index = getCacheIndex()
    if (index.length > 0) {
      index.sort((a, b) => a.timestamp - b.timestamp)
      const toDelete = index.slice(0, Math.ceil(index.length * 0.3))

      toDelete.forEach(item => {
        sessionStorage.removeItem(item.key)
      })

      const remainingIndex = index.slice(Math.ceil(index.length * 0.3))
      sessionStorage.setItem(CACHE_INDEX_KEY, JSON.stringify(remainingIndex))
    }
  }

  /**
   * 获取组织树数据
   */
  const fetchOrgTreeData = async () => {
    try {
      // 获取当前版本信息
      const versionRes = await bizInstitutionApi.getCurVersionDate()
      currentVersionLabel.value = versionRes ? `${versionRes.year}年${versionRes.period}月` : ''
      currentVersionValue.value = versionRes ? `${versionRes.year}-${versionRes.period}` : ''

      // 设置加载状态
      orgTreeProps.loading = true

      // 获取组织树结构
      const treeRes = await bizInstitutionApi.getStructureTree({ structureType: '2' })

      if (treeRes?.length) {
        // 处理树节点的可点击状态
        await disposeTree(treeRes)

        // 查找三个主要节点
        const nodeMap = {
          group: treeRes.find(r => r.id === '20140824164901061871'),
          stock: treeRes.find(r => r.id === '20140822152314001513'),
          limit: treeRes.find(r => r.id === '20140824164901061873')
        }

        // 构建组织树数据
        orgTreeData.value = [
          {
            simpOrgName: '全部',
            id: '00',
            disabled: nodeMap.group?.isClickable === '0',
            children: [nodeMap.group, nodeMap.stock, nodeMap.limit].filter(Boolean)
          }
        ]

        // 设置默认展开
        if (orgTreeData.value[0]?.id) {
          orgTreeProps.expanded.push(orgTreeData.value[0].id)
        }
      } else {
        orgTreeData.value = []
      }
    } catch (error) {
      console.error('获取组织树数据失败:', error)
      orgTreeData.value = []
    } finally {
      orgTreeProps.loading = false
    }
  }

  /**
   * 处理树节点的可点击状态
   */
  const disposeTree = async (tree) => {
    for (let i = 0; i < tree.length; i++) {
      tree[i].disabled = tree[i]?.isClickable == 1 ? false : true
      if (tree[i]?.children) {
        disposeTree(tree[i]?.children)
      }
    }
  }

  /**
   * 获取全部管理架构数据
   */
  const fetchAllGraphData = withErrorHandling(async () => {
    const params = cloneDeep(structureForm.value)
    delete params.entityId

    const cacheKey = generateCacheKey('allManageTree', params)
    const cachedData = getCachedData(cacheKey)

    if (cachedData) {
      console.log('📊 使用缓存的全部管理架构数据')
      return { ...cachedData, fromCache: true }
    }

    const data = await portraitApi.allManageTree(params)
    setCachedData(cacheKey, data)
    return { ...data, fromCache: false }
  }, {
    errorMessage: '获取全部管理架构数据失败',
    onError: handleNetworkError
  })

  /**
   * 获取集团管理架构数据
   */
  const fetchGroupGraphData = async () => {
    const params = cloneDeep(structureForm.value)

    const cacheKey = generateCacheKey('groupManageTree', params)
    const cachedData = getCachedData(cacheKey)

    if (cachedData) {
      console.log('📊 使用缓存的集团管理架构数据')
      return { ...cachedData, fromCache: true }
    }

    try {
      const data = await portraitApi.groupManageTree(params)
      setCachedData(cacheKey, data)
      return { ...data, fromCache: false }
    } catch (error) {
      console.error('获取集团管理架构数据失败:', error)
      throw error
    }
  }

  /**
   * 获取管理架构数据
   */
  const fetchManageGraphData = async () => {
    const params = cloneDeep(structureForm.value)

    const cacheKey = generateCacheKey('manageTree', params)
    const cachedData = getCachedData(cacheKey)

    if (cachedData) {
      console.log('📊 使用缓存的管理架构数据')
      return { ...cachedData, fromCache: true }
    }

    try {
      const data = await portraitApi.manageTree(params)
      if (isEmpty(data)) {
        message.warning('暂无数据')
        return null
      }
      setCachedData(cacheKey, data)
      return { ...data, fromCache: false }
    } catch (error) {
      console.error('获取管理架构数据失败:', error)
      throw error
    }
  }

  /**
   * 获取机构详情信息
   */
  const fetchInstitutionInfo = async (entityId, versionId) => {
    try {
      const res = await portraitApi.institutionInfo({
        entityId,
        versionId: versionId ?? structureForm.value?.versionId
      })
      return res
    } catch (error) {
      console.error('获取机构详情失败:', error)
      throw error
    }
  }

  /**
   * 在组织树中深度查找节点
   */
  const deepFindByEntityId = (data, entityId) => {
    for (const item of data) {
      if (item.id === entityId) {
        return item
      }
      if (item.children && item.children.length > 0) {
        const found = deepFindByEntityId(item.children, entityId)
        if (found) return found
      }
    }
    return undefined
  }

  /**
   * 处理日期变化（防抖处理）
   */
  const handleDateChange = debounce((date, yearMonth) => {
    console.log("日期变化:", date, yearMonth)
    if (!!date) {
      const [year, month] = date.split('-')
      structureForm.value.year = year
      structureForm.value.month = month
      delete structureForm.value.isCurrentVersion
    } else {
      delete structureForm.value?.year
      delete structureForm.value?.month
      structureForm.value.isCurrentVersion = '1'
    }
  }, DEBOUNCE_DELAY.DATE_CHANGE)

  /**
   * 查询当前版本
   */
  const handleQueryCurrentVersion = () => {
    delete structureForm.value.yearMonth
    structureForm.value.isCurrentVersion = '1'
    // 清理缓存，因为版本变化了
    clearCache()
  }

  /**
   * 清理缓存 - sessionStorage版本
   */
  const clearCache = () => {
    try {
      const index = getCacheIndex()
      index.forEach(item => {
        sessionStorage.removeItem(item.key)
      })
      sessionStorage.removeItem(CACHE_INDEX_KEY)
    } catch (error) {
      console.warn('清理缓存失败:', error)
    }
  }

  /**
   * 清理过期缓存 - sessionStorage版本
   */
  const clearExpiredCache = () => {
    try {
      const index = getCacheIndex()
      const now = Date.now()
      const validIndex = []

      index.forEach(item => {
        try {
          const cached = sessionStorage.getItem(item.key)
          if (cached) {
            const parsedCache = JSON.parse(cached)
            if (now - parsedCache.timestamp < CACHE_EXPIRY) {
              validIndex.push(item)
            } else {
              sessionStorage.removeItem(item.key)
            }
          }
        } catch (error) {
          sessionStorage.removeItem(item.key)
        }
      })

      sessionStorage.setItem(CACHE_INDEX_KEY, JSON.stringify(validIndex))
    } catch (error) {
      console.warn('清理过期缓存失败:', error)
    }
  }

  // ==================== 定期清理过期缓存 ====================
  let cacheCleanupTimer = null
  const startCacheCleanup = () => {
    cacheCleanupTimer = setInterval(() => {
      clearExpiredCache()
    }, 10 * 60 * 1000) // 每10分钟清理一次
  }

  const stopCacheCleanup = () => {
    if (cacheCleanupTimer) {
      clearInterval(cacheCleanupTimer)
      cacheCleanupTimer = null
    }
  }

  // 启动缓存清理
  startCacheCleanup()

  // ==================== 计算属性 ====================
  /**
   * 是否为管理架构类型
   */
  const isManagementType = computed(() => structureForm.value.type === '1')

  /**
   * 是否为集团架构类型
   */
  const isGroupType = computed(() => structureForm.value.type === '2')

  /**
   * 是否选择了机构
   */
  const hasSelectedEntity = computed(() => !isNil(structureForm.value?.entityId))

  /**
   * 是否选择了全部架构
   */
  const isAllArchitecture = computed(() => structureForm.value.entityId === '00')

  /**
   * 当前表单参数（用于API调用）
   */
  const currentParams = computed(() => cloneDeep(structureForm.value))

  return {
    // 响应式数据
    structureForm,
    currentVersionLabel,
    currentVersionValue,
    orgTreeProps,
    orgTreeData,

    // 计算属性
    isManagementType,
    isGroupType,
    hasSelectedEntity,
    isAllArchitecture,
    currentParams,

    // 方法
    fetchOrgTreeData,
    fetchAllGraphData,
    fetchGroupGraphData,
    fetchManageGraphData,
    fetchInstitutionInfo,
    deepFindByEntityId,
    handleDateChange,
    handleQueryCurrentVersion,
    clearCache,
    clearExpiredCache,
    stopCacheCleanup
  }
}

import { ref, computed, onMounted } from 'vue'

/**
 * 设备性能检测和优化配置组合函数
 */
export function useDeviceOptimization() {
  const deviceInfo = ref({
    hardwareConcurrency: navigator.hardwareConcurrency || 1,
    memory: null,
    connection: null,
    isLowEnd: false,
    performanceScore: 0
  })

  const optimizationConfig = ref({
    // 默认配置
    enableAnimation: true,
    renderQuality: 'high',
    cacheSize: 1000,
    debounceDelay: 100,
    chunkSize: 100,
    enableVirtualScroll: false,
    enableWebWorker: false,
    maxNodes: 1000
  })

  /**
   * 检测设备性能
   */
  const detectDevicePerformance = () => {
    let score = 0

    // CPU 核心数评分 (0-30分)
    const cores = deviceInfo.value.hardwareConcurrency
    if (cores >= 8) score += 30
    else if (cores >= 4) score += 20
    else if (cores >= 2) score += 10
    else score += 5

    // 内存评分 (0-40分)
    if (performance.memory) {
      deviceInfo.value.memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }

      const memoryLimitMB = performance.memory.jsHeapSizeLimit / (1024 * 1024)
      if (memoryLimitMB >= 4096) score += 40
      else if (memoryLimitMB >= 2048) score += 30
      else if (memoryLimitMB >= 1024) score += 20
      else if (memoryLimitMB >= 512) score += 10
      else score += 5
    } else {
      score += 20 // 默认中等分数
    }

    // 网络连接评分 (0-20分)
    if (navigator.connection) {
      deviceInfo.value.connection = {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      }

      const effectiveType = navigator.connection.effectiveType
      if (effectiveType === '4g') score += 20
      else if (effectiveType === '3g') score += 15
      else if (effectiveType === '2g') score += 5
      else score += 10
    } else {
      score += 15 // 默认分数
    }

    // 用户代理评分 (0-10分)
    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.includes('mobile') || userAgent.includes('android')) {
      score += 5 // 移动设备通常性能较低
    } else {
      score += 10
    }

    deviceInfo.value.performanceScore = score
    deviceInfo.value.isLowEnd = score < 50

    return score
  }

  /**
   * 根据设备性能生成优化配置
   */
  const generateOptimizationConfig = () => {
    const score = deviceInfo.value.performanceScore

    if (score < 30) {
      // 极低端设备 - 更激进的优化
      optimizationConfig.value = {
        enableAnimation: false,
        renderQuality: 'low',
        cacheSize: 20,
        debounceDelay: 800,
        chunkSize: 10,
        enableVirtualScroll: true,
        enableWebWorker: false,
        maxNodes: 50,
        // 新增的激进优化配置
        enableLazyRender: true,
        maxRenderTime: 16, // 每帧最大渲染时间 16ms
        enableDataThrottling: true,
        dataThrottleDelay: 100
      }
    } else if (score < 50) {
      // 低端设备 - 加强优化
      optimizationConfig.value = {
        enableAnimation: false,
        renderQuality: 'low',
        cacheSize: 100,
        debounceDelay: 500,
        chunkSize: 25,
        enableVirtualScroll: true,
        enableWebWorker: true,
        maxNodes: 150,
        // 新增的优化配置
        enableLazyRender: true,
        maxRenderTime: 32,
        enableDataThrottling: true,
        dataThrottleDelay: 50
      }
    } else if (score < 70) {
      // 中端设备
      optimizationConfig.value = {
        enableAnimation: true,
        renderQuality: 'medium',
        cacheSize: 500,
        debounceDelay: 200,
        chunkSize: 100,
        enableVirtualScroll: false,
        enableWebWorker: true,
        maxNodes: 500
      }
    } else {
      // 高端设备
      optimizationConfig.value = {
        enableAnimation: true,
        renderQuality: 'high',
        cacheSize: 1000,
        debounceDelay: 100,
        chunkSize: 200,
        enableVirtualScroll: false,
        enableWebWorker: true,
        maxNodes: 1000
      }
    }
  }

  /**
   * 获取思维导图配置
   */
  const getMindMapConfig = computed(() => {
    const config = optimizationConfig.value

    return {
      // 基础配置
      disableMouseWheelZoom: true,
      editable: false,
      isUseCustomNodeContent: true,
      enableFreeDrag: false,

      // 性能相关配置
      enableAnimation: config.enableAnimation,
      renderQuality: config.renderQuality,

      // 根据设备性能调整的配置
      expandBtnSize: config.renderQuality === 'low' ? 20 : 24,

      // 主题配置
      themeConfig: {
        lineWidth: config.renderQuality === 'low' ? 1 : 2,
        lineColor: '#a6a6a6',
        lineDasharray: 'none',
        lineStyle: 'straight',
        borderWidth: 0,
        root: {
          hoverRectColor: '#fafafa'
        }
      }
    }
  })

  /**
   * 监控内存使用
   */
  const monitorMemoryUsage = () => {
    if (!performance.memory) return null

    const current = {
      used: Math.round(performance.memory.usedJSHeapSize / (1024 * 1024)),
      total: Math.round(performance.memory.totalJSHeapSize / (1024 * 1024)),
      limit: Math.round(performance.memory.jsHeapSizeLimit / (1024 * 1024))
    }

    // 内存使用率超过80%时发出警告
    const usageRate = current.used / current.limit
    if (usageRate > 0.8) {
      console.warn(`内存使用率过高: ${(usageRate * 100).toFixed(1)}%`)
      return { ...current, warning: true }
    }

    return { ...current, warning: false }
  }

  /**
   * 获取性能建议
   */
  const getPerformanceRecommendations = computed(() => {
    const recommendations = []
    const score = deviceInfo.value.performanceScore

    if (score < 30) {
      recommendations.push('建议关闭动画效果以提升性能')
      recommendations.push('建议限制显示的节点数量')
      recommendations.push('建议使用虚拟滚动')
    } else if (score < 50) {
      recommendations.push('建议减少缓存大小')
      recommendations.push('建议增加防抖延迟')
    } else if (score < 70) {
      recommendations.push('当前设备性能中等，可适当优化')
    } else {
      recommendations.push('设备性能良好，可使用完整功能')
    }

    return recommendations
  })

  /**
   * 立即初始化设备检测（不等待 onMounted）
   */
  detectDevicePerformance()
  generateOptimizationConfig()

  /**
   * 组件挂载时输出调试信息
   */
  onMounted(() => {
    console.log('设备性能评分:', deviceInfo.value.performanceScore)
    console.log('优化配置:', optimizationConfig.value)
    console.log('性能建议:', getPerformanceRecommendations.value)
  })

  return {
    deviceInfo,
    optimizationConfig,
    getMindMapConfig,
    getPerformanceRecommendations,
    detectDevicePerformance,
    generateOptimizationConfig,
    monitorMemoryUsage
  }
}

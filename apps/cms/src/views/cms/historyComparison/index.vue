<template>
	<!-- 历史比对 -->
	<div class="h-full comparison">
		<a-form ref="formRef" :rules="formRules">
			<a-row :gutter="24">
				<a-col>
					<a-form-item label="基准版本-期间" name="baseVersionDate">
						<a-date-picker :disabled-date="disabledDat1" v-model:value="baseVersionDate" picker="month" />
					</a-form-item>
				</a-col>
				<a-col>
					<a-form-item label="查询范围" name="searchTree">
						<TreeSelect
							style="width: 280px"
							v-model:value="searchTree"
							:tree-data="selectTreeOrg"
							:field-names="selectTreeOrgFieldNames"
							searchKey="name"
						/>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="24">
				<a-col>
					<a-form-item label="对比版本-期间" name="compareVersionDate">
						<a-date-picker v-model:value="compareVersionDate" :disabled-date="disabledDate" picker="month" />
					</a-form-item>
				</a-col>
				<a-col>
					<a-form-item label="">
						<a-button
							:type="compareVersionDate ? 'default' : 'primary'"
							@click="compareVersionDate = ''"
							v-if="curVersionDate"
							>实时版本：{{ curVersionDate }}</a-button
						>
					</a-form-item>
				</a-col>
				<a-col>
					<a-form-item label="">
						<a-button type="primary" :loading="iconLoading" @click="historyCompare">比较</a-button>
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="24">
				<a-col>
					<a-form-item label="企业名称" name="searchName">
						<a-input @change="searchInput" v-model:value="searchName" allowClear placeholder="请输入企业名称" />
					</a-form-item>
				</a-col>
				<a-col>
					<a-form-item label="变更类型" name="searchValue">
						<a-select
							style="min-width: 150px"
							mode="multiple"
							@change="onChange"
							v-model:value="searchValue"
							:options="searchOptions"
							placeholder="请选择变更类型"
						></a-select>
					</a-form-item>
				</a-col>
				<a-col>
					<a-form-item label="">
						<a-button @click="searchData" type="primary">筛选</a-button>
					</a-form-item>
				</a-col>
				<a-col>
					<a-form-item label="">
						<qt-button @click="(next) => exportInfo(next)" type="primary">导出</qt-button>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<!-- :pagination="pagination" -->
		<a-table
			:columns="columns"
			position
			:data-source="tableData"
			size="small"
			:pagination="false"
			:loading="loading"
			:row-key="(record) => record.id"
		>
			<template #bodyCell="{ column, record }">
				<template
					v-if="
						column.dataIndex != 'id' &&
						column.dataIndex != 'simpOrgName' &&
						column.dataIndex != 'topInternalShareholder' &&
						column.dataIndex != 'managementEntity'
					"
				>
					<div
						@click="openModal(column.dataIndex, record)"
						v-if="
							(column.dataIndex == 'managementLevelChange' && record?.managementLevelChange == '1') ||
							(column.dataIndex == 'legalLevelChange' && record?.legalLevelChange == '1') ||
							(column.dataIndex == 'attentionChange' && record?.attentionChange == '1') ||
							(column.dataIndex == 'businessInfoChange' && record?.businessInfoChange == '1') ||
							(column.dataIndex == 'shareholderChange' && record?.shareholderChange == '1') ||
							(column.dataIndex == 'industryChange' && record?.industryChange == '1') ||
							(column.dataIndex == 'newEntity' && record?.newEntity == '1') ||
							(column.dataIndex == 'disposed' && record?.disposed == '1') ||
							(column.dataIndex == 'managementFunctionChange' && record?.managementFunctionChange == '1')
						"
					>
						<img style="width: 20px; height: 20px" src="@/assets/images/cms/home/<USER>" alt="" />
					</div>
					<div v-else></div>
				</template>
			</template>
		</a-table>
		<a-pagination
			style="float: right; margin-top: 10px"
			v-if="historyCompareList"
			v-model:current="currentNumber"
			v-model:page-size="pageSize"
			:total="total"
			showSizeChanger
			@change="paginationChange"
		/>
		<a-modal width="80%" v-model:open="open" :title="activeColTitle" @ok="handleOk">
			<Collapse
				:title="conditionTitle1"
				v-if="
					activeColName == 'businessInfoChange' ||
					activeColName == 'shareholderChange' ||
					activeColName == 'industryChange'
				"
			>
				<template #content>
					<a-table
						class="ml-6"
						:columns="modalColumns"
						:data-source="condition1"
						:pagination="false"
						size="small"
						:row-key="(record) => record.id"
					>
					</a-table>
				</template>
			</Collapse>
			<div v-else-if="activeColName == 'attentionChange'">
				<div v-for="(item, i) in compFieldChangeList" :key="i">
					<Collapse :title="item.title">
						<template #content>
							<a-table
								class="ml-6"
								:columns="modalColumns"
								:data-source="item.list"
								:pagination="false"
								size="small"
								:row-key="(record) => record.id"
							>
							</a-table>
						</template>
					</Collapse>
				</div>
			</div>
			<a-table
				v-else
				class="ml-6"
				:columns="modalColumns"
				:data-source="condition1"
				:pagination="false"
				size="small"
				:row-key="(record) => record.id"
			>
			</a-table>
			<Collapse :title="conditionTitle2" v-if="activeColName == 'businessInfoChange'">
				<template #content>
					<a-table
						class="ml-6"
						:columns="modalColumns2"
						:data-source="condition2"
						:pagination="false"
						size="small"
						:row-key="(record) => record.id"
					>
					</a-table>
				</template>
			</Collapse>
			<Collapse
				:title="conditionTitle3"
				v-if="activeColName == 'shareholderChange' || activeColName == 'industryChange'"
			>
				<template #content>
					<a-table
						class="ml-6"
						:columns="
							activeColName == 'shareholderChange'
								? modalColumns3
								: activeColName == 'industryChange'
								  ? modalColumns4
								  : {}
						"
						:data-source="condition3"
						:pagination="false"
						size="small"
						:row-key="(record) => record.id"
					>
					</a-table>
				</template>
			</Collapse>
		</a-modal>
		<History ref="historyRef" />
	</div>
</template>
<script setup name="historyComparison" lang="jsx">
	import { message } from 'ant-design-vue'
	import History from './history.vue'
	import dayjs from 'dayjs'
	import tool from '@/utils/tool'
	import { required, rules } from '@/utils/formRules'
	import downloadUtil from '@/utils/downloadUtil.js'
	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	import { useOrgTreeStore } from '@/store'
	const OrgTreeStore = useOrgTreeStore()
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})

	const dynamicImport = (icon) => {
		const href = new URL(`../../../assets/images/cms/home/<USER>
		return href
	}
	const relationship = tool.dictList('relationship')
	const loading = ref()
	const iconLoading = ref()
	const exportLoading = ref()
	const historyRef = ref()
	const baseVersionDate = ref()
	const compareVersionDate = ref()
	const treeAttr = reactive({
		keys: 'id',
		title: 'simpOrgName',
		loading: true,
		selectLeastOne: true,
		selected: [], // 默认选中
		expanded: [], // 默认展开
		checked: [] // 默认勾选
	})
	const versionDate = ref()
	const searchTree = ref()
	const selectTreeOrg = computed(() => {
		if (OrgTreeStore.selectTreeOrg.length) {
			return OrgTreeStore.selectTreeOrg
		} else {
			bizInstitutionApi.getStructureOrgnList({ isWhereFilling: '0' }).then((res) => {
				OrgTreeStore.setSelectTreeOrg(res)
				return res || []
			})
		}
	})
	const selectTreeOrgFieldNames = ref({
		children: 'children',
		label: 'simpOrgName',
		value: 'id'
	})
	const searchOptions = ref([
		// { label: '所有', value: '0' },
		{ label: '法人层级', value: '1' },
		{ label: '管理层级', value: '2' },
		{ label: '关注字段变更', value: '3' },
		{ label: '工商信息', value: '4' },
		{ label: '股权关系', value: '5' },
		{ label: '管理职能', value: '6' },
		{ label: '行业', value: '7' },
		{ label: '新建', value: '8' },
		{ label: '处置', value: '9' }
	])
	// 表格
	const tableData = ref([])
	const treeData = ref([])
	const total = ref()
	const historyCompareList = ref([])
	const getTreeData = () => {
		// 处理树是否可编辑
		const disposeTree = async (tree) => {
			for (let i = 0; i < tree.length; i++) {
				tree[i].disabled = tree[i]?.isClickable == 1 ? false : true
				if (tree[i]?.children) {
					disposeTree(tree[i]?.children)
				}
			}
		}
		treeAttr.loading = true
		bizInstitutionApi
			.getStructureTree({ structureType: 0 })
			.then(async (res) => {
				if (res?.length) {
					await disposeTree(res)
					treeData.value = res
					treeAttr.expanded.push(res[0]?.id)
				}
			})
			.finally(() => {
				treeAttr.loading = false
			})
	}
	getTreeData()
	const searchName = ref('')
	const searchValue = ref([])
	const entityCodeList = ref([])
	const searchData = () => {
		// debugger
		// if (!tableData.value || tableData.value.length == 0) {
		// 	return
		// }
		loading.value = true
		if (!searchName.value && (!searchValue._rawValue || searchValue._rawValue.length == 0)) {
			tableData.value = historyCompareList.value.slice(0, pageNumber.value)
			total.value = historyCompareList.value.length
			loading.value = false
			return
		}
		currentNumber.value = 1
		let list = []
		entityCodeList.value = []
		if (searchName.value || (searchValue._rawValue && searchValue._rawValue.length > 0)) {
			historyCompareList.value.map((item) => {
				if (searchName.value && searchValue._rawValue && searchValue._rawValue.length > 0) {
					let index = item.simpOrgName?.indexOf?.(searchName.value) ?? -1
					let isSearch = false
					searchValue._rawValue.map((i) => {
						if (
							(i == '1' && item.legalLevelChange == '1') ||
							(i == '2' && item.managementLevelChange == '1') ||
							(i == '3' && item.attentionChange == '1') ||
							(i == '4' && item.businessInfoChange == '1') ||
							(i == '5' && item.shareholderChange == '1') ||
							(i == '6' && item.managementFunctionChange == '1') ||
							(i == '7' && item.industryChange == '1') ||
							(i == '8' && item.newEntity == '1') ||
							(i == '9' && item.disposed == '1')
						) {
							isSearch = true
						}
					})
					if (index != -1 && isSearch) {
						let hasIndex = list.findIndex((obj) => obj.entityCode == item.entityCode)
						if (hasIndex == -1) {
							list.push(item)
							entityCodeList.value.push(item.entityCode)
						}
					}
				} else if (searchName.value) {
					let index = item.simpOrgName?.indexOf?.(searchName.value) ?? -1
					if (index != -1) {
						list.push(item)
						entityCodeList.value.push(item.entityCode)
					}
				} else if (searchValue._rawValue) {
					searchValue._rawValue.map((i) => {
						if (
							(i == '1' && item.legalLevelChange == '1') ||
							(i == '2' && item.managementLevelChange == '1') ||
							(i == '3' && item.attentionChange == '1') ||
							(i == '4' && item.businessInfoChange == '1') ||
							(i == '5' && item.shareholderChange == '1') ||
							(i == '6' && item.managementFunctionChange == '1') ||
							(i == '7' && item.industryChange == '1') ||
							(i == '8' && item.newEntity == '1') ||
							(i == '9' && item.disposed == '1')
						) {
							let hasIndex = list.findIndex((obj) => obj.entityCode == item.entityCode)
							if (hasIndex == -1) {
								list.push(item)
								entityCodeList.value.push(item.entityCode)
							}
						}
					})
				} else {
					list = historyCompareList.value
				}
			})
			tableData.value = list.slice(0, pageNumber.value)
			total.value = list.length
			loading.value = false
		} else {
			tableData.value = historyCompareList.value.slice(0, pageNumber.value)
			total.value = historyCompareList.value.length
			loading.value = false
		}
	}
	// const onChange = (data) => {
	// 	searchValue.value = data
	// }
	const currentNumber = ref(1)
	const pageSize = ref(10)
	const pageNumber = ref(10)
	const baseVersionId = ref()
	const compareVersionId = ref()
	const historyCompare = () => {
		searchName.value = ''
		searchValue.value = []
		entityCodeList.value = []
		historyCompareList.value = []
		tableData.value = []
		loading.value = true
		iconLoading.value = true
		let date = compareVersionDate.value && compareVersionDate.value != '' ? compareVersionDate.value : versionDate.value
		bizInstitutionApi
			.historyCompare({
				baseVersionDate: dayjs(baseVersionDate.value).format('YYYY-MM'),
				compareVersionDate: dayjs(date).format('YYYY-MM'),
				entityId: searchTree.value
			})
			.then(async (res) => {
				if (res && res.list) {
					baseVersionId.value = res.baseVersionId
					compareVersionId.value = res.compareVersionId
					historyCompareList.value = res.list
					total.value = res.list.length
					currentNumber.value = 1
					pageNumber.value = 10
					pageSize.value = 10
					tableData.value = historyCompareList.value.slice(0, pageNumber.value)
					loading.value = false
					iconLoading.value = false
				}
			})
			.catch(() => {
				loading.value = false
				iconLoading.value = false
			})
	}
	const exportInfo = (next) => {
		if (!tableData.value || tableData.value.length == 0) {
			next()
			return
		}
		// exportLoading.value = true
		let date = compareVersionDate.value && compareVersionDate.value != '' ? compareVersionDate.value : versionDate.value
		bizInstitutionApi
			.historyCompareExport({
				baseVersionDate: dayjs(baseVersionDate.value).format('YYYY-MM'),
				compareVersionDate: dayjs(date).format('YYYY-MM'),
				entityId: searchTree.value,
				entityCodes: entityCodeList._rawValue ? entityCodeList._rawValue.join(',') : ''
			})
			.then((res) => {
				downloadUtil.resultDownload(res)
				// exportLoading.value = false
				next()
			})
			.catch(() => {
				// exportLoading.value = false
				next()
			})
	}

	// pageSize改变回调分页事件
	const paginationChange = (page, pageSize) => {
		currentNumber.value = page
		pageNumber.value = pageSize
		if (page == 1) {
			tableData.value = historyCompareList.value.slice(0 * page, pageSize)
		} else {
			tableData.value = historyCompareList.value.slice(pageSize * (page - 1), pageSize * page)
		}
	}
	const open = ref(false)
	const activeColName = ref()
	const activeColTitle = ref()
	const handleOk = (e) => {
		console.log(e)
		open.value = false
	}

	const openModal = (name, record) => {
		compFieldChangeList.value = []
		condition1.value = []
		condition2.value = []
		condition3.value = []
		console.log(record, '====record')
		activeColName.value = name
		if (name == 'legalLevelChange') {
			// 法人层级
			activeColTitle.value = '法人层级'
			getCompLegalLevel(record)
		} else if (name == 'managementLevelChange') {
			// 管理层级
			activeColTitle.value = '管理层级'
			getCompManagementLevel(record)
		} else if (name == 'attentionChange') {
			// 关注字段
			activeColTitle.value = '关注字段变更'
			getCompFieldChange(record)
		} else if (name == 'businessInfoChange') {
			// 工商信息
			activeColTitle.value = '工商信息'
			getCompBusiness(record)
		} else if (name == 'shareholderChange') {
			// 股权关系
			activeColTitle.value = '股权关系'
			getCompOrgRelation(record)
		} else if (name == 'managementFunctionChange') {
			// 管理职能
			activeColTitle.value = '管理职能'
			getCompManageFunction(record)
		} else if (name == 'industryChange') {
			// 行业
			activeColTitle.value = '行业'
			getEntityCategoryComp(record)
		}
		if (name == 'newEntity' || name == 'disposed') {
			// 新建 ｜｜ 处置
			if (record?.status == 2) return message.warning('该机构已移除！')
			let date = compareVersionDate.value || versionDate.value
			historyRef.value.onOpen(record.entityId, record.versionId, dayjs(date).format('YYYY-MM'))
		} else {
			open.value = true
		}
	}
	const compFieldChangeList = ref()
	const condition1 = ref()
	const condition2 = ref()
	const condition3 = ref()
	const conditionTitle1 = ref()
	const conditionTitle2 = ref()
	const conditionTitle3 = ref()
	const getCompLegalLevel = (record) => {
		bizInstitutionApi
			.getCompLegalLevel({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					condition1.value = res
				}
			})
	}
	const getCompManagementLevel = (record) => {
		bizInstitutionApi
			.getCompManagementLevel({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					condition1.value = res
				}
			})
	}
	const getCompFieldChange = (record) => {
		bizInstitutionApi
			.getCompFieldChange({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					let list = res
					let newList = [
						{
							title: '机构性质',
							list: []
						},
						{
							title: '亏损治理',
							list: []
						},
						{
							title: '重点关注的参股公司',
							list: []
						}
					]
					list.map((item) => {
						if (
							item.fieldName == '是否实质开展经营' ||
							item.fieldName == '是否为SPV公司' ||
							item.fieldName == '是否其他不实质开展经营公司' ||
							item.fieldName == '不实质开展经营机构的存续目的' ||
							item.fieldName == '是否为中信集团控股公司' ||
							item.fieldName == '是否为金融租赁项目公司'
						) {
							newList[0].list.push(item)
						}
						if (item.fieldName == '是否亏损' || item.fieldName == '亏损类型') {
							newList[1].list.push(item)
						}
						if (
							item.fieldName == '是否为【持股比例高于50%但不并表的机构】' ||
							item.fieldName == '是否使用中信品牌' ||
							item.fieldName == '中信方是否为第一大股东'
						) {
							newList[2].list.push(item)
						}
					})
					compFieldChangeList.value = newList
				}
			})
	}
	const getCompBusiness = (record) => {
		bizInstitutionApi
			.getCompBusiness({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					condition1.value = res.businessList || []
					condition2.value = res.managerList || []
					conditionTitle1.value = '基本信息'
					conditionTitle2.value = '董监高信息'
				}
			})
	}
	const getCompOrgRelation = (record) => {
		bizInstitutionApi
			.getCompOrgRelation({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					condition1.value = res.businessList || []
					condition3.value = res.orgRelationList || []
					conditionTitle1.value = '基本信息'
					conditionTitle2.value = ''
					conditionTitle3.value = '内部股东信息'
				}
			})
	}
	const getCompManageFunction = (record) => {
		bizInstitutionApi
			.getCompManageFunction({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					condition1.value = res.baseList || []
				}
			})
	}
	// 行业todo
	const getEntityCategoryComp = (record) => {
		bizInstitutionApi
			.getEntityCategoryComp({
				baseVersionId: baseVersionId.value,
				compareVersionId: record.versionId,
				entityCode: record.entityCode
			})
			.then(async (res) => {
				if (res) {
					condition1.value = res.belongCategoryList || []
					condition3.value = res.entityCategoryList || []
					conditionTitle1.value = '基本信息'
					conditionTitle3.value = '行业信息'
				}
			})
	}
	// const formRules = ref({
	// 	baseVersionDate: [required('请选择基准版本-期间'), rules.baseVersionDate],
	// 	searchTree: [required('请选择查询范围'), rules.searchTree]
	// })

	const columns = computed(() => {
		return [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '企业全称',
				dataIndex: 'simpOrgName',
				ellipsis: true
			},
			{
				title: '持股比例最高的内部股东',
				dataIndex: 'topInternalShareholder',
				ellipsis: true
			},
			{
				title: '所属管理主体',
				dataIndex: 'managementEntity',
				ellipsis: true
			},
			{
				title: '法人层级',
				dataIndex: 'legalLevelChange'
			},
			{
				title: '管理层级',
				dataIndex: 'managementLevelChange'
			},
			{
				title: '关注字段变更',
				dataIndex: 'attentionChange'
			},
			{
				title: '工商信息',
				dataIndex: 'businessInfoChange'
			},
			{
				title: '股权关系',
				dataIndex: 'shareholderChange'
			},
			{
				title: '管理职能',
				dataIndex: 'managementFunctionChange'
			},
			{
				title: '行业',
				dataIndex: 'industryChange'
			},
			{
				title: '新建',
				dataIndex: 'newEntity'
			},
			{
				title: '处置',
				dataIndex: 'disposed'
			}
		]
	})
	const modalColumns = computed(() => {
		return [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '变更字段',
				dataIndex: 'fieldName',
				ellipsis: true
			},
			{
				title: '变更前',
				dataIndex: 'baseField',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let content = text
					if (text == 'Y') {
						content = '是'
					} else if (text == 'N') {
						content = '否'
					}
					return content
				}
			},
			{
				title: '变更后',
				dataIndex: 'contrastField',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let content = text
					if (text == 'Y') {
						content = '是'
					} else if (text == 'N') {
						content = '否'
					}
					return content
				}
			}
		]
	})
	const modalColumns2 = computed(() => {
		return [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '董监高姓名',
				dataIndex: 'name',
				ellipsis: true
			},
			{
				title: '变更前',
				dataIndex: 'baseField',
				ellipsis: true
			},
			{
				title: '变更后',
				dataIndex: 'contrastField',
				ellipsis: true
			},
			{
				title: '变更类型',
				dataIndex: 'type',
				ellipsis: true
			}
		]
	})
	const modalColumns3 = computed(() => {
		return [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '内部股东企业全称',
				dataIndex: 'baseOrgName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contOrgName
					} else if (record.type == '删除') {
						data = record.baseOrgName
					} else {
						data =
							record.baseOrgName == record.contOrgName
								? record.baseOrgName
								: record.baseOrgName + ' -> ' + record.contOrgName
					}
					return data
				}
			},
			{
				title: '与内部股东的财务关系',
				dataIndex: 'baseRelationship',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					// let baseRelationship = relationship.find((item) => item.value === record.baseRelationship)?.label
					// let contRelationship = relationship.find((item) => item.value === record.contRelationship)?.label
					let baseRelationship = record.baseRelationship
					let contRelationship = record.contRelationship
					let data = ''
					if (record.type == '新增') {
						data = contRelationship
					} else if (record.type == '删除') {
						data = baseRelationship
					} else {
						data =
							baseRelationship == contRelationship ? baseRelationship : baseRelationship + ' -> ' + contRelationship
					}
					return data
				}
			},
			{
				title: '内部股东持股比例（%）',
				dataIndex: 'baseShareRate',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let baseShareRate = record.baseShareRate ? parseFloat((record.baseShareRate * 100).toPrecision(12)) : 0
					let contShareRate = record.contShareRate ? parseFloat((record.contShareRate * 100).toPrecision(12)) : 0
					let src = baseShareRate > contShareRate ? dynamicImport('icon-down.png') : dynamicImport('icon-up.png')
					let style = baseShareRate > contShareRate ? 'color:#26c030' : 'color:#d6000f'
					let data = ''
					if (record.type == '新增') {
						data = contShareRate ? contShareRate + '%' : ''
					} else if (record.type == '删除') {
						data = baseShareRate ? baseShareRate + '%' : ''
					} else {
						data =
							baseShareRate == contShareRate ? (
								<div>{baseShareRate + '%'}</div>
							) : (
								<div>
									{baseShareRate + '%' + ' -> '}
									<span style={style}>{contShareRate + '%'}</span>
									<span>
										<img style="width: 15px; height:15px" src={src} />
									</span>
								</div>
							)
					}
					return data
				}
			},
			{
				title: '是否持股比例最高的内部股东',
				dataIndex: 'baseIsHight',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contIsHight
					} else if (record.type == '删除') {
						data = record.baseIsHight
					} else {
						data =
							record.baseIsHight == record.contIsHight
								? record.baseIsHight
								: record.baseIsHight + ' -> ' + record.contIsHight
					}
					return data
				}
			},
			{
				title: '变更类型',
				dataIndex: 'type',
				ellipsis: true
			}
		]
	})
	const modalColumns4 = computed(() => {
		return [
			{
				title: '序号',
				dataIndex: 'id',
				ellipsis: true,
				width: 80,
				customRender: ({ text, record, index }) => {
					return index + 1
				}
			},
			{
				title: '行业门类',
				dataIndex: 'baseCategName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contCategName
					} else if (record.type == '删除') {
						data = record.baseCategName
					} else {
						data =
							record.baseCategName == record.contCategName
								? record.baseCategName
								: record.baseCategName + ' -> ' + record.contCategName
					}
					return data
				}
			},
			{
				title: '行业大类',
				dataIndex: 'baseLargeName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contLargeName
					} else if (record.type == '删除') {
						data = record.baseLargeName
					} else {
						data =
							record.baseLargeName == record.contLargeName
								? record.baseLargeName
								: record.baseLargeName + ' -> ' + record.contLargeName
					}
					return data
				}
			},
			{
				title: '行业中类',
				dataIndex: 'baseMiddleName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contMiddleName
					} else if (record.type == '删除') {
						data = record.baseMiddleName
					} else {
						data =
							record.baseMiddleName == record.contMiddleName
								? record.baseMiddleName
								: record.baseMiddleName + ' -> ' + record.contMiddleName
					}
					return data
				}
			},
			{
				title: '行业小类',
				dataIndex: 'baseCodeName',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let data = ''
					if (record.type == '新增') {
						data = record.contCodeName
					} else if (record.type == '删除') {
						data = record.baseCodeName
					} else {
						data =
							record.baseCodeName == record.contCodeName
								? record.baseCodeName
								: record.baseCodeName + ' -> ' + record.contCodeName
					}
					return data
				}
			},
			{
				title: '最近一年经审计后的营业收入',
				dataIndex: 'baseOperating',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let baseOperating = record.baseOperating
					let contOperating = record.contOperating
					let src = baseOperating > contOperating ? dynamicImport('icon-down.png') : dynamicImport('icon-up.png')
					let style = baseOperating > contOperating ? 'color:#26c030' : 'color:#d6000f'
					let data = ''
					if (record.type == '新增') {
						data = contOperating
					} else if (record.type == '删除') {
						data = baseOperating
					} else {
						data =
							baseOperating == contOperating ? (
								<div>{baseOperating || ''}</div>
							) : (
								<div>
									{(baseOperating || '') + ' -> '}
									<span style={style}>{contOperating || ''}</span>
									<span>
										<img style="width: 15px; height:15px" src={src} />
									</span>
								</div>
							)
					}
					return data
				}
			},
			{
				title: '营收占比（%）',
				dataIndex: 'baseRevenue',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					let baseRevenue = record.baseRevenue
					let contRevenue = record.contRevenue
					let src = baseRevenue > contRevenue ? dynamicImport('icon-down.png') : dynamicImport('icon-up.png')
					let style = baseRevenue > contRevenue ? 'color:#26c030' : 'color:#d6000f'
					let data = ''
					if (record.type == '新增') {
						data = contRevenue + '%'
					} else if (record.type == '删除') {
						data = baseRevenue + '%'
					} else {
						data =
							baseRevenue == contRevenue ? (
								<div>{baseRevenue ? baseRevenue + '%' : ''}</div>
							) : (
								<div>
									{(baseRevenue ? baseRevenue + '%' : '') + ' -> '}
									<span style={style}>{contRevenue ? contRevenue + '%' : ''}</span>
									<span>
										<img style="width: 15px; height:15px" src={src} />
									</span>
								</div>
							)
					}
					return data
				}
			},
			{
				title: '变更类型',
				dataIndex: 'type',
				ellipsis: true
			}
		]
	})
	const curVersionDate = ref('')
	const disabledDate = (current) => {
		const nextMonthDate = dayjs(baseVersionDate.value).add(1, 'month')
		return current && (current > dayjs(versionDate.value) || current < nextMonthDate)
	}
	const disabledDat1 = (current) => {
		let date = compareVersionDate.value || versionDate.value
		return current && current > dayjs(date)
	}
	onMounted(async () => {
		bizInstitutionApi.getCurVersionDate().then((res) => {
			curVersionDate.value = res ? `${res?.year}年${res?.period}月` : ''
			versionDate.value = res ? `${res?.year}-${res?.period}` : ''
		})
	})
</script>
<style lang="less" scoped>
	.comparison {
		background: #fff;
		padding: 20px;
		overflow-y: auto;
		border-radius: 8px;
		.title {
			font-size: 16px;
			font-weight: bold;
			margin-bottom: 20px;
		}
		.info-item {
			display: inline-block;
			vertical-align: top;
			margin-bottom: 10px;
			.label {
				color: #aaa;
				font-size: 14px;
			}
			.value {
				color: #555;
				font-size: 16px;
				font-weight: bold;
			}
		}
	}
</style>

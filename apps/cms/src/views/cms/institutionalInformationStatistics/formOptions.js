import tool from '@/utils/tool'

export const fillingOptions = [{ label: '全部', value: '', children: tool.dictList('filling_standards') }]
export const legalOptions = [{ label: '全部', value: '', children: [{ label: '不适用', value: 'K' }, ...tool.dictList('legal_entity_level').map(d => ({ label: d.label + '级', value: d.value }))] }]
export const managementOptions = [{ label: '全部', value: '', children: [{ label: '不适用', value: 'K' }, ...tool.dictList('management_level').map(d => ({ label: d.label + '级', value: d.value }))] }]
export const sectorOptions = [{ label: '全部', value: '', children: [{ label: '不适用', value: 'K' }, ...tool.dictList('sector')].filter(d => d.value !== 'sector_7') }]
export const industryOptions = [{ label: '全部', value: '', children: tool.dictList('industry_categ') }]
export const lossOptions = [{ label: '全部', value: '', children: tool.dictList('loss_type') }]
export const thepurOptions = tool.dictList('thepur_exist')
export const isothersubherOptions = tool.dictList('isother_subher')
export const financialcompanyOptions = tool.dictList('financial_company')
export const yesorNoApplicable = [{ label: '全部', value: '', children: [{ label: '是', value: 'Y' }, { label: '否', value: 'N' }] }]
export const yesorno = [{ label: '全部', value: '', children: [{ label: '不适用', value: 'K' }, { label: '是', value: 'Y' }, { label: '否', value: 'N' }] }]
export const thepurExistOptions = [
  { label: '全部', value: '', children: [
    { label: 'SPV公司', value: 'thepur_exist', children: isothersubherOptions },
    { label: '其他不实质开展经营公司', value: 'isother_subher', children: thepurOptions },
    { label: '金融租赁项目公司', value: 'financial_company', children: financialcompanyOptions }
  ]}
]
export const options = [
  { label: '数量统计', value: 'institutionCount' },
  { label: '持股比例（中信方合计）', value: 'totalShareholdingRatio' }
]
export const mapKeyToField = {
  fillingStandards: '机构类别',
  legalEntityLevel: '法人层级',
  managementLevel: '管理层级',
  plate: '集团板块',
  industryCateg: '所属门类',
  isListed: '是否上市',
  hasBusiness: '是否实质开展经营',
  thepurExist: '不实质开展经营机构的存续目的',
  hasLoss: '是否亏损',
  lossType: '亏损类型',
  isitanInsti: '是否为持股比例高于50%但不并表的机构',
  isuseCiticb: '是否使用中信品牌',
	isciticLargshared: '中信方是否为第一大股东',
	// contributedCapitalFull: '是否实缴到位',
}
export const yesornoGroupKey = [
  'isitanInsti', 'isuseCiticb', 'isciticLargshared', 'hasLoss'
]
// export const yesorNoApplicableGroupKey = [
//   'isListed', 'hasBusiness', 'contributedCapitalFull'
// ]
export const yesorNoApplicableGroupKey = [
	'isListed', 'hasBusiness'
]
export const muiltSelectGroupKey = [
  'fillingStandards', 'legalEntityLevel', 'managementLevel', 'plate', 'thepurExist', 'lossType', 'industryCateg'
]

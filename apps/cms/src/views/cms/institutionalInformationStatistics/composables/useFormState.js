import { ref } from 'vue'

/**
 * 表单状态管理组合式函数
 */
export function useFormState() {
  // 初始表单状态
  const initialFormState = {
    mainFields: ['totalShareholdingRatio', 'institutionCount'],
    financeFields: ['netProfitTotal'],
    detailFields: [],
    industryCateg: []
  }

  const formState = ref({ ...initialFormState })

  /**
   * 重置表单状态
   */
  const resetFormState = () => {
    formState.value = { ...initialFormState }
  }

  return {
    formState,
    resetFormState
  }
}

import { ref } from 'vue'
import dayjs from 'dayjs'
import bizInstitutionApi from '@/api/biz/bizInstitutionApi'

/**
 * 版本日期管理组合式函数
 */
export function useVersionDate() {
  const versionDate = ref()
  const curVersionDate = ref('')

  /**
   * 禁用日期函数
   */
  const disabledDate = (current) => {
    const nextMonthDate = dayjs(versionDate.value) - 1
    return current && current > nextMonthDate
  }

  /**
   * 处理日期变化
   */
  const handleDateChange = (date, yearMonth, formState) => {
    if (date) {
      const [year, month] = date.split('-')
      formState.value.year = year
      formState.value.month = month
      console.log('日期变化:', yearMonth)
    } else {
      delete formState.value?.year
      delete formState.value?.month
      formState.value.isCurrentVersion = '1'
      console.log('清空日期:', yearMonth)
    }
  }

  /**
   * 获取当前版本日期
   */
  const fetchCurrentVersionDate = async () => {
    try {
      const res = await bizInstitutionApi.getCurVersionDate()
      curVersionDate.value = res ? `${res?.year}年${res?.period}月` : ''
      versionDate.value = res ? `${res?.year}-${res?.period}` : ''
    } catch (error) {
      console.error('获取版本日期失败:', error)
    }
  }

  return {
    versionDate,
    curVersionDate,
    disabledDate,
    handleDateChange,
    fetchCurrentVersionDate
  }
}

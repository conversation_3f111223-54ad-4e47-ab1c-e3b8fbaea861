import { cloneDeep, isNil, omit, uniq } from 'lodash-es'
import institutionalStatisticsApi from '@/api/biz/institutionalStatisticsApi'
import downloadUtil from '@/utils/downloadUtil'
import {
  fillingOptions, legalOptions, managementOptions, sectorOptions,
  yesorNoApplicable, yesorno, thepurExistOptions
} from '../formOptions'

/**
 * 数据查询管理组合式函数
 */
export function useDataQuery(formState, dataSource, loading) {

  /**
   * 处理查询参数 - 提取公共逻辑
   */
  const processQueryParams = (params) => {
    // 处理各种选择字段，如果选择了全部则设为 undefined
    params.fillingStandards = params?.fillingStandards?.length === fillingOptions[0].children.length
      ? undefined : params.fillingStandards
    params.legalEntityLevel = params?.legalEntityLevel?.length === legalOptions[0].children.length
      ? undefined : params.legalEntityLevel
    params.managementLevel = params?.managementLevel?.length === managementOptions[0].children.length
      ? undefined : params.managementLevel
    params.plate = params?.plate?.length === sectorOptions[0].children.length
      ? undefined : params.plate
    params.industryLarge = params?.industryLarge?.length === 98 ? [] : params.industryLarge
    params.isListed = params?.isListed?.length === yesorNoApplicable[0].children.length
      ? undefined : params.isListed
		// params.contributedCapitalFull = params?.contributedCapitalFull?.length === yesorNoApplicable[0].children.length
		// 	? undefined : params.contributedCapitalFull

    params.hasBusiness = params?.hasBusiness?.length === yesorNoApplicable[0].children.length
      ? undefined : params.hasBusiness

    // 计算 thepurExist 的总长度
    const thepurExistTotalLength = thepurExistOptions[0].children[0].children.length +
      thepurExistOptions[0].children[1].children.length +
      thepurExistOptions[0].children[2].children.length
    params.thepurExist = params?.thepurExist?.length === thepurExistTotalLength
      ? undefined : params.thepurExist

    params.hasLoss = params?.hasLoss?.length === yesorno[0].children.length
      ? undefined : params.hasLoss
    params.lossType = params?.lossType?.length === yesorno[0].children.length
      ? undefined : params.lossType
    params.isitanInsti = params?.isitanInsti?.length === yesorno[0].children.length
      ? undefined : params.isitanInsti
    params.isuseCiticb = params?.isuseCiticb?.length === yesorno[0].children.length
      ? undefined : params.isuseCiticb
    params.isCiticLargeShared = params?.isCiticLargeShared?.length === yesorno[0].children.length
      ? undefined : params.isCiticLargeShared

    // 处理版本参数
    if (isNil(params?.yearMonth)) {
      params.isCurrentVersion = '1'
    }

    return params
  }


  /**
   * 查询数据
   */
  const queryData = async () => {
    try {
      loading.value = true
      dataSource.value = []

      const params = processQueryParams(cloneDeep(formState.value))
      const queryParams = omit(params, ['detailFields', 'mainFields', 'financeFields', 'industryCateg'])

      const res = await institutionalStatisticsApi.queryManagementSubjects(queryParams)
      dataSource.value = res ?? []
    } catch (error) {
      console.error('查询数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 导出数据
   */
  const exportData = async (next) => {
    try {
      const params = processQueryParams(cloneDeep(formState.value))

      // 构建导出字段
      const mainExportKeys = [
        ...params.detailFields,
        ...params.mainFields,
        ...params.financeFields
      ]
      params.exportFields = uniq(mainExportKeys).map(d =>  d === 'isciticLargshared' ? 'isCiticLargeShared' : d)

      const exportParams = omit(params, ['detailFields', 'mainFields', 'financeFields', 'industryCateg'])
      exportParams.isExport = '1'

      const res = await institutionalStatisticsApi.exportqueryManagementSubjects(exportParams)
      downloadUtil.resultDownload(res)
    } catch (error) {
      console.error('导出数据失败:', error)
    } finally {
      next?.()
    }
  }

  return {
    queryData,
    exportData,
    processQueryParams
  }
}

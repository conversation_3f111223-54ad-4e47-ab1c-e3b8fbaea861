import { cloneDeep } from 'lodash-es'
import { mapKeyToField } from '../formOptions'

/**
 * 行操作管理组合式函数
 */
export function useRowActions(formState, formRef) {
  
  /**
   * 处理行点击事件
   */
  const handleRowClick = (record, index) => {
    console.log('点击了第', index, '行，数据为：', record)
    
    const param = cloneDeep(formState.value)
    const columns = param.detailFields.map(d => ({
      title: mapKeyToField[d],
      width: 330,
      dataIndex: d
    }))
    
    console.log('详情列配置:', columns)
    
    formRef.value?.onOpen({
      dataSource: record?.shEntityVoList ?? [], 
      columns, 
      versionId: record?.versionId
    })
  }

  return {
    handleRowClick
  }
}

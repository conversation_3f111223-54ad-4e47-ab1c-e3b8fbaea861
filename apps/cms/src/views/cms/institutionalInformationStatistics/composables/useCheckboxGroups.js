import { ref, computed } from 'vue'
import { mapKeyToField } from '../formOptions'

/**
 * 复选框组管理组合式函数
 */
export function useCheckboxGroups(formState) {
  // 详细字段复选框状态
  const indeterminateDetail = ref(false)
  const checkAllDetail = ref(false)
  
  // 财务字段复选框状态
  const indeterminateFinance = ref(false)
  const checkAllFinance = ref(true)

  // 详细字段选项
  const detailFieldsOptions = computed(() => {
    return Object.entries(mapKeyToField).map(([key, value]) => ({
      label: value,
      value: key
    }))
  })

  /**
   * 处理详细字段全选变化
   */
  const onCheckDetailAllChange = (e) => {
    e.preventDefault()
    const val = e.target.checked
    indeterminateDetail.value = false
    checkAllDetail.value = val
    
    if (val) {
      formState.value.detailFields = detailFieldsOptions.value.map(d => d.value)
    } else {
      formState.value.detailFields = []
    }
  }

  /**
   * 处理详细字段选择变化
   */
  const onChangeDetail = (checkList) => {
    indeterminateDetail.value = checkList.length > 0 && checkList.length < detailFieldsOptions.value.length
    checkAllDetail.value = checkList.length === detailFieldsOptions.value.length
  }

  /**
   * 处理财务字段全选变化
   */
  const onCheckFinanceAllChange = (e) => {
    e.preventDefault()
    const val = e.target.checked
    indeterminateFinance.value = false
    checkAllFinance.value = val
    
    if (val) {
      formState.value.financeFields = ['netProfitTotal']
    } else {
      delete formState.value.financeFields
    }
  }

  /**
   * 处理财务字段选择变化
   */
  const onChangeFinance = (checkList) => {
    indeterminateFinance.value = checkList.length === 0
    checkAllFinance.value = checkList.length === 1
  }

  return {
    indeterminateDetail,
    checkAllDetail,
    indeterminateFinance,
    checkAllFinance,
    detailFieldsOptions,
    onCheckDetailAllChange,
    onChangeDetail,
    onCheckFinanceAllChange,
    onChangeFinance
  }
}

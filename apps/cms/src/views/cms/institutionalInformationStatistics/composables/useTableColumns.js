import { ref, computed, watch } from 'vue'
import { cloneDeep, compact, isEmpty } from 'lodash-es'
import {
  fillingOptions, legalOptions, managementOptions, sectorOptions, industryOptions,
  lossOptions, thepurOptions, isothersubherOptions, financialcompanyOptions,
  yesorNoApplicable, yesorno, options, mapKeyToField,
  yesornoGroupKey, yesorNoApplicableGroupKey, muiltSelectGroupKey
} from '../formOptions'

/**
 * 表格列管理组合式函数
 */
export function useTableColumns(formState) {
  // 固定列配置
  const fixColumns = [
    {
      title: '序号',
      width: 70,
			align: 'center',
      customRender: ({ index }) => `${index + 1}`
    },
    {
      title: '机构唯一编码',
			align: 'center',
      dataIndex: 'entityCode'
    },
    {
      title: '所属管理主体',
			align: 'center',
      dataIndex: 'simpOrgName'
    }
  ]

  // 初始化列配置，包含固定列和默认的主要字段列
  const initialColumns = [
    ...fixColumns,
    {
      title: '持股比例（中信方合计）',
      width: 200,
			align: 'center',
      dataIndex: 'totalShareholdingRatio'
    },
    {
      title: '数量统计',
      width: 200,
			align: 'center',
      dataIndex: 'institutionCount'
    },
    {
      title: '净利润合计',
			align: 'center',
      dataIndex: 'netProfitTotal'
    }
  ]

  const columns = ref([...initialColumns])

  // 选择键到字典的映射
  const selectKeyToDict = {
    fillingStandards: fillingOptions[0].children,
    legalEntityLevel: legalOptions[0].children,
    managementLevel: managementOptions[0].children,
    plate: sectorOptions[0].children,
    industryCateg: industryOptions,
    thepurExist: [...thepurOptions, ...isothersubherOptions, ...financialcompanyOptions],
    lossType: lossOptions[0].children
  }

  /**
   * 根据ID查找数据项
   */
  const findId = (data, code, searchKey = 'value') => {
    for (const item of data) {
      if (item[searchKey] == code) return item
      if (item?.children?.length) {
        const found = findId(item.children, code, searchKey)
        if (found) return found
      }
    }
    return null
  }

  /**
   * 生成表格列配置
   */
  const generateColumns = (params) => {
    let mainTableColumns = []
    let detailTableColumns = []

    // 主要字段列
    if (params.mainFields?.length > 0) {
      mainTableColumns = params.mainFields.map(c => ({
        title: options.find(f => f.value === c)?.label,
        width: 200,
				align: 'center',
        dataIndex: c
      }))
    }

    // 财务字段列
    if (params.financeFields?.length > 0) {
      mainTableColumns.push({
        title: '净利润合计',
				align: 'center',
        dataIndex: 'netProfitTotal'
      })
    }

    // 详细字段列
    if (params.detailFields?.length > 0) {
      detailTableColumns = params.detailFields.map(key => {
        if (muiltSelectGroupKey.includes(key)) {
          if (key === 'industryCateg') {
            return {
              title: mapKeyToField[key],
              children: isEmpty(params.industryLarge)
                ? industryOptions[0].children.map(({ value, label }) => ({
                    title: label,
                    dataIndex: ['dimensionStats', key, value],
									  align: 'center',
                    width: 220
                  }))
                : params.industryLarge.map((code) => {
                    const dict = findId(selectKeyToDict[key], code)
                    return {
                      title: dict?.label,
											align: 'center',
                      dataIndex: ['dimensionStats', key, code]
                    }
                  })
            }
          }

          return {
            title: mapKeyToField[key],
            width: 220,
            children: params[key]
              ? params[key].map((code) => {
                  const dict = findId(selectKeyToDict[key], code)
                  return {
                    title: dict.label,
										align: 'center',
                    dataIndex: ['dimensionStats', key, code]
                  }
                })
              : selectKeyToDict[key].map(({ value, label }) => ({
                  title: label,
								  align: 'center',
                  dataIndex: ['dimensionStats', key, value]
                }))
          }
        }

        if (yesornoGroupKey.includes(key)) {
          const childCol = isEmpty(params[key]) || !params[key] ? ['Y', 'N', 'K'] : [params[key]]
          return {
            title: mapKeyToField[key],
            width: 200,
            children: childCol.map(d => ({
              title: yesorno[0].children.find(f => f.value === d)?.label,
							align: 'center',
              dataIndex: ['dimensionStats', key, d]
            }))
          }
        }

        if (yesorNoApplicableGroupKey.includes(key)) {
					console.log("=>(useTableColumns.js:156) params[key]", params[key], key);
          const childCol = isEmpty(params[key]) || !params[key] ? ['Y', 'N'] : params[key]
          return {
            title: mapKeyToField[key],
            width: 200,
            children: childCol.map(d => ({
              title: yesorNoApplicable[0].children.find(f => f.value === d)?.label,
							align: 'center',
              dataIndex: ['dimensionStats', key, d]
            }))
          }
        }
      })
    }

    return [...fixColumns, ...mainTableColumns, ...compact(detailTableColumns)]
  }


  // 监听表单状态变化，更新列配置
  watch(
    () => formState.value,
    (newFormState) => {
      try {
        const params = cloneDeep(newFormState)
        columns.value = generateColumns(params)
				console.log("=>(useTableColumns.js:178) columns.value", columns.value);
      } catch (e) {
        console.error('生成表格列配置时出错:', e)
      }
    },
    { deep: true }
  )

  return {
    columns,
    fixColumns,
    generateColumns
  }
}

<template>
  <div class="institutional-statistics bg-white px-[18px] py-[15px] overflow-auto">
    <a-form :model="formState" :rules="formRules" layout="vertical">
      <a-collapse v-model:activeKey="activeKey" expand-icon-position="end" ghost>
        <!-- 时间维度 -->
        <a-collapse-panel key="1" header="时间维度" :style="customStyle">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-item label="数据区间：" name="yearMonth">
                <a-date-picker
                  @change="handleDateChange"
                  v-model:value="formState.yearMonth"
                  value-format="YYYY-M"
                  picker="month"
                  class="w-full"
                  :disabled-date="disabledDate"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="实时版本：">
                <a-button
                  type="primary"
                  @click="handleCurrentVersionClick"
                  v-if="curVersionDate"
                >
                  {{ curVersionDate }}
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
        </a-collapse-panel>

        <!-- 机构维度筛选 -->
        <a-collapse-panel key="2" header="机构维度筛选" :style="customStyle">
          <InstitutionFilters v-model:form-state="formState" />
        </a-collapse-panel>

        <!-- 统计数据选择 -->
        <a-collapse-panel key="3" header="统计数据选择" :style="customStyle">
          <StatisticsDataSelector
            v-model:form-state="formState"
            :indeterminate-detail="indeterminateDetail"
            :check-all-detail="checkAllDetail"
            :indeterminate-finance="indeterminateFinance"
            :check-all-finance="checkAllFinance"
            :detail-fields-options="detailFieldsOptions"
            @check-detail-all-change="onCheckDetailAllChange"
            @change-detail="onChangeDetail"
            @check-finance-all-change="onCheckFinanceAllChange"
            @change-finance="onChangeFinance"
            @query="queryData"
            @reset="resetFormState"
            @export="exportData"
          />
        </a-collapse-panel>
      </a-collapse>
    </a-form>

    <!-- 数据表格 -->
    <DataTable
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      @row-click="handleRowClick"
    />

    <!-- 详情弹窗 -->
    <DetailForm ref="formRef" @successful="" />
  </div>
</template>

<script setup name="institutionalInformationStatistics">
import { ref, onMounted } from 'vue'

// 组件导入
import InstitutionFilters from './components/InstitutionFilters.vue'
import StatisticsDataSelector from './components/StatisticsDataSelector.vue'
import DataTable from './components/DataTable.vue'
import DetailForm from './form.vue'

// 组合式函数导入
import { useFormState } from './composables/useFormState'
import { useTableColumns } from './composables/useTableColumns'
import { useDataQuery } from './composables/useDataQuery'
import { useCheckboxGroups } from './composables/useCheckboxGroups'
import { useVersionDate } from './composables/useVersionDate'
import { useRowActions } from './composables/useRowActions'

// 响应式数据
const dataSource = ref([])
const formRef = ref()
const loading = ref(false)
const activeKey = ref(['1', '3'])

// 常量
const formRules = {}
const customStyle = 'background: #f7f7f7;border-radius: 4px;margin-bottom: 14px;border: 0;overflow: hidden'

// 使用组合式函数
const { formState, resetFormState } = useFormState()
const { columns } = useTableColumns(formState)
const { queryData, exportData } = useDataQuery(formState, dataSource, loading)
const {
  indeterminateDetail,
  checkAllDetail,
  indeterminateFinance,
  checkAllFinance,
  onCheckDetailAllChange,
  onChangeDetail,
  onCheckFinanceAllChange,
  onChangeFinance,
  detailFieldsOptions
} = useCheckboxGroups(formState)
const {
  curVersionDate,
  disabledDate,
  handleDateChange: _handleDateChange,
  fetchCurrentVersionDate
} = useVersionDate()
const { handleRowClick } = useRowActions(formState, formRef)

// 事件处理函数
const handleDateChange = (date, yearMonth) => {
  _handleDateChange(date, yearMonth, formState)
}

const handleCurrentVersionClick = () => {
  delete formState.value.yearMonth
  queryData()
}

// 生命周期
onMounted(async () => {
  await fetchCurrentVersionDate()
})
</script>

<style scoped>
[data-doc-theme='light'] .ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}

[data-doc-theme='dark'] .ant-table-striped :deep(.table-striped) td {
  background-color: rgb(29, 29, 29);
}
</style>

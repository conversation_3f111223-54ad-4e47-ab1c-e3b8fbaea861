<template>
  <a-table
    bordered
    size="small"
    :loading="loading"
    :columns="columns"
    :dataSource="dataSource"
    :scroll="{ x: 'max-content' }"
    :row-key="(record) => record.entityId"
    :row-class-name="getRowClassName"
    :customRow="customRow"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'simpOrgName'">
        <a-button type="link">{{ record.simpOrgName }}</a-button>
      </template>
    </template>
  </a-table>
</template>

<script setup>
// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  columns: {
    type: Array,
    default: () => []
  },
  dataSource: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['rowClick'])

// 方法
const getRowClassName = (record, index) => {
  return index % 2 === 1 ? 'table-striped' : null
}

const customRow = (record, index) => {
  return {
    onClick: (event) => {
      emit('rowClick', record, index)
    }
  }
}
</script>

<style scoped>

	.ant-table-wrapper :deep(.ant-table.ant-table-bordered >.ant-table-container >.ant-table-content >table >thead>tr>th){
		border: 0.5px solid #bcbcbc;
	}
	.ant-table-wrapper :deep(.ant-table.ant-table-bordered >.ant-table-container >.ant-table-content >table >thead >tr:not(:last-child)>th) {
		border-bottom: 0.5px solid #bcbcbc;
	}

	.ant-table-wrapper :deep(.ant-table.ant-table-bordered >.ant-table-container >.ant-table-content >table >tbody>tr>td) {
		border-bottom: 0.5px solid #bcbcbc;
	}


[data-doc-theme='light'] .ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}

[data-doc-theme='dark'] .ant-table-striped :deep(.table-striped) td {
  background-color: rgb(29, 29, 29);
}
</style>

<template>
  <template v-for="(filter, index) in lossFilters" :key="index">
    <a-col :span="6">
      <a-form-item :label="filter.label" :name="filter.name">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState[filter.name]" 
          :treeData="filter.options" 
          :disabled="filter.disabled"
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
  </template>
</template>

<script setup>
import { computed } from 'vue'
import { yesorno, lossOptions } from '../formOptions'

// Props
const props = defineProps({
  formState: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:formState'])

// 计算属性
const formState = computed({
  get: () => props.formState,
  set: (value) => emit('update:formState', value)
})

// 亏损相关筛选配置
const lossFilters = computed(() => [
  {
    label: '是否亏损：',
    name: 'hasLoss',
    options: yesorno
  },
  {
    label: '亏损类型：',
    name: 'lossType',
    options: lossOptions,
    disabled: formState.value.hasLoss?.length === 1 && formState.value.hasLoss.includes('N')
  }
])
</script>

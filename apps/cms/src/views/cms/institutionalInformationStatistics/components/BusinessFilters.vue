<template>
  <template v-for="(filter, index) in businessFilters" :key="index">
    <a-col :span="6">
      <a-form-item :label="filter.label" :name="filter.name">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState[filter.name]" 
          :treeData="filter.options" 
          :disabled="filter.disabled"
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
  </template>
</template>

<script setup>
import { computed } from 'vue'
import { yesorNoApplicable, thepurExistOptions } from '../formOptions'

// Props
const props = defineProps({
  formState: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:formState'])

// 计算属性
const formState = computed({
  get: () => props.formState,
  set: (value) => emit('update:formState', value)
})

// 业务相关筛选配置
const businessFilters = computed(() => [
  {
    label: '是否上市：',
    name: 'isListed',
    options: yesorNoApplicable
  },
  {
    label: '是否实质开展经营：',
    name: 'hasBusiness',
    options: yesorNoApplicable
  },
  {
    label: '不实质开展经营机构的存续目的：',
    name: 'thepurExist',
    options: thepurExistOptions,
    disabled: formState.value.hasBusiness?.length === 1 && formState.value.hasBusiness.includes('Y')
  }
])
</script>

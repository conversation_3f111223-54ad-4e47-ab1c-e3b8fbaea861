<template>
  <a-row>
    <!-- 展开维度选择 -->
    <a-col>
      <a-form-item name="detailFields">
        <a-form-item-rest>
          <a-checkbox
            :indeterminate="indeterminateDetail"
            :checked="checkAllDetail"
            @change="$emit('checkDetailAllChange', $event)"
          >
            展开维度
          </a-checkbox>
        </a-form-item-rest>
        <a-popover trigger="click">
          <template #content>
            <a-checkbox-group
              v-model:value="formState.detailFields"
              @change="$emit('changeDetail', $event)"
            >
              <div class="flex flex-col gap-y-1">
                <a-checkbox
                  :value="cb.value"
                  v-for="cb in detailFieldsOptions"
                  :key="cb.value"
                >
                  {{ cb.label }}
                </a-checkbox>
              </div>
            </a-checkbox-group>
          </template>
          <DownOutlined />
        </a-popover>
      </a-form-item>
    </a-col>

    <!-- 主要字段选择 -->
    <a-col class="ml-4">
      <a-form-item name="mainFields">
        <a-checkbox-group
          v-model:value="formState.mainFields"
          name="checkboxgroup"
          :options="mainFieldOptions"
        />
      </a-form-item>
    </a-col>

    <!-- 财务数据选择 -->
    <a-col>
      <a-form-item name="financeFields">
        <a-form-item-rest>
          <a-checkbox
            :indeterminate="indeterminateFinance"
            :checked="checkAllFinance"
            @change="$emit('checkFinanceAllChange', $event)"
          >
            财务数据
          </a-checkbox>
        </a-form-item-rest>
        <a-popover trigger="click">
          <template #content>
            <a-checkbox-group
              v-model:value="formState.financeFields"
              @change="$emit('changeFinance', $event)"
            >
              <div class="flex flex-col gap-y-1">
                <a-checkbox value="netProfitTotal">净利润合计</a-checkbox>
              </div>
            </a-checkbox-group>
          </template>
          <DownOutlined />
        </a-popover>
      </a-form-item>
    </a-col>

    <!-- 操作按钮 -->
    <a-col class="flex-1 flex justify-end items-start">
      <a-space>
        <a-button type="primary" @click="$emit('query')">
          <template #icon><SearchOutlined /></template>
          查询
        </a-button>
        <a-button ghost danger @click="$emit('reset')">
          <template #icon><RollbackOutlined /></template>
          重置
        </a-button>
        <qt-button ghost danger @click="(next) => $emit('export', next)">
          <template #icon><ExportOutlined /></template>
          导出
        </qt-button>
      </a-space>
    </a-col>
  </a-row>
</template>

<script setup>
import { computed } from 'vue'
import { DownOutlined, SearchOutlined, RollbackOutlined, ExportOutlined } from '@ant-design/icons-vue'
import { options } from '../formOptions'

// Props
const props = defineProps({
  formState: {
    type: Object,
    required: true
  },
  indeterminateDetail: {
    type: Boolean,
    default: false
  },
  checkAllDetail: {
    type: Boolean,
    default: false
  },
  indeterminateFinance: {
    type: Boolean,
    default: false
  },
  checkAllFinance: {
    type: Boolean,
    default: true
  },
  detailFieldsOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'update:formState',
  'checkDetailAllChange',
  'changeDetail',
  'checkFinanceAllChange',
  'changeFinance',
  'query',
  'reset',
  'export'
])

// 计算属性
const formState = computed({
  get: () => props.formState,
  set: (value) => emit('update:formState', value)
})

const mainFieldOptions = computed(() => options)
</script>

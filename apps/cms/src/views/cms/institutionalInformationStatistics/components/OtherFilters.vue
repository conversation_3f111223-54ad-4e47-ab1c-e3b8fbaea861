<template>
  <template v-for="(filter, index) in otherFilters" :key="index">
    <a-col :span="6">
      <a-form-item :label="filter.label" :name="filter.name">
        <a-tree-select
          mode="multiple"
          max-tag-count="responsive"
          tree-default-expand-all
          show-checked-strategy="SHOW_CHILD"
          tree-checkable
          treeNodeFilterProp="label"
          v-model:value="formState[filter.name]"
          :treeData="filter.options"
          placeholder="请选择"
          allow-clear
        />
      </a-form-item>
    </a-col>
  </template>
</template>

<script setup>
import { computed } from 'vue'
import { yesorno, yesorNoApplicable } from '../formOptions'

// Props
const props = defineProps({
  formState: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:formState'])

// 计算属性
const formState = computed({
  get: () => props.formState,
  set: (value) => emit('update:formState', value)
})

// 其他筛选配置
const otherFilters = computed(() => [
  {
    label: '是否为持股比例高于50%但不并表的机构：',
    name: 'isitanInsti',
    options: yesorno
  },
  {
    label: '是否使用中信品牌：',
    name: 'isuseCiticb',
    options: yesorno
  },
  {
    label: '中信方是否为第一大股东：',
    name: 'isCiticLargeShared',
    options: yesorno
  },
	// {
	// 	label: '是否实缴到位：',
	// 	name: 'contributedCapitalFull',
	// 	options: yesorNoApplicable
	// }
])
</script>

<template>
  <a-row :gutter="24">
    <a-col :span="6">
      <a-form-item label="类型：" name="enterpriseNatureNew">
        <a-input 
          v-model:value="formState.enterpriseNatureNew" 
          placeholder="请输入关键字" 
        />
      </a-form-item>
    </a-col>
    
    <a-col :span="6">
      <a-form-item label="机构类别：" name="fillingStandards">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState.fillingStandards" 
          :treeData="fillingOptions" 
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
    
    <a-col :span="6">
      <a-form-item label="法人层级：" name="legalEntityLevel">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState.legalEntityLevel" 
          :treeData="legalOptions" 
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
    
    <a-col :span="6">
      <a-form-item label="管理层级：" name="managementLevel">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState.managementLevel" 
          :treeData="managementOptions" 
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
    
    <a-col :span="6">
      <a-form-item label="集团板块：" name="plate">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState.plate" 
          :treeData="sectorOptions" 
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
    
    <a-col :span="6">
      <a-form-item label="所属门类：" name="industryLarge">
        <a-tree-select 
          mode="multiple" 
          max-tag-count="responsive" 
          tree-default-expand-all 
          show-checked-strategy="SHOW_CHILD" 
          tree-checkable 
          treeNodeFilterProp="label" 
          v-model:value="formState.industryLarge" 
          :treeData="industryOptions" 
          placeholder="请选择" 
          allow-clear 
        />
      </a-form-item>
    </a-col>
    
    <!-- 更多筛选字段... -->
    <BusinessFilters v-model:form-state="formState" />
    <LossFilters v-model:form-state="formState" />
    <OtherFilters v-model:form-state="formState" />
  </a-row>
</template>

<script setup>
import { computed } from 'vue'
import BusinessFilters from './BusinessFilters.vue'
import LossFilters from './LossFilters.vue'
import OtherFilters from './OtherFilters.vue'

import {
  fillingOptions, 
  legalOptions, 
  managementOptions, 
  sectorOptions, 
  industryOptions
} from '../formOptions'

// Props
const props = defineProps({
  formState: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:formState'])

// 计算属性
const formState = computed({
  get: () => props.formState,
  set: (value) => emit('update:formState', value)
})
</script>

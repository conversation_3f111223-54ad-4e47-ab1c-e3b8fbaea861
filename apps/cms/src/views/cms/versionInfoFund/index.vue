<template>
	<!-- 版本信息管理 -->
	<a-spin :spinning="isOperating" tip="数据处理中，请稍候...">
		<div class="h-full version-info-manage">
			<a-form ref="formRef">
				<a-row :gutter="24">
					<a-col>
						<a-form-item label="" name="categoryId">
							<qt-button @click="(next) => archive(next)" type="primary">归档</qt-button>
						</a-form-item>
					</a-col>
				</a-row>
			</a-form>
			<div ref="tableboxRef">
				<!-- <a-table
				class="s-table"
				ref="tableRef"
				:columns="columns"
				:data-source="tableData"
				:expand-row-by-click="true"
				:alert="options.alert.show"
				:row-selection="options.rowSelection"
				size="small"
				:row-key="(record) => record.id"
			>
			</a-table> -->
				<SearchTable
					ref="tableRef"
					:columns="columns"
					:api="bizVersionApi.getVersionList"
					:hasSearch="false"
					bordered
					:row-key="(record) => record.id"
					:searchInfo="searchInfo"
					:row-selection="options.rowSelection"
					:expand-row-by-click="true"
					:alert="options.alert.show"
				></SearchTable>
			</div>
			<a-modal :width="1000" okText="归档" v-model:open="open" title="归档确认">
				<template #footer>
					<a-button key="back" @click="open = false">取消</a-button>
					<a-popconfirm title="请再次确认是否归档?" ok-text="归档" cancel-text="取消" @confirm="handleOk">
						<a-button key="submit" type="primary" :loading="loading">归档</a-button>
					</a-popconfirm>
				</template>
				<div>
					{{ msg }}
				</div>
				<div v-for="(item, i) in nameList" :key="i">
					{{ item }}
				</div>
			</a-modal>
		</div>
	</a-spin>
</template>
<script setup name="versionInfoManage" lang="jsx">
	import { message } from 'ant-design-vue'
	import bizVersionApi from '@/api/biz/bizVersionApi.js'
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	const isOperating = ref(false)
	const searchInfo = ref({})
	const tableboxRef = ref()
	const tableRef = ref()
	const pageSizeNumber = ref(20)
	const height = ref(200)
	const tableScrollComputed = async () => {
		height.value = tableboxRef.value.offsetHeight - 56 - 47
	}
	// 列表选择配置
	const selectedRowKeys = ref()
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys.value = ref([])
			}
		},
		rowSelection: {
			type: 'radio',
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			},
			getCheckboxProps: (record) => ({
				disabled: record.status === '2', // Column configuration not to be checked
				name: record.name
			})
		}
	}
	const open = ref(false)
	const handleOk = (e) => {
		confirmArchive()
	}
	const msg = ref()
	const nameList = ref()
	// 点击归档按钮
	const archive = (next) => {
		if (!selectedRowKeys.value || selectedRowKeys.value.length == 0) {
			next()
			return message.error('请勾选需要归档的版本')
		}
		isOperating.value = true
		// const hideLoading = message.loading('开始归档中...')
		bizVersionApi
			.archive({
				id: selectedRowKeys.value[0]
			})
			.then((res) => {
				console.log(res, '==res=')
				isOperating.value = false
				tableRef.value?.refresh?.()
			})
			.catch((err) => {
				isOperating.value = false
				if (err?.data?.nameList) {
					msg.value = err?.data?.msg || '请确认是否归档'
					nameList.value = err.data.nameList
					open.value = true
				} else {
					tableRef.value?.refresh?.()
				}
			})
			.finally(() => {
				next()
				// hideLoading()
			})
	}
	const confirmArchive = () => {
		open.value = false
		isOperating.value = true
		bizVersionApi
			.confirmArchive({
				id: selectedRowKeys.value[0]
			})
			.then((res) => {
				// if (res !== null) {
				// 	open.value = false
				// 	message.success('归档成功')
				// }
				message.success('归档成功')
			})
			.catch(() => {
				open.value = false
			})
			.finally(() => {
				isOperating.value = false
				tableRef.value?.refresh?.()
			})
	}
	// 表格
	const tableData = ref()
	const current = ref(1)
	const size = ref(20)
	const getData = () => {
		tableData.value = null
		bizVersionApi
			.getVersionList({
				current: current.value,
				size: size.value
			})
			.then((res) => {
				if (res !== null) {
					console.log(res, '==res===')
					tableData.value = res.records
				}
			})
	}
	const columns = computed(() => {
		return [
			// {
			// 	title: '序号',
			// 	dataIndex: 'id',
			// 	ellipsis: true,
			// 	width: 80,
			// 	customRender: ({ text, record, index }) => {
			// 		return index + 1
			// 	}
			// },
			{
				title: '年份',
				dataIndex: 'year',
				ellipsis: true
			},
			{
				title: '期间',
				dataIndex: 'period',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					return text ? text + '月' : ''
				}
			},
			{
				title: '状态',
				dataIndex: 'status',
				ellipsis: true,
				customRender: ({ text, record, index }) => {
					return text == 1 ? '已开启' : text == 2 ? '已归档' : ''
				}
			},
			{
				title: '归档时间',
				dataIndex: 'archiveTime'
			}
		]
	})
</script>
<style>
	.version-info-manage
		.ant-table-wrapper
		.ant-table
		.ant-table-tbody
		tr.ant-table-row.ant-table-row-level-0:not(.search-row)
		td {
		padding: 12px 5px !important;
	}
</style>
<style lang="less" scoped>
	.version-info-manage {
		background: #fff;
		padding: 20px;
		overflow-y: auto;
		border-radius: 8px;
	}
</style>

<template>
	<!-- 股权关系 -->
	<div class="tree-org bg-white relative" id="#shareholderInfo">
		<Collapse title="架构树" :showIcon="false" contentPaddingTop="pt-8">
			<template #content>
				<div id="shareholderInfo">
					<div class="absolute right-0 top-6 z-9999 w-[285px] flex justify-between items-center mr-3">
						<a-tag color="error" :class="[treeType === '2' ? 'visible cursor-pointer' : 'invisible']" @click="seeAllShareholders">查看内部股东</a-tag>
						<a-radio-group
							v-model:value="treeType"
							button-style="solid"
							@change="handleChangeTree"
						>
							<a-radio-button value="1">管理架构</a-radio-button>
							<a-radio-button value="2">股权架构</a-radio-button>
						</a-radio-group>
					</div>
					<div class="h-[50px]"></div>
					<a-spin :spinning="loading" tip="加载中...">
						<div v-show="treeType === '1'" class="manage h-[530px]" ref="mindMapContainerRef">
							<Legend :topLevelColor="topLevelNodeColors" :hasBusiness="businessTypeColors" />
						</div>
						<div v-if="treeType === '2'" class="share h-[530px]">
							<RelationGraph ref="graphInstanceRef" :options="graphOptions" :on-node-expand="handleNodeExpand">
								<template #graph-plug>
									<Legend :topLevelColor="topLevelNodeColors" :hasBusiness="businessShareTypeColors" />
								</template>
								<template #node="{ node }">
									<a-tooltip>
										<template #title>{{ node.text }}</template>
										<div class="my-industy-node cursor-pointer relative">
										<!-- root node -->
											<div class="my-card-body">{{ node.text }}</div>
										</div>
									</a-tooltip>
								</template>
							</RelationGraph>
						</div>
					</a-spin>
				</div>
			</template>
		</Collapse>
	</div>
</template>
<script setup lang="jsx">
	import { message } from 'ant-design-vue'
	import tool from '@/utils/tool'
	import MindMap from 'simple-mind-map'
	import MiniMap from 'simple-mind-map/src/plugins/MiniMap.js'
	import Watermark from 'simple-mind-map/src/plugins/Watermark.js'
	import KeyboardNavigation from 'simple-mind-map/src/plugins/KeyboardNavigation.js'
	import ExportPDF from 'simple-mind-map/src/plugins/ExportPDF.js'
	import ExportXMind from 'simple-mind-map/src/plugins/ExportXMind.js'
	import Export from 'simple-mind-map/src/plugins/Export.js'
	import Select from 'simple-mind-map/src/plugins/Select.js'
	import AssociativeLine from 'simple-mind-map/src/plugins/AssociativeLine.js'
	import TouchEvent from 'simple-mind-map/src/plugins/TouchEvent.js'
	import NodeImgAdjust from 'simple-mind-map/src/plugins/NodeImgAdjust.js'
	import SearchPlugin from 'simple-mind-map/src/plugins/Search.js'
	import Painter from 'simple-mind-map/src/plugins/Painter.js'
	import Formula from 'simple-mind-map/src/plugins/Formula.js'
	import portraitApi from '@/api/biz/portraitApi.js'
	import Legend from './legend.vue'
	import { cloneDeep, differenceBy, isEmpty, isNil } from 'lodash-es'
	import { hasPerm } from '@/utils/permission'
	import RelationGraph from 'relation-graph-vue3'
	import internalinvestmentApi from '@/api/biz/bizEntityinternalinvestmentApi'
	import { ROOT_NODE_IDS } from '@/views/cms/managementStructure/constants'
	import { SearchOutlined } from '@ant-design/icons-vue';
	import { nextTick,h } from 'vue'
	import router from '@/router'
	MindMap.usePlugin(MiniMap)
		.usePlugin(Watermark)
		.usePlugin(KeyboardNavigation)
		.usePlugin(ExportPDF)
		.usePlugin(ExportXMind)
		.usePlugin(Export)
		.usePlugin(Select)
		.usePlugin(AssociativeLine)
		.usePlugin(NodeImgAdjust)
		.usePlugin(TouchEvent)
		.usePlugin(SearchPlugin)
		.usePlugin(Painter)
		.usePlugin(Formula)

	const mindMapContainerRef = ref()
	const treeType = ref('1')
	const props = defineProps({
		data: {
			type: Object,
			default: () => {}
		},
		infoData: {
			type: Object,
			default: () => {}
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	const loading = ref(true)
	let mindMap = null
	const _mindMap = ref()
	const topLevelNodeColors = {
		'中信集团': '#D20000',
		'中信股份': '#D45100',
		'中信有限': '#485563'
	}
	const rootNodeLevelMap = {
		'20140824164901061871': 'group-0',
		'20140822152314001513': 'stock-0',
		'20140824164901061873': 'limit-0'
	}
	const businessTypeColors = {
		root: {
			'20140824164901061871': '#D20000',
			'20140822152314001513': '#D45100',
			'20140824164901061873': '#485563'
		},
		Y: {
			2: '#ddedf8',
			3: '#e1f0da',
			4: '#ffeadf',
			5: '#fff7dc'
		},
		N: {
			2: '#7f7f7f',
			3: '#a6a6a6',
			4: '#bfbfbf',
			5: '#d9d9d9'
		}
	}
	const businessShareTypeColors = {
		root: {
			'20140824164901061871': '#D20000',
			'20140822152314001513': '#D45100',
			'20140824164901061873': '#485563'
		},
		'Y': {
			'并表': '#ddedf8',
			'参股': '#e1f0da',
			'实质管理': '#ffeadf',
		},
		'N': {
			'并表': '#7f7f7f',
			'参股': '#a6a6a6',
			'实质管理': '#bfbfbf',
		}
	}


	const rootNodeIds = Object.keys(rootNodeLevelMap)
	// 如果点击的节点，那么代表被点击的节点
	const currentNode = shallowRef(null)
	const showManageGraph = async (data) => {
		// 递归处理investList和其detailList
		function buildInvestTree(node) {
			return {
				data: {
					text: node.simpOrgName,
					isClickable: true,
					uid: node.entityId,
					...node
				},
				children: (node.detailList || []).filter((v) => !rootNodeIds.includes(v.entityId)).map(buildInvestTree)
			}
		}

		// 收集链条上的所有节点（从最浅到最深），遇到 rootNodeIds 包含的 entityId 时只添加一次并终止递归
		function collectUpperChain(node, chain = []) {
			chain.push(node)
			if (rootNodeIds.includes(node.entityId)) {
				// 如果是根节点，只添加一次并终止递归
				return chain
			}
			if (node.detailList && node.detailList.length > 0) {
				return collectUpperChain(node.detailList[0], chain)
			}
			return chain
		}

		// 构建树结构，从最深到最浅包裹
		function buildChainTree(chain, currentNode) {
			let root = currentNode
			for (let i = 0; i < chain.length; i++) {
				const node = chain[i]
				root = {
					data: {
						text: node.simpOrgName,
						entityId: node.entityId,
						managementLevel: rootNodeIds.includes(node.entityId)
							? rootNodeLevelMap[node.entityId]
							: node.managementLevel,
						hasBusiness: rootNodeIds.includes(node.entityId) ? 'Y' : node.hasBusiness,
						isClickable: node.isClickable === '1'
					},
					children: [root]
				}
			}
			return root
		}

		// 当前节点为根节点，children递归investList
		const currentNode = {
			data: {
				text: data.simpOrgName,
				entityId: data.entityId,
				managementLevel: rootNodeIds.includes(data.entityId) ? rootNodeLevelMap[data.entityId] : data.managementLevel,
				hasBusiness: rootNodeIds.includes(data.entityId) ? 'Y' : data.hasBusiness,
				isClickable: true,
				uid: data.entityId
				// ...其他字段
			},
			children: (data.investList || []).filter((v) => !rootNodeIds.includes(v.entityId)).map(buildInvestTree)
		}

		// 如果有 upperList，则收集所有上级链条节点并反向包裹
		let __graph_json_data = currentNode
		if (data.upperList && data.upperList.length > 0 && !rootNodeIds.includes(data.entityId)) {
			const upper = data.upperList[0]
			const chain = collectUpperChain(upper, [])
			// __graph_json_data = buildChainTree(chain, currentNode)
			__graph_json_data = {
				data: {
					text: '',
					hasBusiness: 'Y',
					managementLevel: 'root-0',
					uid: 'root-0',
					fillColor: '#fff'
				},
				children: [buildChainTree(chain, currentNode)]
			}
		}

		mindMap.updateData(__graph_json_data)

		if (rootNodeIds.includes(data.entityId)) {
			mindMap.execCommand('UNEXPAND_TO_LEVEL', '1')
			// mindMap.setLayout('catalogOrganization')
		} else {
			// mindMap.execCommand('UNEXPAND_TO_LEVEL', '1')
			// mindMap.setLayout('timeline')
		}

		//
		console.log('=>(index.vue:302) __graph_json_data', __graph_json_data)
	}

	const fetchManageGraphData = async () => {
		loading.value = true
		const parms =
			props.infoData.paramYear === '' && props.infoData.paramMonth === ''
				? {
						entityId: props.infoData.entityId ?? rootNodeIds[0],
						isCurrentVersion: 1
				  }
				: {
						entityId: props.infoData.entityId,
						year: props.infoData.paramYear,
						month: props.infoData.paramMonth,
						versionId: props.infoData.versionId
				  }
		try {
			const data = await portraitApi.manageTree(parms)
			console.log('=>(shareholder.vue:217) isEmpty(data)', isEmpty(data))
			if (isEmpty(data)) {
				mindMap.clearDraw()
			} else {
				await showManageGraph(data)
			}
		} finally {
			loading.value = false
		}
	}

	async function init() {
		mindMap = new MindMap({
			el: mindMapContainerRef.value,
			data: null,
			editable: false, // 开启编辑模式
			isUseCustomNodeContent: true,
			initRootNodePosition: ['center', 'top'],
			expandBtnSize: 24,
			expandBtnStyle: {
				color: '#808080',
				fill: '#fff',
				fontSize: 10,
				strokeColor: '#333333'
			},
			customCreateNodeContent: (node) => {
				let div = document.createElement('div')
				const { managementLevel, hasBusiness, text, entityId } = node.nodeData.data
				const isChildrenLevel = node.layerIndex === 1
				// 构建CSS类名
				const layoutClass = isChildrenLevel ? 'vertical-layout' : 'horizontal-layout'

				if (ROOT_NODE_IDS.includes(entityId)) {
					div.className = `${hasBusiness} my-industy-node-level-${managementLevel} ${layoutClass}`
				} else {
					if (isNil(hasBusiness) || isNil(managementLevel)) {
						div.className = `K my-industy-node-level-default-0 ${layoutClass}`
					} else {
						div.className = `${hasBusiness} my-industy-node-level-${Math.abs(managementLevel)} ${layoutClass}`
					}
				}
				div.innerHTML = `
						<div class="my-industy-node text-center">
						  <div class="my-card-body">${text}</div>
            </div>
            `
				return div
			},
			customHandleMousewheel: (e) => {
				if (e.deltaY > -1) {
					const main = document.getElementById('industryInfo')
					main.scrollIntoView({ behavior: 'smooth', block: 'center' })
				} else {
					const main = document.getElementById('keyInfo')
					main.scrollIntoView({ behavior: 'smooth', block: 'center' })
				}
			},
			disableMouseWheelZoom: true,
			mousewheelAction: 'zoom', // zoom（放大缩小）、move（上下移动）
			// 禁止鼠标滚轮缩放，你仍旧可以使用api进行缩放
			// disableMouseWheelZoom: false,
			// alwaysShowExpandBtn: true,
			layout: 'catalogOrganization',
			themeConfig: {
				backgroundColor: '#fff',
				// 连线的粗细
				lineWidth: 2,
				// 连线的颜色
				lineColor: '#a6a6a6',
				// 连线样式
				lineDasharray: 'none',
				// 连线风格，支持三种
				// 1.曲线（curve）。仅logicalStructure、mindMap、verticalTimeline三种结构支持。
				// 2.直线（straight）。
				// 3.直连（direct）。仅logicalStructure、mindMap、organizationStructure、verticalTimeline四种结构支持。
				lineStyle: 'straight'
			},
			// 曲线连接时，根节点和其他节点的连接线样式保持统一，默认根节点为 ( 型，其他节点为 { 型，设为true后，都为 { 型。仅logicalStructure、mindMap两种结构支持。
			rootLineKeepSameInCurve: true,
			// 直线连接(straight)时，连线的圆角大小，设置为0代表没有圆角，仅支持logicalStructure、mindMap、verticalTimeline三种结构
			lineRadius: 5,
			// 连线尾部是否显示标记，目前只支持箭头
			showLineMarker: false,
			isShowCreateChildBtnIcon: false,
			// 概要连线的粗细
			generalizationLineWidth: 1
		})
		_mindMap.value = toRaw(mindMap)
		// 节点右键事件
		mindMap.on('node_contextmenu', (e, node) => {
			if (e.which === 3) {
				currentNode.value = node.nodeData
				console.log('=>(index.vue:528) node.nodeData', node.nodeData)
			}
		})
		mindMap.on('node_tree_render_end', waitEndLoading)
	}
	const rootPercent = ref(0)
	const getRootNodeData = async () => {
		const params = {
			entityId: '20140822152314001513',
			versionId: '00000000000000000000',
			isCurrentVersion: '1'
		}
		try {
			const data = await internalinvestmentApi.getPage(params)
			const promiseList = data.records.map(async (r) => {
				const res = await portraitApi.relationTree({
					...params,
					entityId: r.investorId
				})
				return {
					...res,
					shareRate: r.shareRate
				}
			})
			const result = await Promise.all(promiseList)
			console.log('=>(shareholder.vue:332) result', result)
			result.forEach((item) => {
				const topGroup = item.upperList[0]
				const num = parseFloat(topGroup.percent.replace('%', '')) / 100
				const accTotcal = item.shareRate * num
				rootPercent.value = rootPercent.value + accTotcal
			})
		} catch (e) {
			console.log('=>(index.vue:472) e', e)
		}
	}
	const waitEndLoading = () => {
		loading.value = false
		const node = mindMap.renderer.findNodeByUid('root-0')
		if (node) {
			node.removeLine()
		}
		// const node = mindMap.renderer.findNodeByUid(props.infoData.entityId)
		// if (node) {
		// 	mindMap.renderer.moveNodeToCenter(node, undefined)
		// }
	}

	onMounted(async () => {
		await init()
		await fetchManageGraphData()
		await getRootNodeData()
	})
	watch(
		() => props.infoData,
		(value) => {
			console.log('=>(shareholder.vue:287) value', value)
			if (treeType.value === '1') {
				fetchManageGraphData()
			} else {
				fetchGraphData()
			}
		},
		{ deep: true }
	)
	//股份相关
	const graphOptions = {
		reLayoutWhenExpandedOrCollapsed: true,
		disableZoom: true,
		performanceMode: true,
		moveToCenterWhenRefresh: true,
		disableDragNode: true,
		disableLineClickEffect: true,
		allowShowMiniToolBar: false,
		allowShowDownloadButton: false,
		allowShowFullscreenMenu: false,
		defaultFocusRootNode: false,
		defaultExpandHolderPosition: 'right',
		defaultNodeShape: 1,
		defaultNodeBorderWidth: 0,
		defaultLineShape: 4,
		defaultLineColor: '#333',
		// defaultPolyLineRadius: 15,
		defaultJunctionPoint: 'lr',
		// defaultLineTextOffset_x: 100,
		// defaultLineTextOffset_y: -3,
		// defaultJunctionPoint: 'border',
		// lineUseTextPath: true,
		defaultLineMarker: {
			markerWidth: 20,
			markerHeight: 20,
			refX: 3,
			refY: 3,
			data: 'M 0 0, V 6, L 4 3, Z'
		},
		layout: {
			layoutName: 'tree',
			from: 'left',
			min_per_height: 40,
			min_per_width: 200,
			levelDistance: '400,400,400,400,400,400'
		}
	}
	const graphInstanceRef = ref()
	const discordNodes = [
		{
			entityId: '20140829153600000689'
		},
		{
			entityId: '20140829153600000687'
		},
		{
			entityId: '20220119154215029860'
		}
	]
	const isTop = computed(() => {
		return props.infoData.entityId === rootNodeIds[0]
	})
	function findTopEntityId(node) {
		let current = node
		while (current.detailList && current.detailList.length > 0) {
			current = current.detailList[0]
		}
		return current.entityId
	}

	function deepFindByEntityId(data, entityId) {
		for (const item of data) {
			if (item.entityId === entityId) {
				return item
			}
			if (item.detailList && item.detailList.length > 0) {
				const found = deepFindByEntityId(item.detailList, entityId)
				if (found) return found
			}
		}
		return undefined
	}

	const openByLevel = async (level) => {
		const graphInstance = graphInstanceRef.value?.getInstance()
		if (graphInstance) {
			// Reset data
			graphInstance.getNodes().forEach((node) => {
				node.expanded = true
				node.alignItems = 'top'
			})
			// Reset data
			graphInstance.getNodes().forEach((node) => {
				if (rootNodeIds.includes(node.data.entityId)) {
					node.className = `${node.data.hasBusiness} my-industy-node-level-` + node.data.fillingStandards
				} else {
					if (isNil(node.data.hasBusiness) || isNil(node.data.fillingStandards) || node.data.fillingStandards.length === 0) {
						node.className = `K my-industy-node-level-default-0`
					} else {
						node.className = `${node.data.hasBusiness} my-industy-node-level-` + node.data.fillingStandards
					}
				}
			})
			graphInstance.getNodes().forEach((node) => {
				// Determine the level of the node (root node is 0)
				if (Math.abs(node.lot.level) >= level) {
					// console.log('collapseNode:', level, node.text, node.lot.level);
					// Collapse node

					node.expanded = false
				}
			})
			await graphInstance.moveToCenter()
			await graphInstance.doLayout()
		}
	}
	const showShareGraph = async (data) => {
		let __graph_json_data = null
		if (isTop.value) {
			__graph_json_data = {
				rootId: 'root',
				nodes: [
					{
						id: 'root',
						text: 'root',
						opacity: 0
					},
					{
						id: '20140822152314001513',
						text: '中国中信股份有限公司',
						expandHolderPosition: data.investList.length > 0 ? 'right' : 'hide',
						expanded: false,
						data: {
							entityId: '20140822152314001513',
							fillingStandards: 'stock-0',
							hasBusiness: 'Y',
							isClickable: true,
							isNeedLoadDataFromRemoteServer: true,
							text: '100%'
						}
					},
					{
						id: '20140824164901061873',
						text: '中国中信有限公司',
						expandHolderPosition: data.investList.length > 0 ? 'right' : 'hide',
						expanded: false,
						data: {
							entityId: '20140824164901061873',
							fillingStandards: 'limit-0',
							hasBusiness: 'Y',
							isClickable: true,
							isNeedLoadDataFromRemoteServer: true,
							text: '100%'
						}
					}
				],
				lines: [
					{
						from: 'root',
						to: '20140824164901061871',
						isHide: true
					},
					{
						from: 'root',
						to: '20140822152314001513',
						isHide: true
					},
					{
						from: 'root',
						to: '20140824164901061873',
						isHide: true
					}
				]
			}
		} else {
			let topId = ''
			if (data.upperList && data.upperList.length > 0) {
				topId = findTopEntityId(data.upperList[0])
			} else {
				topId = data.entityId
			}
			__graph_json_data = {
				rootId: topId,
				nodes: [],
				lines: []
			}
		}

		__graph_json_data.nodes.push({
			id: `${data.entityId}`,
			text: data.name,
			data: {
				entityId: data.entityId,
				fillingStandards: rootNodeIds.includes(data.entityId)
					? rootNodeLevelMap[data.entityId]
					: data.fillingStandards.slice(-1),
				hasBusiness: rootNodeIds.includes(data.entityId) ? rootNodeLevelMap[data.entityId] : data.hasBusiness,
				isClickable: true,
				isNeedLoadDataFromRemoteServer: false
			}
		})
		if (rootNodeIds[0] === data.entityId) {
			__graph_json_data.lines.push({
				from: '20140824164901061871',
				to: '20140822152314001513',
				toJunctionPoint: 'tb',
				fromJunctionPoint: 'tb',
				text: (rootPercent.value * 100).toFixed(2) + '%',
				textOffset_x: 30,
				textOffset_y: -3
			})
			__graph_json_data.lines.push({
				from: '20140822152314001513',
				to: '20140824164901061873',
				toJunctionPoint: 'tb',
				fromJunctionPoint: 'tb',
				text: '100%',
				textOffset_x: 30,
				textOffset_y: -3
			})
		}
		function pushNodeAndChildren(node, parentId, isUpper = false, graphData, rootNodeIds, rootNodeLevelMap) {
			graphData.nodes.push({
				id: `${node.entityId}`,
				text: node.name,
				data: {
					entityId: node.entityId,
					fillingStandards: rootNodeIds.includes(node.entityId)
						? rootNodeLevelMap[node.entityId]
						: node.fillingStandards.slice(-1),
					hasBusiness: rootNodeIds.includes(node.entityId) ? 'Y' : node.hasBusiness,
					isClickable: isUpper ? node.isClickable === '1' : true,
					text: node.percent
				}
			})

			if (parentId) {
				const exceptNode =
					`${data.entityId}` !== discordNodes[0].entityId &&
					`${data.entityId}` !== discordNodes[1].entityId &&
					`${data.entityId}` !== rootNodeIds[1]
				const isCurTop = rootNodeIds.includes(node.entityId) && exceptNode
				const linsShape = node.detailList && node.detailList.length > 1 ? 4 : 1
				graphData.lines.push(
					isUpper
						? {
								from: `${node.entityId}`,
								to: parentId,
								text: node.percent,
								textOffset_x: isCurTop ? 100 : 0,
								textOffset_y: -3,
								linsShape
						  }
						: {
								from: parentId,
								to: `${node.entityId}`,
								text: node.percent,
								textOffset_x: isCurTop ? 25 : 100,
								textOffset_y: -3,
								linsShape,
								fromJunctionPoint: isCurTop ? 'tb' : 'lr',
								toJunctionPoint: isCurTop ? 'tb' : 'lr'
						  }
				)
			}
			if (node.detailList && node.detailList.length > 0) {
				node.detailList.forEach((child) => {
					pushNodeAndChildren(child, `${node.entityId}`, isUpper, graphData, rootNodeIds, rootNodeLevelMap)
				})
			}
		}

		// 下级
		if (data?.investList?.length > 0) {
			let investNodes = data?.investList
			if (rootNodeIds[0] === data?.entityId) {
				const stock = deepFindByEntityId(data?.investList, rootNodeIds[1]) ?? []
				const limit = deepFindByEntityId(data?.investList, rootNodeIds[2]) ?? []
				investNodes = differenceBy(data.investList, discordNodes, 'entityId')
				// 批量处理stock的子节点
				if (stock?.detailList?.length > 0) {
					const stockChildren = stock.detailList.filter(s => s.entityId !== rootNodeIds[2])
					__graph_json_data.nodes[1].data.childrenData = stockChildren
				}

				//批量处理limit的子节点
				if (limit?.detailList?.length > 0) {
					__graph_json_data.nodes[2].data.childrenData = limit.detailList
				}
			}
			investNodes.forEach((v) => {
				pushNodeAndChildren(v, `${data.entityId}`, false, __graph_json_data, rootNodeIds, rootNodeLevelMap)
			})
		}

		// 上级
		if (data?.upperList?.length > 0) {
			data.upperList.forEach((v) => {
				pushNodeAndChildren(v, `${data.entityId}`, true, __graph_json_data, rootNodeIds, rootNodeLevelMap)
			})
		}

		console.log('=>(index.vue:302) __graph_json_data', __graph_json_data)
		const graphInstance = graphInstanceRef.value?.getInstance()
		if (graphInstance) {
			await graphInstance.setJsonData(__graph_json_data)
			await graphInstance.doLayout()
			await openByLevel(1)
			await graphInstance.moveToCenter()
			loading.value = false
		}
	}
	const fetchGraphData = async () => {
		loading.value = true
		const parms =
			props.infoData.paramYear === '' && props.infoData.paramMonth === ''
				? {
						entityId: props.infoData.entityId,
						isCurrentVersion: 1,
						isQueryHighestShare: '1'
				  }
				: {
						entityId: props.infoData.entityId,
						year: props.infoData.paramYear,
						month: props.infoData.paramMonth,
						versionId: props.infoData.versionId,
						isQueryHighestShare: '1'
				  }
		try {
			const data = await portraitApi.relationTree(parms)
			await showShareGraph(data)
		} catch (e) {
			console.log('=>(index.vue:472) e', e)
		} finally {
			loading.value = false
		}
	}
	const handleChangeTree = ($event) => {
		if ($event.target.value === '2') {
			fetchGraphData()
		} else {
			fetchManageGraphData()
		}
	}
	const DISCORD_NODES = [
		{ entityId: '20140829153600000689' },
		{ entityId: '20140829153600000687' },
		{ entityId: '20220119154215029860' }
	]

	const ROOT_NODE_LEVEL_MAP = {
		"20140824164901061871": 'group-0',
		"20140822152314001513": 'stock-0',
		"20140824164901061873": 'limit-0'
	}
	function buildNodeAndChildren(node, parentId, isUpper = false, graphData, rootNodeIds, DISCORD_NODES, rootNodeLevelMap, rootData) {
		graphData.nodes.push({
			id: `${node.entityId}`,
			text: node.name,
			expanded: false,
			data: {
				entityId: node.entityId,
				fillingStandards: rootNodeIds.includes(node.entityId) ? rootNodeLevelMap[node.entityId] : node.fillingStandards.slice(-1),
				hasBusiness: rootNodeIds.includes(node.entityId) ? 'Y' : node.hasBusiness,
				isClickable: isUpper ? node.isClickable === '1' : true,
				text: node.percent
			}
		})

		if (parentId) {
			const exceptNode = `${rootData.entityId}` !== DISCORD_NODES[0].entityId &&
				`${rootData.entityId}` !== DISCORD_NODES[1].entityId &&
				`${rootData.entityId}` !== rootNodeIds[1]
			const isCurTop = rootNodeIds.includes(node.entityId) && exceptNode
			const linsShape = node.detailList.length === 1 ? 1 : 4

			graphData.lines.push(
				isUpper
					? {
						from: `${node.entityId}`,
						to: parentId,
						text: node.percent,
						textOffset_x: isCurTop ? 60 : 0,
						textOffset_y: -3,
						linsShape
					}
					: {
						from: parentId,
						to: `${node.entityId}`,
						text: node.percent,
						textOffset_x: isCurTop ? 25 : 60,
						textOffset_y: -3,
						linsShape,
						fromJunctionPoint: isCurTop ? 'tb' : 'lr',
						toJunctionPoint: isCurTop ? 'tb' : 'lr'
					}
			)
		}

		if (node.detailList && node.detailList.length > 0) {
			node.detailList.forEach(child => {
				buildNodeAndChildren(child, `${node.entityId}`, isUpper, graphData, rootNodeIds, DISCORD_NODES, rootNodeLevelMap, rootData)
			})
		}
	}

	const buildChildrenGraphData = async (node, rootNodeIds, DISCORD_NODES, ROOT_NODE_LEVEL_MAP) => {
		const graphData = {
			nodes: [],
			lines: []
		}

		// 检查是否有子数据
		const childrenData = node.data.childrenData || []
		if (childrenData.length === 0) {
			console.log('ℹ️ 节点无子数据')
			return graphData
		}

		// 批量构建子节点
		childrenData.forEach((childData, index) => {
			try {
				buildNodeAndChildren(
					childData,
					`${node.data.entityId}`,
					false,
					graphData,
					rootNodeIds,
					DISCORD_NODES,
					ROOT_NODE_LEVEL_MAP,
					node.data
				)
			} catch (error) {
				console.error(`❌ 构建子节点 ${index + 1} 失败:`, error)
			}
		})

		return graphData
	}
	const updateNodeStyles = async (graphInstance, rootNodeIds) => {
		console.log('🎨 更新节点样式...')

		const nodes = graphInstance.getNodes()
		let updatedCount = 0

		nodes.forEach(node => {
			const oldClassName = node.className

			if (rootNodeIds.includes(node.data.entityId)) {
				// 根节点样式
				node.className = `${node.data.hasBusiness} my-industy-node-level-${node.data.fillingStandards}`
			} else {
				// 普通节点样式
				if (isNil(node.data.hasBusiness) || isNil(node.data.fillingStandards) || node.data.fillingStandards.length === 0) {
					node.className = `K my-industy-node-level-default-0`
				} else {
					node.className = `${node.data.hasBusiness} my-industy-node-level-${node.data.fillingStandards}`
				}
			}

			if (oldClassName !== node.className) {
				updatedCount++
			}
		})

		// 触发样式更新
		await nextTick()
	}
	const handleNodeExpand = async (node) => {
		const graphInstance = graphInstanceRef.value?.getInstance()
		if (!graphInstance) {
			console.error('❌ 图表实例不存在，无法展开节点')
			return
		}

		if (!node.data?.isNeedLoadDataFromRemoteServer) {
			return
		}

		if (node.data?.childrenLoaded) {
			return
		}
		await graphInstance.loading(true)
		node.data.childrenLoaded = true
		const graphData = await buildChildrenGraphData(node, rootNodeIds, DISCORD_NODES, ROOT_NODE_LEVEL_MAP)
		if (graphData.nodes.length > 0 || graphData.lines.length > 0) {
			await graphInstance.appendJsonData(graphData)
			await updateNodeStyles(graphInstance, rootNodeIds)
			await graphInstance.moveToCenter()
			await graphInstance.clearLoading()
		} else {
			console.log('ℹ️ 节点无子数据，展开完成')
		}
	}
	const seeAllShareholders = () => {
		const targetRoute = router.resolve({
			path: '/shareholdingStructure/service',
			query: { entityId: props.infoData.entityId, year: props.infoData.paramYear,
				month: props.infoData.paramMonth }
		})
		window.open(targetRoute.href, '_blank')
	}
</script>
<style lang="less" scoped>
	.tree-org {
		height: 650px;
		padding: 10px 20px;
		display: block;
		margin: 15px 0 0 0;
		box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
		border-radius: 6px;
		box-sizing: border-box;
		position: relative;
	}

	:deep(.my-industy-node-level-default-0) {
		.my-industy-node {
			border: #fccbcb solid 1px;
			color: #333333;
			.my-card-header {
				background: #fff;
			}
			.my-card-body {
				background: #fccbcb;
			}
		}
	}

	.manage {
		:deep(.horizontal-layout .my-industy-node) {
			width: 200px;
			border-radius: 5px;
			background-color: #ffffff;
			overflow: clip;
			.my-card-body {
				line-height: 20px;
				padding: 5px 10px;
			}
		}

		:deep(.vertical-layout .my-industy-node) {
			width: 60px;
			border-radius: 5px;
			background-color: #ffffff;
			overflow: clip;
			.my-card-body {
				line-height: 20px;
				padding: 5px 10px;
			}
		}

		:deep(.my-industy-node-level-group-0) {
			.my-industy-node {
				width: 200px;
				border: #d20000 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #d20000;
				}
				.my-card-body {
					color: #fff;
					background: #d20000;
					font-size: 18px;
					line-height: 50px;
					height: 50px;
					padding: 0;
				}
			}
		}

		:deep(.my-industy-node-level-stock-0) {
			.my-industy-node {
				width: 200px;
				border: #d45100 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #d45100;
				}
				.my-card-body {
					color: #fff;
					background: #d45100;
					font-size: 18px;
					line-height: 50px;
					height: 50px;
					padding: 0;
				}
			}
		}

		:deep(.my-industy-node-level-limit-0) {
			.my-industy-node {
				width: 200px;
				border: #485563 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #485563;
				}
				.my-card-body {
					color: #fff;
					background: #485563;
					font-size: 18px;
					line-height: 50px;
					height: 50px;
					padding: 0;
				}
			}
		}

		:deep(.my-industy-node-level-1.Y) {
			.my-industy-node {
				border: #ddedf8 solid 1px;
				color: #333333;
			}
		}
		:deep(.my-industy-node-level-1.N) {
			.my-industy-node {
				border: #333 solid 1px;
				.my-card-body {
					color: #fff;
					background: #333;
				}
			}
		}

		:deep(.my-industy-node-level-2.Y) {
			.my-industy-node {
				border: #ddedf8 solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #ddedf8;
				}
			}
		}
		:deep(.my-industy-node-level-2.N) {
			.my-industy-node {
				border: #7f7f7f solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #333;
				}
				.my-card-body {
					color: #fff;
					background: #7f7f7f;
				}
			}
		}

		:deep(.my-industy-node-level-3.Y) {
			.my-industy-node {
				border: #e1f0da solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #e1f0da;
				}
			}
		}
		:deep(.my-industy-node-level-3.N) {
			.my-industy-node {
				border: #a6a6a6 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #333;
				}
				.my-card-body {
					color: #fff;
					background: #a6a6a6;
				}
			}
		}

		:deep(.my-industy-node-level-4.Y) {
			.my-industy-node {
				border: #ffeadf solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #ffeadf;
				}
			}
		}
		:deep(.my-industy-node-level-4.N) {
			.my-industy-node {
				border: #bfbfbf solid 1px;
				.my-card-header {
					color: #333;
					background-color: #fff;
				}
				.my-card-body {
					color: #fff;
					background: #bfbfbf;
				}
			}
		}

		:deep(.my-industy-node-level-5.Y) {
			.my-industy-node {
				border: #fff7dc solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #fff7dc;
				}
			}
		}
		:deep(.my-industy-node-level-5.N) {
			.my-industy-node {
				border: #d9d9d9 solid 1px;
				color: #999999;
				.my-card-header {
					background-color: #fff;
					color: #333;
				}
				.my-card-body {
					color: #fff;
					background: #d9d9d9;
				}
			}
		}
	}

	.share {
		.my-industy-node {
			width: 200px;
			border-radius: 5px;
			background-color: #ffffff;
			overflow: clip;
			.my-card-body {
				line-height: 30px;
				text-wrap: nowrap;
			}
		}

		:deep(.c-btn-open-close) {
			& span {
				background-color: #d20000 !important;
			}
		}
		:deep(.my-industy-node-level-group-0) {
			.my-industy-node {
				width: 200px;
				border: #d20000 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #d20000;
				}
				.my-card-body {
					color: #fff;
					background: #d20000;
					font-size: 18px;
					line-height: 50px;
					height: 50px;
					padding: 0;
				}
			}
		}

		:deep(.my-industy-node-level-stock-0) {
			.my-industy-node {
				width: 200px;
				border: #d45100 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #d45100;
				}
				.my-card-body {
					color: #fff;
					background: #d45100;
					font-size: 18px;
					line-height: 50px;
					height: 50px;
					padding: 0;
				}
			}
		}

		:deep(.my-industy-node-level-limit-0) {
			.my-industy-node {
				width: 200px;
				border: #485563 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #485563;
				}
				.my-card-body {
					color: #fff;
					background: #485563;
					font-size: 18px;
					line-height: 50px;
					height: 50px;
					padding: 0;
				}
			}
		}

		:deep(.my-industy-node-level-1.Y) {
			.my-industy-node {
				border: #ddedf8 solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #ddedf8;
				}
			}
		}
		:deep(.my-industy-node-level-1.N) {
			.my-industy-node {
				border: #7f7f7f solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #333;
				}
				.my-card-body {
					color: #fff;
					background: #7f7f7f;
				}
			}
		}

		:deep(.my-industy-node-level-2.Y) {
			.my-industy-node {
				border: #e1f0da solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #e1f0da;
				}
			}
		}
		:deep(.my-industy-node-level-2.N) {
			.my-industy-node {
				border: #a6a6a6 solid 1px;
				.my-card-header {
					background-color: #fff;
					color: #333;
				}
				.my-card-body {
					color: #fff;
					background: #a6a6a6;
				}
			}
		}

		:deep(.my-industy-node-level-3.Y) {
			.my-industy-node {
				border: #ffeadf solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #ffeadf;
				}
			}
		}
		:deep(.my-industy-node-level-3.N) {
			.my-industy-node {
				border: #bfbfbf solid 1px;
				.my-card-header {
					color: #333;
					background-color: #fff;
				}
				.my-card-body {
					color: #fff;
					background: #bfbfbf;
				}
			}
		}

		:deep(.my-industy-node-level-4.Y) {
			.my-industy-node {
				border: #fccbcb solid 1px;
				color: #333333;
				.my-card-header {
					background: #fff;
				}
				.my-card-body {
					background: #fccbcb;
				}
			}
		}


	}
</style>

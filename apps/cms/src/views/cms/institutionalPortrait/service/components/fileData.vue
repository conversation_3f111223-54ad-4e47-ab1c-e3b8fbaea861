<template>
	<div class="disposeOf-content bg-white flex flex-col overflow-hidden box-border" id="fileInfo">
		<Collapse title="附件信息" :showIcon="false">
			<template #content>
				<a-spin :spinning="loading" wrapperClassName="spinning-wrapper">
					<div class="grid grid-cols-3 gap-4">
						<div
							class="flex items-center justify-between gap-4 p-4 rounded-md bg-gray-100 text-gray-600 text-sm"
							v-for="item in FileList"
						>
							<div class="flex-1 truncate cursor-pointer">
								<a-tooltip :title="item.fileName">{{ item.fileName }}</a-tooltip>
							</div>
							<div class="flex gap-2 items-center">
								<img
									src="/src/assets/images/cms/business/view.png"
									alt="up"
									class="h-3 w-auto cursor-pointer"
									@click="previewFile(item, FileList)"
								/>
								<img
									src="/src/assets/images/cms/business/download.png"
									alt="download"
									class="h-3 w-auto cursor-pointer"
									@click="download(item)"
								/>
							</div>
						</div>
					</div>
				</a-spin>
			</template>
		</Collapse>
		<timeline-form-file-preview ref="timelineFormFilePreviewRef" />
	</div>
</template>
<script setup lang="jsx">
	import { ref } from 'vue'
	import { find, cloneDeep } from 'lodash-es'
	import { message, Modal } from 'ant-design-vue'
	import { downloadByUrl } from '@/utils/downloadFile'
	import tool from '@/utils/tool'
	import TimelineFormFilePreview from './timelineFormFilePreview.vue'
	import fileApi from '@/api/dev/fileApi'
	const { modal } = Modal.useModal()
	const props = defineProps({
		infoData: {
			type: Object,
			default: () => {}
		}
	})
	const loading = ref(false)
	const FileList = computed(() => {
		let arr = cloneDeep(props.infoData?.fileList)
		console.log(arr)
		arr?.map((item) => {
			item.name = item?.fileName
			item.url = item?.filePath
			return item
		})
		return arr
	})
	const timelineFormFilePreviewRef = ref()
	const previewFile = (item, fileArr) => {
		const fileExtension = item?.fileName.substring(item?.fileName.lastIndexOf('.') + 1)

		const param = {
			...item,
			suffix: fileExtension
		}
		if (!item.url) return message.error(`未找到文件，请确认文件是否存在！`)
		timelineFormFilePreviewRef.value.onOpen(param, fileArr)
	}
	const download = async (item) => {
		console.log(item)
		try {
			if (!item.filePath) return message.error(`未找到文件，请确认文件是否存在！`)
			fileApi.getObsPathByFilePath({ filePath: item.url }).then((res) => {
				downloadByUrl({ url: res, fileName: item.name })
			})
		} catch {
			message.error(`下载报错`)
		}
	}
</script>
<style lang="less" scoped>
	:deep(.ant-tabs .ant-tabs-content-holder) {
		overflow: auto;
		height: auto;
	}
	:deep(.ant-spin-container) {
		height: 100%;
	}
	.disposeOf-content {
		// height: 480px;
		vertical-align: top;
		padding: 10px 20px;
		margin: 15px 0 0 0;
		box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
		border-radius: 6px;
		box-sizing: border-box;
	}
	.title {
		font-size: 15px;
		font-weight: 700;
	}
	.info-item {
		display: inline-block;
		vertical-align: top;
		margin-bottom: 10px;
		box-sizing: border-box;
		padding-left: 5px;
		.label {
			color: #999999;
			font-size: 14px;
		}
		.value {
			color: #2a2c39;
			font-size: 14px;
			font-weight: bold;
		}
		.text-area-style {
			background-color: #f6f6f6;
			border-radius: 5px;
			padding: 10px;
			box-sizing: border-box;
			border: 1px solid 1px solid #e6e6e6;
		}
	}
	.spinning-wrapper {
		max-height: 450px;
		overflow-y: auto;
	}
	.sliming-divider {
		margin: 5px 0px;
	}
</style>

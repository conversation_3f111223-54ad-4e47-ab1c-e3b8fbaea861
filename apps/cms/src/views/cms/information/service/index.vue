<template>
	<a-spin :spinning="cardLoading" wrapperClassName="overflow-hidden h-full" size="large">
		<a-space class="w-full p-3 mb-[10px] bg-[#d6000f] rounded-lg text-white action-btn">
			<template #split>
				<a-divider type="vertical" style="background-color: #fff; border-left: 2px solid #fff" />
			</template>
			<a-space v-if="isShow && hasPerm(['addChildren', 'edit', 'move', 'moveUpOrDown', 'edit'])">
				<a-button
					v-if="hasPerm('addChildren')"
					type="link"
					size="small"
					@click="onAdd"
					:disabled="btnDisabled || isLock"
				>
					新建下级
				</a-button>
				<a-button
					v-if="hasPerm('edit')"
					type="link"
					size="small"
					@click="onEdit"
					:disabled="btnDisabled || lockBtnEdit"
				>
					修改
				</a-button>
				<a-button
					v-if="hasPerm('move')"
					type="link"
					size="small"
					@click="onMove()"
					:disabled="btnDisabled || lockBtnEdit"
				>
					架构移动
				</a-button>
				<a-dropdown v-if="hasPerm('moveUpOrDown')" :disabled="btnDisabled || lockBtnEdit">
					<template #overlay>
						<a-menu>
							<a-menu-item key="1" @click="onMoveUpOrDown('1')"> 上移 </a-menu-item>
							<a-menu-item key="2" @click="onMoveUpOrDown('2')"> 下移 </a-menu-item>
						</a-menu>
					</template>
					<a-button type="link" size="small">
						顺序调整
						<CaretDownFilled />
					</a-button>
				</a-dropdown>
			</a-space>
			<a-space v-if="isShow && hasPerm(['handle', 'recover'])">
				<a-button
					v-if="hasPerm('handle')"
					type="link"
					size="small"
					@click="onHandleFn"
					:disabled="btnDisabled || lockBtnEdit"
				>
					处置
				</a-button>
				<a-button
					v-if="hasPerm('recover')"
					type="link"
					size="small"
					@click="onRecoverFn"
					:disabled="btnDisabled || lockBtnEdit"
				>
					恢复处置
				</a-button>
			</a-space>
			<a-space v-if="isShow && hasPerm(['lockAdd', 'lockEdit'])">
				<a-dropdown v-if="hasPerm(['lockAdd'])" :disabled="btnDisabled">
					<template #overlay>
						<a-menu @click="handlerLock">
							<a-menu-item key="1"> 锁定新建 </a-menu-item>
							<a-menu-item key="0"> 解锁新建 </a-menu-item>
						</a-menu>
					</template>
					<a-button type="link" size="small">
						新建管理
						<CaretDownFilled />
					</a-button>
				</a-dropdown>
<!--				<a-dropdown v-if="isShow && hasPerm('lockEdit')" :disabled="btnDisabled">-->
<!--					<template #overlay>-->
<!--						<a-menu @click="lockEdit">-->
<!--							<a-menu-item key="0"> 本机构 </a-menu-item>-->
<!--							<a-menu-item key="1"> 下级机构 </a-menu-item>-->
<!--						</a-menu>-->
<!--					</template>-->
<!--					<a-button type="link" size="small">-->
<!--						提交-->
<!--						<CaretDownFilled />-->
<!--					</a-button>-->
<!--				</a-dropdown>-->
			</a-space>
			<a-space v-if="!isShow">
				<a-button type="link" size="small" :loading="cardLoading" @click="submitForm"> 保存 </a-button>
				<QtButton type="link" size="small" @click="(next) => onCancel(next)"> 取消 </QtButton>
				<QtButton
					type="link"
					size="small"
					@click="(next) => onVerify(next)"
					v-if="mainRef?.getTabsForm(0).registarea1 == 'RA100000'"
					:disabled="dataCount <= 0"
				>
					信息校验(剩余{{ dataCount }}次)
					<template #icon>
						<a-tooltip title="调取第三方工商信息校验，每月（期间）每家机构限点击5次">
							<QuestionCircleOutlined />
						</a-tooltip>
					</template>
				</QtButton>
			</a-space>
			<a-space v-if="isShow && hasPerm(['history', 'export'])">
				<a-button v-if="hasPerm('history')" type="link" size="small" @click="historyRef.onOpen()"> 历史版本 </a-button>
				<QtButton v-if="hasPerm('export')" type="link" size="small" @click="(next) => onExport(next)"> 导出 </QtButton>
			</a-space>
		</a-space>
		<Main
			ref="mainRef"
			:disabled="disabled"
			:approveForm="approveForm"
			:busineseForm="busineseForm"
			:moveNodeData="moveNodeData"
			:isAdd="isAdd"
			@isSave="isSave"
			@loadingFn="(loadingStaus) => (cardLoading = loadingStaus)"
			@treeType="treeType"
			@treeClick="treeClick"
		></Main>
	</a-spin>
	<ChangePage ref="changePageRef" />
	<History ref="historyRef" />
	<ApproveForm ref="approveFormRef" @successful="ApproveFormSuccessful" />
	<MoveTree ref="moveTreeRef" @successful="treeMoveSuccessful" />
	<ManageForm ref="manageFormRef" @successful="manageSuccessful" />
	<AddApprove ref="addApproveRef" @successful="addApproveSuccessful" />
</template>
<script setup name="informationService">
	import { message, Modal } from 'ant-design-vue'
	import downloadUtil from '@/utils/downloadUtil.js'
	import Main from './components/main.vue'
	import AddApprove from './components/addApprove.vue'
	import { getListArr } from '@/utils/businessVerification'
	import ApproveForm from './components/approveForm.vue'
	import ChangePage from './changePage.vue'
	import History from './history.vue'
	import MoveTree from './moveTree.vue'
	import ManageForm from './manageForm.vue'
	import bizInstitutionApi from '@/api/biz/bizInstitutionApi.js'
	import EntityCreateApi from '@/api/biz/entityCreateApi'
	import bizEntityDisposalApi from '@/api/biz/bizEntityDisposalApi.js'
	import bizEntityProcessApi from '@/api/biz/bizEntityProcessApi.js'
	import tool from '@/utils/tool'

	const route = useRoute()
	const changePageRef = ref()
	const historyRef = ref()
	const mainRef = ref()
	const approveFormRef = ref()
	const moveTreeRef = ref()
	const manageFormRef = ref()
	const addApproveRef = ref()
	const disabled = ref(true)
	const cardLoading = ref(false)
	const btnClickNum = ref(0)
	const btnDisabled = ref(false)
	const isAdd = ref(false)
	const isMoveNode = ref(false)
	const lockBtnEdit = ref(false)
	const getTreeNode = computed(() => {
		return mainRef.value.getNode()
	})
	const dataCount = computed(() => {
		const num = 5
		let count = num - btnClickNum.value
		count = isAdd.value ? num : count - mainRef.value?.data?.checkCount
		return count > 0 ? count : 0
	})

	const { roleCodeList } = tool.data.get('USER_INFO')
	const isShow = computed(() => {
		return disabled.value
	})

	const treeType = (data) => {
		btnDisabled.value = data != 0
		if (!disabled.value) {
			disabled.value = true
			mainRef.value.getDetail(getTreeNode.value.id, getTreeNode.value.ywId)
		}
	}
	const onAdd = () => {
		getTreeNode.value?.id ? approveFormRef.value.onOpen(mainRef.value.curVersionDate) : message.warning('请选择机构!')
		btnClickNum.value = 0
	}
	const approveForm = ref({})
	const ApproveFormSuccessful = (data) => {
		// approveForm.value = data
		approveForm.value = {
			...data,
			...{
				entityinternalinvestmentList: [
					{
						id: '',
						investorId: getTreeNode.value?.id,
						relationship: null,
						shareRate: '',
						isHightShareholder: 'Y',
						isConsolidation: getTreeNode.value?.isConsolidation,
						isEdit: true
					}
				]
			}
		}
		btnClickNum.value = 0
		isAdd.value = true
		disabled.value = false
		isMoveNode.value = false
		tabActive.value = '0'
	}

	const onEdit = () => {
		isAdd.value = false
		btnClickNum.value = 0
		getTreeNode.value?.id ? (disabled.value = false) : message.warning('请选择机构!')
	}
	const onMove = () => {
		if (!getTreeNode.value?.id) return message.warning('请选择机构!')
		const node = getTreeNode.value
		if (node.lockStatus == 1) return message.warning('已被锁定，无法移动!')
		if (node.parent.node.isClickable != 1) return message.warning('无该机构上级权限，不可移动!')
		moveTreeRef.value.onOpen(getTreeNode.value, mainRef.value?.data.fillingStandards == 'filling_standards_4')
	}
	const moveNodeData = ref({})

	const treeMoveSuccessful = (data) => {
		moveNodeData.value = data
		disabled.value = false
		tabActive.value = '1'
		isMoveNode.value = true
	}
	const moveApiFn = async (data) => {
		bizInstitutionApi.moveNode({ orgOrderNo: data }).then((res) => {
			// mainRef.value.clearSelectTree()
			// getTreeNode.value?.id
			mainRef.value.treeAttr.selected = [getTreeNode.value.id]
			mainRef.value.loadTreeData()
		})
	}
	const onMoveUpOrDown = (type) => {
		isAdd.value = false
		if (!getTreeNode.value?.id) return message.warning('请选择机构!')
		const node = getTreeNode.value
		const id = node.id
		const parent = node.parent
		const parentId = {
			organizationParentId: node.parentId
		}
		if (node.lockStatus == 1) return message.warning('已被锁定，无法移动!')
		if (parent?.node.children?.length == 1)
			return message.warning(type == 1 ? '已经到达最顶部，无法继续上移' : '已经到达最底部，无法继续下移')
		const index = parent?.node.children.find((item) => item.key == id)?.index
		switch (type) {
			case '1':
				// 上
				if (index == 0) return message.warning('已经到达最顶部，无法继续上移')
				const prev = parent.node.children[index - 1]
				if (prev.lockStatus == 1) return message.warning('上方机构已被锁定，无法移动!')
				moveApiFn([
					{
						...parentId,
						...{
							id: node.ywId,
							entityCode: node.entityCode,
							sortCode: prev.sortCode
						}
					},
					{
						...parentId,
						...{
							id: prev.ywId,
							entityCode: prev.entityCode,
							sortCode: node.sortCode
						}
					}
				])
				break
			case '2':
				// 下
				if (index == parent?.node.children.length - 1) return message.warning('已经到达最底部，无法继续下移')
				const next = parent.node.children[index + 1]
				if (next.lockStatus == 1) return message.warning('下方机构已被锁定，无法移动!')
				moveApiFn([
					{
						...parentId,
						...{
							id: next.ywId,
							entityCode: next.entityCode,
							sortCode: node.sortCode
						}
					},
					{
						...parentId,
						...{
							id: node.ywId,
							entityCode: node.entityCode,
							sortCode: next.sortCode
						}
					}
				])
				break
		}
	}
	const getAllData = async (id) => {
		cardLoading.value = true
		cardLoading.value = false
	}
	// 验证函数
	const tabFormObj = ref(new Map())
	// 选中的tabs
	const tabActive = ref('0')

	// 提供注册/注销方法
	provide('validatorRegistry', {
		tabActive,
		register: (key, validator) => {
			tabFormObj.value.set(key, validator)
		},
		unregister: (key) => {
			tabFormObj.value.delete(key)
		}
	})

	// 提交保存
	const submitForm = async () => {
		try {
			cardLoading.value = true
			let formData = {
				editType: isMoveNode.value ? '3' : '',
				oldDetail: isAdd.value ? {} : mainRef.value?.oldData
			}
			for (const [key, validator] of tabFormObj.value) {
				const data = await validator()
				Object.assign(formData, data)
			}
			console.log('合并后的数据:', formData)
			const otherVerify = async () => {
				if (formData.consolidationParentId && formData.orgParentunitId) {
					try {
						const res = await bizInstitutionApi.isParentunitByOrg({
							isadd: isAdd.value ? '1' : '0',
							entityId: isAdd.value ? '' : formData?.entityId,
							entityCode: isAdd.value ? '' : formData?.entityCode,
							versionId: isAdd.value ? '' : mainRef.value.data?.versionId,
							consolidationParentId: formData?.consolidationParentId,
							orgParentunitId: formData?.orgParentunitId,
							organizationParentId: formData?.organizationParentId
						})
						if (res == 1) {
							cardLoading.value = false
							if (!formData.hasLoss) {
								tabActive.value = '2'
								return message.warning('属于管理架构，请选择是否亏损！')
							}
							if (!formData.legalEntityLevel || !formData.legalEntityLevel) {
								tabActive.value = '3'
							}
							if (!formData.legalEntityLevel) {
								return message.warning('属于管理架构，请选择法人层级！')
							}
							if (!formData.managementLevel) {
								return message.warning('属于管理架构，请选择管理层级！')
							}
						}
					} catch (error) {
						console.log(error)
						cardLoading.value = false
						return message.error(error?.msg || '操作错误')
					}
				}

				const submitApiFn = () => {
					cardLoading.value = true
					bizInstitutionApi
						.addOrEdit(formData)
						.then(async (res) => {
							if (isMoveNode.value) mainRef.value.treeAttr.selected = [getTreeNode.value.id]
							await mainRef.value.loadTreeData()
							await mainRef.value.getDetailLoading(getTreeNode.value.id, getTreeNode.value.ywId)
							disabled.value = true
							isAdd.value = false
							isMoveNode.value = false
						})
						.finally(() => {
							cardLoading.value = false
						})
				}
				if (Number(formData.legalEntityLevel || 0) > 7) {
					Modal.confirm({
						title: '提示',
						content: '“根据集团要求，法人层级不应超过7级，请确认法人层级是否准确？',
						okText: '已确认',
						cancelText: '未确认',
						onOk: async () => {
							if (Number(formData.managementLevel || 0) > 4) {
								Modal.confirm({
									title: '提示',
									content: '“根据集团要求，管理层级不应超过4级，请确认管理层级是否准确？',
									okText: '已确认',
									cancelText: '未确认',
									onOk: async () => {
										submitApiFn()
									},
									onCancel: () => {
										cardLoading.value = false
									}
								})
							} else {
								submitApiFn()
							}
						},
						onCancel: () => {
							cardLoading.value = false
						}
					})
				} else if (Number(formData.managementLevel || 0) > 4) {
					Modal.confirm({
						title: '提示',
						content: '“根据集团要求，管理层级不应超过4级，请确认管理层级是否准确？',
						okText: '已确认',
						cancelText: '未确认',
						onOk: async () => {
							submitApiFn()
						},
						onCancel: () => {
							cardLoading.value = false
						}
					})
				} else {
					submitApiFn()
				}
			}

			if (formData.fillingStandards == 'filling_standards_1' || formData.fillingStandards == 'filling_standards_3') {
				const arr = formData.entityinternalinvestmentList?.filter((item) => item.relationship == 'relationship_1')
				if (arr.length && arr[0]?.investorId != formData.consolidationParentId) {
					Modal.confirm({
						title: '提示',
						content: '上级并表单位与内部股东中“与内部股东的财务关系”为“子公司”的股东不一致，请检查填报是否正确',
						okText: '继续提交',
						onOk: async () => {
							otherVerify()
						},
						onCancel: () => {
							cardLoading.value = false
						}
					})
				} else {
					otherVerify()
				}
			} else {
				if (formData.fillingStandards == 'filling_standards_2'){
					if (formData.manageCompany != mainRef.value?.oldData.manageCompany) {
						Modal.confirm({
							title: '提示',
							content: '通过上级管理单位获取的管理主体和原有管理主体字段不一致，请确认是否填写正确或者上级管理单位发生变化',
							okText: '继续提交',
							onOk: async () => {
								otherVerify()
							},
							onCancel: () => {
								cardLoading.value = false
							}
						})
					} else {
						otherVerify()
					}
				}else{
					otherVerify()
				}
			}
			// 管理机构
		} catch (error) {
			console.log(error)
			tabActive.value = error.key
			cardLoading.value = false
		}
	}
	const onCancel = async (next) => {
		try {
			mainRef.value.clearTabsValidate()
			disabled.value = true
			await mainRef.value.getDetailLoading(getTreeNode.value.id, getTreeNode.value.ywId)
			isAdd.value = false
			isMoveNode.value = false
		} finally {
			next()
		}
	}
	// 编辑状态点击树先保存数据
	const isSave = async (data) => {
		// 保存
		if (data.status) {
			// getAllData(data.id)
			await submitForm()
			// 不需要局部loading
			// mainRef.value.getDetail(data.id, data.ywId)
		} else {
			// 不保存
			disabled.value = true
			isAdd.value = false
			isMoveNode.value = false
			// 根据id获取对应的
		}
	}
	// 处置
	const onHandleFn = async (type) => {
		if (!getTreeNode.value?.id) return message.warning('请选择机构!')
		if (mainRef.value?.data.status == 1) return message.warning('该机构已处置!')
		if (mainRef.value?.data.status == 2) return message.warning('该机构已被锁定，请先解锁!')
		manageFormRef.value.onOpen({ ...mainRef.value.data })
	}
	// 恢复处置
	const onRecoverFn = async (type) => {
		if (!getTreeNode.value?.id) return message.warning('请选择机构!')
		// if (mainRef.value?.data.status == 0) return message.warning('该机构未处置!')
		if (mainRef.value?.data.status == 2) return message.warning('该机构已被锁定，请先解锁!')
		Modal.confirm({
			title: '提示',
			content: '是否确认恢复该单位？',
			onOk: async () => {
				bizEntityDisposalApi
					.recovery({
						ywId: mainRef.value?.data.id,
						entityId: mainRef.value?.data.entityId,
						versionId: mainRef.value?.data.versionId,
						parentId: mainRef.value?.data.organizationParentId,
						status: mainRef.value?.data.status
					})
					.then(async (res) => {
						if (res?.isDisposal == 0 || res?.isDisposal == 2 || res?.isDisposal == 4) {
							if (res.isDisposal == 0) message.warning('未处置数据，不可进行恢复！')
							if (res.isDisposal == 2) message.warning('验证本机构父级为机构类别为：不纳入登记范围，不可进行恢复！')
							const fillingIsNot = res.fillingIsNot
							if (
								fillingIsNot.isOtherConsolidation == 1 ||
								fillingIsNot.isOtherParentunit == 1 ||
								fillingIsNot.isOtherInternal == 1 ||
								fillingIsNot.isOtherFilier == 1 ||
								fillingIsNot.isOrgnaizationChild == 1
							) {
								Modal.warning({
									title: '无法执行操作',
									content: h('div', {}, [
										fillingIsNot.isOtherConsolidation == 1 ? h('p', '本机构是其他机构上级并表单位，无法处置!') : '',
										fillingIsNot.isOtherParentunit == 1 ? h('p', '本机构是其他机构上级管理单位，无法处置!') : '',
										fillingIsNot.isOtherInternal == 1 ? h('p', '本机构是其他机构内部股东，无法处置!') : '',
										fillingIsNot.isOtherFilier == 1 ? h('p', '本机构是其他机构填报人所在机构，无法处置!') : '',
										fillingIsNot.isOrgnaizationChild == 1 ? h('p', '本机构有股权架构下级机构，无法处置!') : '',
										fillingIsNot.isTrueRemoveChild == 1
											? h('p', '本机构下级机构存在非”不纳入登记范围”的机构，无法处置!')
											: ''
									]),
									okText: '确认'
								})
							}
						} else if (res?.isDisposal == 1) {
							message.success('恢复成功!')
							mainRef.value.getDetailLoading(getTreeNode.value.id, getTreeNode.value.ywId)
							mainRef.value.loadTreeData()
						}
					})
			}
		})
	}
	// 锁定编辑
	const lockEdit = async ({ key }) => {
		const data = mainRef.value?.data
		if (!getTreeNode.value?.id) return message.warning('请选择机构!')
		if (key == 0 && data?.lockStatus == 1) return message.warning('该机构已被锁定，请先解锁!')
		cardLoading.value = true
		bizEntityProcessApi
			.lockOrg({ ywId: data.id, entityId: data.entityId, type: key })
			.then(async (res) => {
				console.log(res)
				if (getTreeNode.value?.status == 1) {
					mainRef.value.clearSelectTree()
				} else {
					mainRef.value.treeAttr.selected = [getTreeNode.value.id]
				}
				await mainRef.value.loadTreeData()
				console.log('锁定后的tree选中值：', getTreeNode.value)
				if (key == 0) lockBtnEdit.value = true
			})
			.finally(() => {
				cardLoading.value = false
			})
	}
	const treeClick = (data) => {
		lockBtnEdit.value = data.lockStatus == 1
	}
	const isLock = ref(false)
	const getCreateLockStatus = async () => {
		isLock.value = await EntityCreateApi.isLock()
	}
	const handlerLock = ({ key }) => {
		if (key === '1') {
			Modal.confirm({
				title: '提示',
				content: '确认锁定？',
				onOk: async () => {
					try {
						await EntityCreateApi.lock({ status: '1' })
						isLock.value = true
						message.success('锁定成功')
					} catch (e) {
						console.log('=>(index.vue:340) e', e)
					}
				}
			})
		} else {
			EntityCreateApi.lock({ status: '0' })
			message.success('解锁成功')
			isLock.value = false
		}
	}

	getCreateLockStatus()

	const onExport = async (next) => {
		if (!getTreeNode.value?.id) {
			next()
			return message.warning('请选择机构!')
		}
		const data = mainRef.value?.data
		if (!data?.versionId || !data?.entityId) {
			next()
			return message.warning('未获取到版本号或者版本对应股权主键!')
		}
		bizInstitutionApi
			.exportShEntityInfo({ versionId: data?.versionId, exportType: 2, entityId: data?.entityId })
			.then((res) => {
				downloadUtil.resultDownload(res)
			})
			.finally(() => {
				next()
			})
	}
	const onVerify = async (next) => {
		const form = mainRef.value?.getTabsForm(0)
		if (form?.registarea1 == 'RA100000') {
			try {
				const arr = await getListArr({ registCode: form?.registcode.trim() }, form)
				console.log("=>(index.vue:634) arr", arr);
				if (arr.length == 0) {
					message.warning('未找到有效的工商登记信息，请手动填写或检查统一社会信用代码是否正确')
				} else if (arr.length > 0) {
					btnClickNum.value++
					addApproveRef.value.onOpen(arr)
				}
				next()
			} catch (e) {
				console.log(e)
				next()
			}
		} else {
			message.warning('注册地区请选择中国大陆地区!')
			next()
		}
	}
	const busineseForm = ref({})
	const addApproveSuccessful = (data) => {
		console.log('信息校验返回值：', data)
		busineseForm.value = data
	}
	const manageSuccessful = async (data) => {
		if (data.status) {
			mainRef.value.data = {}
		} else {
			mainRef.value.treeAttr.selected = [getTreeNode.value.id]
			await mainRef.value.getDetailLoading(getTreeNode.value.id, getTreeNode.value.ywId)
			// tabActive.value = data.tabActive
		}
		await mainRef.value.loadTreeData()
		disabled.value = data.disabled
	}
</script>
<style lang="less" scoped>
	.no-sider {
		margin-left: 0;
	}
	.action-btn {
		:deep(.ant-btn-link) {
			color: #fff !important;
		}
	}

	:deep(.ant-spin-container) {
		height: 100%;
		overflow: hidden;
		display: flex;
		flex-flow: column nowrap;
		// .ant-card {
		// 	flex: none;
		// 	height: calc(100% - 58px);
		// }
	}
	:deep(.ant-input-number) {
		.ant-input-number-handler-wrap {
			display: none;
		}
	}
</style>

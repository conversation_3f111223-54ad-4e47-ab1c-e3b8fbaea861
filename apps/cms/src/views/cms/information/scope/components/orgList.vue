<template>
	<div ref="treeBox" class="p-[12px] rounded-lg bg-white relative" :style="styleComputed">
		<div
			class="absolute z-10 h-8 cursor-pointer flex items-center top-[50%] bg-[#ffe8e6] border-[1px] border-solid border-[#fa9096] border-r-0 rounded-l-md"
			:style="{ right: treeCollapse ? 0 : '0', ...collapseStyle }"
			@click="collapseBtnClick"
			v-if="showCollapse"
		>
			<CaretLeftOutlined class="mx-auto my-0 text-[#d6000f]" v-if="treeCollapse" />
			<CaretRightOutlined class="mx-auto my-0 text-[#d6000f]" v-else />
		</div>
		<div
			class="absolute right-0 top-0 w-[6px] h-full z-9 cursor-col-resize dragBox"
			v-if="draggable && treeCollapse"
			@mousedown="startDrag"
			@mouseup="stopDrag"
			title="按住鼠标左键拖动调整架构树宽度"
		>
			<!-- <a-divider type="vertical" style="height: 100%; margin: 0; border-color: #ccc" dashed />
			<a-divider
				type="vertical"
				class="h-full m-0"
				style="height: 100%; margin: 0; margin-left: 2px; border-color: #ccc"
				dashed
			/> -->
		</div>
		<a-spin :spinning="loading">
			<slot name="treeType" ref="treeType"></slot>
			<div
				v-if="search"
				ref="searchBoxRef"
				class="flex items-center gap-2 mb-2 search-box"
				:class="[slotKeys.includes('treeType') ? 'mt-2' : 'mt-0']"
			>
				<div class="flex-1 overflow-hidden">
					<TreeSelect
						class="w-full"
						v-if="searchTree"
						v-model:value="searchTreeValue"
						:field-names="{ children: 'children', label: 'simpOrgName', value: 'id' }"
						:tree-data="treeData"
						:dropdownMatchSelectWidth="400"
						:placeholder="placeholder"
						@change="autoSelectTree"
						:disabled="$attrs?.searchDisabled"
					/>
					<a-input
						v-else
						@change="searchChange"
						v-model:value="searchValue"
						:placeholder="placeholder"
						:disabled="$attrs?.searchDisabled"
						allow-clear
					>
						<template #prefix>
							<LoadingOutlined style="color: #999999" v-if="searchLoading" />
							<SearchOutlined style="color: #999999" v-else />
						</template>
					</a-input>
				</div>
				<slot name="fold" />
			</div>
			<div class="left-org-list">
				<a-tree
					ref="elTreeRef"
					class="truncate w-full"
					v-if="treeObj.data.length > 0"
					:tree-data="treeObj.data"
					:field-names="treeFieldNames"
					:autoExpandParent="autoExpandParent"
					v-model:expandedKeys="treeSelectAttr.expandedKeys"
					v-model:selectedKeys="treeSelectAttr.selectedKeys"
					v-model:checkedKeys="treeSelectAttr.checkedKeys"
					v-bind="{ ...$attrs, class: '' }"
					@expand="onExpand"
					@select="onSelect"
					:height="treeHeight"
					:virtual="false"
					blockNode
					switcherIcon
				>
					<!-- <template v-for="slotKey in slotKeys" #[slotKey]>
						<slot :name="slotKey" />
					</template> -->

					<template #title="{ data, isWrite, status, statusName }">
						<div
							v-if="
								search &&
								data[Props.title] &&
								typeof data[Props.title] === 'string' &&
								data[Props.title].indexOf(searchValue) > -1
							"
							class="truncate w-full"
						>
							<slot name="Render" v-bind="{ isWrite, status, statusName }"> </slot>
							<a-tooltip placement="topLeft" :title="data[Props.title]">
								<div class="flex justify-between items-center gap-1">
									<div class="flex-1 truncate">
										{{ data[Props.title].substring(0, data[Props.title].indexOf(searchValue)) }}
										<span style="color: #d6000f" class="search-value">{{ searchValue }}</span>
										{{ data[Props.title].substring(data[Props.title].indexOf(searchValue) + searchValue.length) }}
									</div>
									<slot name="right-icon" v-bind="{ data, status }"></slot>
								</div>
							</a-tooltip>
						</div>
						<div class="truncate w-full" v-else>
							<slot name="Render" v-bind="{ isWrite, status, statusName }"></slot>
							<a-tooltip placement="topLeft" :title="data[Props.title]">
								<div class="flex justify-between items-center gap-2">
									<div class="flex-1 truncate">
										{{ data[Props.title] }}
									</div>
									<slot name="right-icon" v-bind="{ data, status }"></slot>
								</div>
							</a-tooltip>
						</div>
					</template>
				</a-tree>
				<a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
			</div>
		</a-spin>
	</div>
</template>

<script setup>
	import { Empty } from 'ant-design-vue'
	import { debounce, map } from 'lodash-es'
	import { nextTick } from 'vue'
	const emit = defineEmits(['treeSelect', 'treeExpand', 'asyncSearch', 'autoSelectNodeData', 'collapseBtnClick'])
	const Props = defineProps({
		// 数据
		treeData: {
			type: Array,
			default: () => []
		},
		placeholder: {
			type: String,
			default: '请输入搜索内容'
		},
		// 至少选择一个节点
		selectLeastOne: {
			type: Boolean,
			default: false
		},
		// 搜索框
		search: {
			type: Boolean,
			default: true
		},
		searchTree: {
			type: Boolean,
			default: true
		},
		// 加载
		loading: {
			type: Boolean,
			default: true
		},
		// field-names内容
		children: {
			type: String,
			default: 'children'
		},
		title: {
			type: String,
			default: 'title'
		},
		keys: {
			type: String,
			default: 'key'
		},
		// 默认选中
		selected: {
			type: Array,
			default: () => []
		},
		// 默认展开
		expanded: {
			type: Array,
			default: () => []
		},
		// 默认勾选
		checked: {
			type: Array,
			default: () => []
		},
		isAsync: {
			type: Boolean,
			default: false
		},
		showCollapse: {
			type: Boolean,
			default: false
		},
		collapseStyle: {
			type: Object,
			default: () => {}
		},
		width: {
			type: Number,
			default: 300
		},
		minWidth: {
			type: Number,
			default: 0
		},
		maxWidth: {
			type: Number,
			default: 500
		},
		draggable: {
			type: Boolean,
			default: true
		}
	})
	const Attrs = useAttrs()
	const slots = useSlots()
	const slotKeys = computed(() => {
		return Object.keys(slots)
	})
	const treeCollapse = ref(true)
	const dragState = reactive({
		isDragging: false,
		startX: 0,
		startWidth: 0
	})
	const elementWidth = ref(Props.width)
	const styleComputed = computed(() => {
		const w = treeCollapse.value ? elementWidth.value : 0
		return {
			width: Props.draggable ? w + 'px' : treeCollapse.value ? 'auto' : 0,
			paddingLeft: treeCollapse.value ? '12px' : '8px',
			paddingRight: treeCollapse.value ? '12px' : '8px'
		}
	})
	const collapseBtnClick = () => {
		treeCollapse.value = !treeCollapse.value
		console.log(treeCollapse.value)
	}
	const startDrag = (e) => {
		dragState.isDragging = true
		dragState.startX = e.clientX
		dragState.startWidth = elementWidth.value
		window.addEventListener('mousemove', handleDrag)
		window.addEventListener('mouseup', stopDrag)
		console.log('触发按下')
	}
	// 处理
	const handleDrag = (event) => {
		if (!dragState.isDragging) return
		// 计算宽度变化
		const deltaX = event.clientX - dragState.startX
		let newWidth = dragState.startWidth + deltaX
		// 应用边界限制
		console.log('拖拽中', newWidth)
		elementWidth.value = Math.max(Props.minWidth, Math.min(Props.maxWidth, newWidth))
	}
	const stopDrag = () => {
		dragState.isDragging = false
		console.log('抬起了,宽度设置', elementWidth.value)
		// 移除监听
		window.removeEventListener('mousemove', handleDrag)
		window.removeEventListener('mouseup', stopDrag)
	}

	const searchTreeValue = ref(null)
	const elTreeRef = ref()
	// 替换 treeNode 中 title,key,children 字段为 treeData 中对应的字段
	const treeFieldNames = reactive({
		children: Props.children,
		title: Props.title,
		key: Props.keys
	})
	// 树选中的基本参数
	const treeSelectAttr = reactive({
		selectedKeys: Props.selected || [],
		expandedKeys: Props.expanded || [],
		checkedKeys: Props.checked || []
	})
	const searchValue = ref('')
	const autoExpandParent = ref(true)
	// 如果有默认选中，则展开相关节点
	// if (Attrs.expandedKeys) {
	// 	autoExpandParent.value = true
	// }
	// 本地化数据，方便搜索 数组扁平化
	const flatTreeMap = ref([])
	const dataList = []
	const generateList = async (data) => {
		for (let i = 0; i < data.length; i++) {
			const node = data[i]
			const key = node[treeFieldNames.key]
			dataList.push(node)
			flatTreeMap.value[key] = node
			if (node.children) {
				generateList(node.children)
			}
		}
	}
	const treeObj = ref({
		data: Props.treeData
	})
	watch(
		() => Props.treeData,
		async (newVal) => {
			treeObj.value.data = newVal
			await generateList(Props.treeData)
			if (Props.selected.length) autoSelectTree(Props.selected[0])
		},
		{ immediate: true }
	)
	// generateList(Props.treeData)
	// 获取父元素
	const getParentKey = (key, tree) => {
		let parentKey
		for (let i = 0; i < tree?.length; i++) {
			const node = tree[i]
			if (node.children) {
				if (node.children.some((item) => item[treeFieldNames.key] === key)) {
					parentKey = node[treeFieldNames.key]
				} else if (getParentKey(key, node.children)) {
					parentKey = getParentKey(key, node.children)
				}
			}
		}
		return parentKey
	}
	// const selectId = ref('')
	const onSelect = (selectedKeys, data) => {
		if (selectedKeys.length === 0) {
			if (Props.selectLeastOne) {
				treeSelectAttr.selectedKeys.push(data.node.id)
				return
			}
		}
		// 无父级，就是顶级节点
		if (data.node.parent) {
			map(data.node.parent?.node.children, (item, index) => {
				item.index = index
				item.key = item[treeFieldNames.key]
			})
		} else {
			const arr = map(treeObj.value.data, (item, index) => {
				item.index = index
				item.key = item[treeFieldNames.key]
				return item
			})
			data.node.parent = {
				node: {
					children: arr,
					id: data.node.parentId,
					key: data.node.parentId
				}
			}
		}
		let obj = {
			selectedKeys: selectedKeys,
			data: data
		}
		// if (selectedKeys.length > 0) {
		// 	selectId.value = selectedKeys[0]
		// 	// if (treeSelectAttr.expandedKeys.includes(selectId.value)) {
		// 	// 	treeSelectAttr.expandedKeys = treeSelectAttr.expandedKeys.filter((item) => item !== selectId.value)
		// 	// 	console.log('关闭', treeSelectAttr.expandedKeys)
		// 	// } else {
		// 	// 	treeSelectAttr.expandedKeys.push(selectId.value)
		// 	// }
		// } else {
		// 	// treeSelectAttr.expandedKeys = treeSelectAttr.expandedKeys.filter((item) => item !== selectId.value)
		// }
		// autoExpandParent.value = false
		emit('treeSelect', obj)
	}
	// 展开收起
	const onExpand = (keys) => {
		treeSelectAttr.expandedKeys = keys
		autoExpandParent.value = false
	}
	const searchLoading = ref(false)

	const searchChange = async (e) => {
		const value = e.target.value
		if (value) {
			searchLoading.value = true
			await searchFn(value)
		} else {
			searchLoading.value = false
			elTreeRef.value.scrollTo({ offset: 0 })
		}
	}
	// 模糊搜索只搜索20个
	const getExpandedKeys = async (value) => {
		let arr = []
		dataList.map((item, index) => {
			if (item[Props.title].indexOf(value) > -1 && arr.length < 20) {
				arr.push(getParentKey(item[treeFieldNames.key], treeObj.value.data))
			}
		})
		return [...new Set(arr)]
	}
	const searchFn = debounce(async (value) => {
		if (Props.isAsync) {
			await debounceAsyncSearch(value)
		} else if (value) {
			const expanded = await getExpandedKeys(value)
			const node = dataList.find((item) => item[Props.title].indexOf(value) > -1)
			treeSelectAttr.expandedKeys = expanded
			autoExpandParent.value = true
			nextTick(() => {
				expanded?.length && elTreeRef.value.scrollTo({ key: node[Props['keys']] })
				searchLoading.value = false
			})
		}
	}, 500)
	const debounceAsyncSearch = debounce(async (value) => emit('asyncSearch', value), 500)

	// watch(
	// 	() => searchValue.value,
	// 	(value) => {
	// 		// searchValue.value = value
	// 		if (Props.isAsync) {
	// 			debounceAsyncSearch(value)
	// 		} else if (value) {
	// 			const expanded = dataList.map((item) => {
	// 				if (item[Props.title].indexOf(value) > -1) {
	// 					// if (item.title.indexOf(value) > -1) {
	// 					return getParentKey(item[treeFieldNames.key], treeObj.data)
	// 				}
	// 				return null
	// 			})
	// 			// .filter((item, i, self) => item && self.indexOf(item) === i)
	// 			console.log('搜索', expanded)
	// 			treeSelectAttr.expandedKeys = expanded
	// 			autoExpandParent.value = true
	// 		} else {
	// 			// expandedKeys = Attrs.selectedKeys.length > 0 ? Attrs.selectedKeys : []
	// 			// autoExpandParent.value = Attrs.selectedKeys.length > 0
	// 		}
	// 	}
	// )

	const getAllpid = async (id) => {
		if (flatTreeMap.value[id]?.parentId) {
			if (!treeSelectAttr.expandedKeys.includes(flatTreeMap.value[id]?.parentId))
				treeSelectAttr.expandedKeys.push(flatTreeMap.value[id]?.parentId)
			await getAllpid(flatTreeMap.value[id]?.parentId)
		}
	}
	const autoSelectTree = async (value) => {
		if (value) {
			autoExpandParent.value = true
			treeSelectAttr.expandedKeys = []
			await getAllpid(value)
			treeSelectAttr.expandedKeys = treeSelectAttr.expandedKeys.reverse().filter((item) => item != '0')
			treeSelectAttr.selectedKeys = [value]
			console.log(flatTreeMap.value[value], '===flatTreeMap.value[flatTreeMap.value[value]]===')
			// flatTreeMap.value[flatTreeMap.value[value]].map((item, index) => {
			// 	item.key = item[treeFieldNames.key]
			// 	item.index = index
			// 	return item
			// })
			// const parentObj = {
			// 	parent: { node: flatTreeMap.value[flatTreeMap.value[value].parentId] }
			// }
			emit('autoSelectNodeData', {
				...flatTreeMap.value[value]
				// ...parentObj
			})
			nextTick(() => {
				elTreeRef.value && elTreeRef.value.scrollTo({ key: value })
			})
		} else {
			treeSelectAttr.expandedKeys = [Props.treeData[0][Props.keys]]
			// elTreeRef.value.scrollTo({ offset: 0 })
			autoExpandParent.value = false
		}
	}

	const treeBox = ref()
	const treeType = ref()
	const searchBoxRef = ref()
	const treeHeight = ref(200)
	const getParentHeight = () => {
		let num = Props.search ? searchBoxRef.value.offsetHeight + 8 : 0
		if (slotKeys.value.includes('treeType')) num += 42
		treeHeight.value = treeBox.value && treeBox.value.offsetHeight - num - 24
	}
	onMounted(() => {
		nextTick(() => {
			console.log(111, treeType.value)
		})
		getParentHeight()
		setTimeout(() => getParentHeight(), 500)
		window.addEventListener('resize', getParentHeight)
	})
	onUnmounted(() => {
		window.removeEventListener('resize', getParentHeight)
	})
	defineExpose({
		treeCollapse
	})
</script>

<style lang="less" scoped>
	@import '@/components/Tree/tree.less';
	:deep(.ant-tree-node-content-wrapper) {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.search-box {
		:deep(.ant-input-affix-wrapper) {
			border-radius: 20px;
		}
	}
	:deep(.ant-select-selector) {
		border-radius: 50px !important;
	}
	:deep(.ant-spin-nested-loading) {
		height: 100%;
		.ant-spin-container {
			height: 100%;
		}
	}
	.dragBox {
		background: url(/src/assets/images/cms/dragIcon.png) repeat-y center center;
		background-size: 10px;
	}
</style>

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/entityold/` + url, ...arg)

export default {
	syncUser() {
		return request('syncUser', {}, 'post')
	},
	syncToSysOrg() {
		return request('syncToSysOrg', {}, 'post')
	},
	syncNoHandelData() {
		return request('syncNoHandelData', {}, 'post')
	},
	syncData(data) {
		return request('syncData', data, 'post')
	},
	importExcel(data) {
		return request('importExcel', data)
	},
	clearImportedData(data) {
		return request('clearImportedData', data, 'post')
	},
	decryptFile() {
		return request('decryptFile', {}, 'post')
	},
	handelDisposalData() {
		return request('handelDisposalData', {}, 'get')
	}
}

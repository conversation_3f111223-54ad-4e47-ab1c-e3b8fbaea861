import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/org/` + url, ...arg)

const requestCode = (url, ...arg) => baseRequest(`/api/webapp/sys/org/` + url, ...arg)

export default {
	// 获取组织分页
	orgPage(data) {
		return request('page', data, 'get')
	},
	// 获取组织列表
	orgList(data) {
		return request('list', data, 'get')
	},
	// 获取组织树
	orgTree(data) {
		return request('tree', data, 'get')
	},
	// 提交表单 edit为true时为编辑，默认为新增
	submitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除组织
	orgDelete(data) {
		return request('delete', data)
	},
	// 获取组织详情
	orgDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取组织树选择器
	orgOrgTreeSelector(data) {
		return request('orgTreeSelector', data, 'get')
	},
	// 获取用户选择器
	orgUserSelector(data) {
		return request('userSelector', data, 'get')
	},
	// 获取异步加载树默认的树形结构
	getDefaultTree(data) {
		return request('getDefaultTree', data, 'get')
	},
	// 通过parentId获取所有的下级机构列表
	getBizOrgListByParentId(data) {
		return request('getBizOrgListByParentId', data, 'get')
	},
	// 通过条件查询机构列表
	getBizOrgListByCondition(data) {
		return request('getBizOrgListByCondition', data, 'post')
	},
	// 用户中心获取默认的树形结构(用户中心页面专用)
	userCenterGetDefaultTree(data) {
		return request('usercenter/getDefaultTree', data, 'get')
	},
	// 用户中心通过parentId获取所有的下级机构列表(用户中心页面专用)
	userCenterGetBizOrgListByParentId(data) {
		return request('usercenter/getBizOrgListByParentId', data, 'get')
	},
	userCenterBuildTreeFromNodes(data) {
		return request(`usercenter/buildTreeFromNodes`, data, 'get')
	},
	getOrgTree(data) {
		return requestCode(`tree`, data, 'get')
	},
	getWebBizOrgListByParentId(data) {
		return requestCode(`getBizOrgListByParentId`, data, 'get')
	},
	bizOrgPage(data) {
		return requestCode(`page`, data, 'get')
	},
	buildTreeFromNodes(data) {
		return requestCode(`buildTreeFromNodes`, data, 'get')
	},
	getParentListBySubstationOrgId(data) {
		return requestCode(`getParentListBySubstationOrgId`, data, 'get')
	},
	usercenterBuildTreeFromNodes(data)	{
		return requestCode(`usercenter/buildTreeFromNodes`, data, 'get')
	}
}

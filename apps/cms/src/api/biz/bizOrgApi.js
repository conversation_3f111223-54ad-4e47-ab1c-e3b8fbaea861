/**
 *  Copyright [2022] [https://www.xiaonuo.vip]
 *	Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *	1.请不要删除和修改根目录下的LICENSE文件。
 *	2.请不要删除和修改Snowy源码头部的版权声明。
 *	3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 *	4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 *	5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 *	6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/org/` + url, ...arg)
/**
 * 机构
 *
 * <AUTHOR>
 * @date 2022-09-22 22:33:20
 */
export default {
	// 获取机构分页
	orgPage(data) {
		return request('page', data, 'get')
	},
	// 获取机构列表
	orgList(data) {
		return request('list', data, 'get')
	},
	// 获取机构树
	orgTree(data) {
		return request('tree', data, 'get')
	},
	// 提交表单 edit为true时为编辑，默认为新增
	submitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除机构
	orgDelete(data) {
		return request('delete', data)
	},
	// 获取机构详情
	orgDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取机构树选择器
	orgTreeSelector(data) {
		return request('orgTreeSelector', data, 'get')
	},
	// 获取人员选择器
	orgUserSelector(data) {
		return request('userSelector', data, 'get')
	},
	// 获取动态字段的配置
	orgDynamicFieldConfigList(data) {
		return request('dynamicFieldConfigList', data, 'get')
	},
	getTreeById(id) {
		return request('getTreeById', { id }, 'get')
	},
	getChildTreeById(id) {
		return request('getChildTreeById', { id }, 'get')
	},
	// 获取异步加载树默认的树形结构
	getDefaultTree(data) {
		return request('getDefaultTree', data, 'get')
	},
	// 通过parentId获取所有的下级机构列表
	getBizOrgListByParentId(data) {
		return request('getBizOrgListByParentId', data, 'get')
	},
	// 通过机构ID获取机构列表树
	getBizOrgTreeByOrgId(orgId) {
		return request(`getBizOrgTreeByOrgId?orgId=${orgId}`, {}, 'post')
	},
	// 通过条件查询机构列表
	getBizOrgListByCondition(data) {
		return request('getBizOrgListByCondition', data, 'post')
	},
	// 通过企业站点对应的机构，获取本级机构及以下的树
	getBizOrgTreeBySubstationId(data) {
		return request('getBizOrgTreeBySubstationId', data, 'post')
	},
	getParentListBySubstationOrgId(data) {
		return request('usercenter/getParentListBySubstationOrgId', data, 'get')
	},
	buildTreeFromNodes(data) {
		return request('usercenter/buildTreeFromNodes', data, 'get')
	},
	buildSocialTreeFromNodes(data) {
		return request('usercenter/buildSocialTreeFromNodes', data, 'get')
	},
	getParentListBySubstationOrgIdAllOrg(data) {
		return request('getParentListBySubstationOrgIdAllOrg', data, 'get')
	},
	getOrgPageByConditionAllOrg(data) {
		return request('getOrgPageByConditionAllOrg', data, 'get')
	},
	buildSubtreeSelectorNodesAllOrg(data) {
		return request('buildSubtreeSelectorNodesAllOrg', data, 'get')
	},
	getParentListByOrgIdAllOrg(data) {
		return request('getParentListByOrgIdAllOrg', data, 'get')
	},
	getOrgListByParentId(data) {
		return request('getOrgListByParentId', data, 'get')
	}
}

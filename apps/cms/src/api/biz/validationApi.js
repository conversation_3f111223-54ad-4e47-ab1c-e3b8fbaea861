import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/validation/` + url, ...arg)

export default {
	// 详情
	validationInfo(data) {
		return request('validationInfo', data, 'get')
	},
	// 校验
	verifyBusinessInfo(data) {
		return request('verifyBusinessInfo', data, 'get')
	},
	// 导出
	exportValidationInfo(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportValidationInfo', data, 'get', options)
	}
}

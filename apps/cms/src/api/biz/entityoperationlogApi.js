import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/entityoperationlog/` + url, ...arg)

export default {
	// 操作日志列表
	getData(data) {
		return request('page', data, 'get')
	},
	// 操作日志详情
	getDetail(data) {
		return request('detail', data, 'get')
	},
	// 导出
	export(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'get', options)
	},
	userSelector(data) {
		return request('userSelector', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/entitycategory/` + url, ...arg)

export default {
	// 行业小类
	getPage(data) {
		return request('page', data, 'get')
	},
	// 行业小类列表
	getList(data) {
		return request('list', data, 'get')
	},
	// 行业小类字典查询
	categoryCodeList(data) {
		return request('categoryCodeList', data)
	},
	downFile(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downloadIndustryTemplate', data, 'get', options)
	}
}

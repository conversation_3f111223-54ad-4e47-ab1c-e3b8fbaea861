import { baseRequest } from '@/utils/request'
import { getTargetElement } from 'vue-hooks-plus/lib/utils/domTarget'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/portrait/` + url, ...arg)

export default {
	// 股权关系树
	getRelationTree (data) {
		return request('relationTree', data, 'get')
	},
	// 主表详情
	getDetail(data) {
		return request('detail', data, 'get')
	},
	// 管理职能-瘦身健体-返回机构的处置公司集合
	getDispose(data) {
		return request('dispose', data, 'get')
	},
	exportDispose(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportDispose', data, 'get', options)
	},
	// 董监高详情
	getManager(data) {
		return request('manager', data, 'get')
	},
	// 经营数据
	getProfitData(data) {
		return request('getProfitData', data, 'get')
	},
	relationTree(data) {
		return request('relationTree', data, 'get')
	},
	manageTree(data) {
		return request('manageTree', data, 'get')
	},
	allManageTree(data) {
		return request('allManageTree', data, 'get')
	},
	groupManageTree(data) {
		return request('groupManageTree', data, 'get')
	},
	institutionInfo(data) {
		return request('institutionInfo', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/entitydisposal/` + url, ...arg)

export default {
	getList(data) {
		return request('getList', data, 'get')
	},
	isTrueByOrgDisposal(data) {
		return request('isTrueByOrgDisposal', data, 'get')
	},
	save(data) {
		return request('saveEntitydisposal', data)
	},
	recovery(data) {
		return request('recoveryDisposal', data)
	},
	isTrueRemoveByOrgDisposal(data) {
		return request('isTrueRemoveByOrgDisposal', data, 'get')
	}
}

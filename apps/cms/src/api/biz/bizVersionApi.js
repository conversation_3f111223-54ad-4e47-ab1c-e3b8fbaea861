import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/version/` + url, ...arg)

export default {
	listYearMonth(data) {
		return request('listYearMonth', data, 'get')
	},
	// 获取版本列表
	getVersionList(data) {
		return request('page', data, 'get')
	},
	// 归档
	archive(data) {
		return request('archive', data, 'post')
	},
	// 归档确认
	confirmArchive(data) {
		return request('confirmArchive', data, 'post')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/entityprocess/` + url, ...arg)

export default {
	// 本机下级加锁
	lockOrg(data) {
		return request('lockOrg', data)
	},
	// 批量加锁
	batchLockOrg(data) {
		return request('batchLockOrg', data)
	},
	// 批量解锁
	batchUnLockOrg(data) {
		return request('batchUnLockOrg', data)
	},
	// 分页
	getList(data) {
		return request('getList', data, 'get')
	},
	// 获取日志列表
	getLogList(data) {
		return request('getLogList', data, 'get')
	}
}

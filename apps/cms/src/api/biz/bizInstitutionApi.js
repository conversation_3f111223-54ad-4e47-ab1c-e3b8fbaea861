import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/institution/` + url, ...arg)

export default {
	// 获取架构树 --1
	getStructureTree(data) {
		return request('getStructureList', data, 'get')
	},
	// 获取架构列表（平铺展示，不是树结构） --2
	getFillingList(data) {
		return request('getFillingList', data, 'get')
	},
	getStructureOrgnList(data) {
		return request('getStructureOrgnList', data, 'get')
	},
	// 编辑主表 -- 0
	editForm(data) {
		return request('edit', data)
	},
	// 删除 -- 0
	delete(data) {
		return request('delete', data)
	},
	// 新增 -- 0
	addForm(data) {
		return request('add', data)
	},
	// 主表分页 -- 0
	getPage(data) {
		return request('page', data, 'get')
	},
	// 主表详情 -- 1
	getDetail(data) {
		return request('detail', data, 'get')
	},
	// 查询当期版本信息（返回日期）
	getCurVersionDate(data) {
		return request('getCurVersionDate', data, 'get')
	},
	// 基本信息新增，编辑
	addOrEdit(data) {
		return request('addOrUpdShEntity', data)
	},
	// 基本信息新增，获取法人层级
	getLegalEntityLevel(data) {
		return request('getLegalEntityLevel', data, 'get')
	},
	// 历史对比列表
	historyCompare(data) {
		return request('historyCompare/list', data, 'get')
	},
	// 历史对比导出
	historyCompareExport(data) {
		const options = {
			responseType: 'blob'
		}
		return request('historyCompare/export', data, 'get', options)
	},
	// 历史对比-法人层级数据差异
	getCompLegalLevel(data) {
		return request('historyCompare/getCompLegalLevel', data, 'get')
	},
	// 历史对比-管理层级数据差异
	getCompManagementLevel(data) {
		return request('historyCompare/getCompManagementLevel', data, 'get')
	},
	// 历史对比-关注字段变更数据差异
	getCompFieldChange(data) {
		return request('historyCompare/getCompFieldChange', data, 'get')
	},
	// 历史对比-工商信息数据差异
	getCompBusiness(data) {
		return request('historyCompare/getCompBusiness', data, 'get')
	},
	// 历史对比-股权关系数据差异
	getCompOrgRelation(data) {
		return request('historyCompare/getCompOrgRelation', data, 'get')
	},
	// 历史对比-管理职能数据差异
	getCompManageFunction(data) {
		return request('historyCompare/getCompManageFunction', data, 'get')
	},
	// 历史对比-行业数据差异
	getEntityCategoryComp(data) {
		return request('historyCompare/getEntityCategoryComp', data, 'get')
	},
	moveNode(data) {
		return request('handleOrgOrderNo', data)
	},
	getSelectVersionId(data) {
		return request('getSelectVersionId', data, 'get')
	},
	exportShEntityInfo(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportShEntityInfo', data, 'get', options)
	},
	isParentunitByOrg(data) {
		return request('isParentunitByOrg', data, 'get')
	},
	getFillingByGDVersion(data) {
		return request('getFillingByGDVersion', data, 'get')
	},
	getOrgList(data) {
		return request('getOrgList', data, 'get')
	},
	getManageCompany(data) {
		return request('getManageCompany', data, 'get')
	},
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/infoStatistics/` + url, ...arg)

export default {
	queryManagementSubjects(data) {
		return request('queryManagementSubjects', data)
	},
	exportqueryManagementSubjects(data) {
		return request('exportqueryManagementSubjects', data, 'post', {
			responseType: 'blob'
		})
	},
	exportSubsidiaries(data) {
		return request('exportSubsidiaries', data, 'post', {
			responseType: 'blob'
		})
	},
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/validation/` + url, ...arg)

export default {
	// 获取架构树 --1
	queryValidationInfo(data) {
		return request('queryValidationInfo', data, 'get')
	},
	// 获取工商管理信息查询是否校验成功
	checkVerifyStatus(data) {
		return request('checkVerifyStatus', data, 'get')
	},
	validationInfoDetails(data) {
		return request('validationInfoDetails', data, 'get')
	}
}

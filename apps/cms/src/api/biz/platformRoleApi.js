import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/user/platformRole/` + url, ...arg)

export default {
	// 新增权限人员
	add(data) {
		return request('add', data, 'post')
	},
	// 删除权限人员
	delete(data) {
		return request('delete', data, 'post')
	},
	addUserList(data) {
		return request('addUserList', data, 'post')
	},
	addWithRole(data) {
		return request('addWithRole', data, 'post')
	},
	getSubstationIdByOrgId(data) {
		return request('getSubstationIdByOrgId', data, 'get')
	}
}

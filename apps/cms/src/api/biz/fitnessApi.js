import { baseRequest } from '@/utils/request'
import bizUserApi from '@/api/biz/bizUserApi'
import downloadUtil from '@/utils/downloadUtil'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/fitness/` + url, ...arg)

export default {
	// 集团参股企业数量
	getHoldingCompany (data) {
		return request('getHoldingCompany', data)
	},
	holdingCompanyExport(data) {
		return request('export/holdingCompany', data, 'post', {
			responseType: 'blob'
		})
	},
	getConsolidatedSubsidiaryDistribution (data) {
		return request('getConsolidatedSubsidiaryDistribution', data,'get')
	},
	consolidatedSubsidiaryDistributionExport(data) {
		return request('export/consolidatedSubsidiaryDistribution', data, 'get', {
			responseType: 'blob'
		})
	},
	getDispositionDetails (data) {
		return request('getDispositionDetails', data)
	},
	dispositionDetails(data) {
		return request('export/dispositionDetails', data, 'post', {
			responseType: 'blob'
		})
	},
	getParticipationDetails (data) {
		return request('getParticipationDetails', data)
	},
	participationDetails(data) {
		return request('export/participationDetails', data, 'post', {
			responseType: 'blob'
		})
	},
	getSubsidiaryDisposalDetails(data) {
		return request('getSubsidiaryDisposalDetails', data)
	},
	subsidiaryDisposalDetails(data) {
		return request('export/subsidiaryDisposalDetails', data, 'post', {
			responseType: 'blob'
		})
	},
	getLegalPersonDecompression(data) {
		return request('getLegalPersonDecompression', data)
	},
	legalPersonDecompression(data) {
		return request('export/legalPersonDecompression', data, 'post', {
			responseType: 'blob'
		})
	},
	getJointStockCompany(data) {
		return request('getJointStockCompany', data)
	},
	jointStockCompany(data) {
		return request('export/jointStockCompany', data, 'post', {
			responseType: 'blob'
		})
	},
	getSubstantiveManagement(data) {
		return request('getSubstantiveManagement', data)
	},
	substantiveManagement(data) {
		return request('export/substantiveManagement', data, 'post', {
			responseType: 'blob'
		})
	},
	getIndustry(data) {
		return request('getIndustry', data)
	},
	industry(data) {
		return request('export/industry', data, 'post', {
			responseType: 'blob'
		})
	},
	getConsolidatedTableSpv(data) {
		return request('getConsolidatedTableSpv', data)
	},
	consolidatedTableSpv(data) {
		return request('export/consolidatedTableSpv', data, 'post', {
			responseType: 'blob'
		})
	},
	getParticipationSpv(data) {
		return request('getParticipationSpv', data)
	},
	participationSpv(data) {
		return request('export/participationSpv', data, 'post', {
			responseType: 'blob'
		})
	},
	getSubstantiveManagementSpv(data) {
		return request('getSubstantiveManagementSpv', data)
	},
	substantiveManagementSpv(data) {
		return request('export/substantiveManagementSpv', data, 'post', {
			responseType: 'blob'
		})
	},
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsDataBoard/` + url, ...arg)

export default {
	// 获取工会基本信息
	getBasicInfo(data) {
		return request('getBasicInfo', data, 'get')
	},
	// 获取工会干部统计
	getUnionsCadresCount(data) {
		return request('getUnionsCadresCount', data, 'get')
	},
	// 获取工会荣誉信息
	getAdvancedInfo(data) {
		return request('getAdvancedInfo', data, 'get')
	},
	// 获取工会荣誉列表信息
	getAdvancedList(data) {
		return request('getAdvancedList', data, 'get')
	},
	// 获取工会会员信息
	getUnionMemberInfo(data) {
		return request('getUnionMemberInfo', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/leagueDataBoard/` + url, ...arg)

const requestOtherUser = (url, ...arg) => baseRequest(`/api/webapp/sys/otheruser/` + url, ...arg)

export default {
	// 获取团青地图信息
	getMapInfo(data) {
		return request('getMapInfo', data, 'get')
	},
	getBasicInfo(data) {
		return request('getBasicInfo', data, 'get')
	},
	// 获取团青列表 组织结构
	getOrgStructure(data) {
		return request('getOrgStructure', data, 'get')
	},
	getList(data) {
		return requestOtherUser('list', data, 'get')
	}
}

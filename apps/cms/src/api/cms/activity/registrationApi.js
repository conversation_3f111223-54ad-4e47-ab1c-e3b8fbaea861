import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/activity/registration/` + url, ...arg)

export default {
	// 获取活动报名分页
	activityPage(data) {
		return request('page', data, 'get')
	},
	activitySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除活动
	activityDelete(data) {
		return request('delete', data)
	},
	// 获取活动报名详情
	activityEnrollDetail(data) {
		return request('detail', data, 'get')
	},
	// 结束活动
	activityFinish(data) {
		return request('finish', data)
	},
	// 导出
	activityExportReport(data) {
		return request('exportReport', data, 'get', {
			responseType: 'blob'
		})
	},
	// 获取活动动态字段的配置
	activityDynamicFieldConfigList(data) {
		return request('dynamicFieldConfigList', data, 'get')
	},
	// 修改评选状态
	activityEditRegistration(data) {
		return request('editRegistration', data, 'post')
	}
}

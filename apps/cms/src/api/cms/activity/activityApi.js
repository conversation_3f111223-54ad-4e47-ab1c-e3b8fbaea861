import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/activity/activity/` + url, ...arg)

/**
 * 活动Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/10/10 15:54
 **/
export default {
	// 获取活动分页
	activityPage(data) {
		return request('page', data, 'get')
	},
	// 提交活动表单 edit为true时为编辑，默认为新增
	activitySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除活动
	activityDelete(data) {
		return request('delete', data)
	},
	// 获取活动详情
	activityDetail(data) {
		return request('detail', data, 'get')
	},
	// 结束活动
	activityFinish(data) {
		return request('finish', data)
	},
	// 导出
	activityExportReport(data) {
		return request('exportReport', data, 'get', {
			responseType: 'blob'
		})
	},
	// 获取活动动态字段的配置
	activityDynamicFieldConfigList(data) {
		return request('dynamicFieldConfigList', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/activity/carousel/` + url, ...arg)

/**
 * 活动Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/10/10 15:54
 **/
export default {
	// 获取活动分页
	bannerPage(data) {
		return request('page', data, 'get')
	},
	// 提交活动表单 edit为true时为编辑，默认为新增
	bannerSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除活动
	bannerDelete(data) {
		return request('delete', data)
	},
	// 获取活动详情
	bannerDetail(data) {
		return request('detail', data, 'get')
	}
}

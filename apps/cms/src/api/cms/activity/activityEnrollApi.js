import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/activity/meetingregistration/` + url, ...arg)

/**
 * 活动报名Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/10/10 15:54
 **/
export default {
	// 获取活动报名分页
	activityEnrollPage(data) {
		return request('page', data, 'get')
	},
	// 提交活动报名表单 edit为true时为编辑，默认为新增
	activitySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除活动报名
	activityDelete(data) {
		return request('delete', data)
	},
	// 获取活动报名详情
	activityEnrollDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取活动报名动态字段的配置
	activityDynamicFieldConfigList(data) {
		return request('dynamicFieldConfigList', data, 'get')
	},
	// 修改报名
	activityEnrollEdit(data) {
		return request('editRegistration', data, 'post')
	},
	// 活动报名 签到
	activitySign(data) {
		return request('batchSign', data, 'post')
	}
}

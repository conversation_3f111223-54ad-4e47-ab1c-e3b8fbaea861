import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/question/questions/` + url, ...arg)

/**
 * 问题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/23 18:14
 **/
export default {
	// 获取问题表分页
	activityQuestionsPage(data) {
		return request('page', data, 'get')
	},
	// 提交问题表表单 edit为true时为编辑，默认为新增
	activityQuestionsSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除问题表
	activityQuestionsDelete(data) {
		return request('delete', data)
	},
	// 获取问题表详情
	activityQuestionsDetail(data) {
		return request('detail', data, 'get')
	},
	activityQuestionsImport(data) {
		return request('import', data)
	}
}

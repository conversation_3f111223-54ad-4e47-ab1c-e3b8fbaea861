import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/category/` + url, ...arg)
const requestCategory = (url, ...arg) => baseRequest(`/api/cmsapp/cms/cmscategory/` + url, ...arg)

/**
 * 栏目表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/21 14:44
 **/
export default {
	// 获取栏目表分页
	cmsCategoryPage(data) {
		return request('page', data, 'get')
	},
	// 提交栏目表表单 edit为true时为编辑，默认为新增
	cmsCategorySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除栏目表
	cmsCategoryDelete(data) {
		return request('delete', data)
	},
	// 获取栏目表详情
	cmsCategoryDetail(data) {
		return request('detail', data, 'get')
	},
	categoryTree(data, flag) {
		//console.log(flag)
		//console.log(data,'data')
		return requestCategory(flag ? 'submittedTree' : 'tree', data, 'get')
	},

	queryWorkFile(data) {
		return request('queryWorkFile', data, 'get')
	}
}

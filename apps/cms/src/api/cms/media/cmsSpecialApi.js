import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/special/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/23 09:53
 **/
export default {
	// 获取专题表分页
	cmsSpecialPage(data) {
		return request('page', data, 'get')
	},
	// 提交专题表表单 edit为true时为编辑，默认为新增
	cmsSpecialSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除专题表
	cmsSpecialDelete(data) {
		return request('delete', data)
	},
	// 获取专题表详情
	cmsSpecialDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取专题树
	cmsSpecialTree(data) {
		return request('tree', data, 'get')
	}
}

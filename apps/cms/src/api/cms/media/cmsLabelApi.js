import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/label/` + url, ...arg)

/**
 * 标签表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/28 10:02
 **/
export default {
	// 获取标签表分页
	cmsLabelPage(data) {
		return request('page', data, 'get')
	},
	// 获取标签表分页
	cmsLabeltree(data) {
		return request('tree', data, 'get')
	},
	// 提交标签表表单 edit为true时为编辑，默认为新增
	cmsLabelSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除标签表
	cmsLabelDelete(data) {
		return request('delete', data)
	},
	// 获取标签表详情
	cmsLabelDetail(data) {
		return request('detail', data, 'get')
	},
	cmsLabelDeleteSub(data) {
		return request('deleteSub', data)
	}
}

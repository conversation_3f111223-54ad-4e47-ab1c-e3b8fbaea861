import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/videodata/` + url, ...arg)
const requestObj = (url, ...arg) => baseRequest(`/api/cmsapp/cms/videojob/` + url, ...arg)
/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/24 13:53
 **/
export default {
	// 获取视频表分页
	cmsShortVideoPage(data) {
		return request('page', data, 'get')
	},
	// 获取视频表审批分页
	cmsShortVideoAuditpage(data) {
		return request('auditpage', data, 'get')
	},
	// 批量下架短视频
	cmsShortVideoShelves(data) {
		return request('shelves/off', data)
	},
	// 提交定时任务 edit为true时为编辑，默认为新增
	cmsShortVideoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 推送
	cmsTimeReceiver(data, edit = false) {
		return requestObj(edit ? 'edit' : 'add', data)
	},
	//获取视频表详情
	cmsShortVideoDetailForm(data) {
		return request('detail', data, 'get')
	},
	// 删除专题
	cmsShortVideoDelete(data) {
		return request('delete', data)
	}
}

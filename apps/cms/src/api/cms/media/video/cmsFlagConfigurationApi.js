import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/flagconfiguration/` + url, ...arg)

/**
 * 视频号配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/05 14:13
 **/
export default {
	// 获取视频号配置表分页
	cmsFlagConfigurationPage(data) {
		return request('page', data, 'get')
	},
	// 上传短视频时当前用户负责的所有视频号列表
	cmsFlagConfigurationVideoList(data) {
		return request('video/list', data, 'get')
	},
	// 提交视频号配置表表单 edit为true时为编辑，默认为新增
	cmsFlagConfigurationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除视频号配置表
	cmsFlagConfigurationDelete(data) {
		return request('delete', data)
	},
	// 获取视频号配置表详情
	cmsFlagConfigurationDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取视频号树
	getTree(data) {
		return request('tree', data, 'get')
	},
	// 获取视频号树
	shelves(data) {
		return request('shelves', data, 'post')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/flagrelation/` + url, ...arg)

/**
 * 视频号角色关联表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/05 14:20
 **/
export default {
	// 获取视频号角色关联表分页
	cmsFlagRelationPage(data) {
		return request('page', data, 'get')
	},
	// 提交视频号角色关联表表单 edit为true时为编辑，默认为新增
	cmsFlagRelationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除视频号角色关联表
	cmsFlagRelationDelete(data) {
		return request('delete', data)
	},
	// 获取视频号角色关联表详情
	cmsFlagRelationDetail(data) {
		return request('detail', data, 'get')
	},
	getAll(data) {
		return request('list', data, 'get')
	},
	getRoleList(data) {
		return request('roleList', data, 'get')
	}
}

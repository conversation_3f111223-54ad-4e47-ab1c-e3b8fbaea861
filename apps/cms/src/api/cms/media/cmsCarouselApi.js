import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/carousel/` + url, ...arg)
const requestCarouselData = (url, ...arg) => baseRequest(`/api/cmsapp/cms/carouseldata/` + url, ...arg)

/**
 * banner配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/12 18:44
 **/
export default {
	// 获取banner配置表分页
	cmsCarouselPage(data) {
		return request('page', data, 'get')
	},
	// 提交banner配置表表单 edit为true时为编辑，默认为新增
	cmsCarouselSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除banner配置表
	cmsCarouselDelete(data) {
		return request('delete', data)
	},
	// 获取banner配置表详情
	cmsCarouselDetail(data) {
		return request('detail', data, 'get')
	},

	// 获取banner数据源配置表分页
	cmsCarouselDataPage(data) {
		return requestCarouselData('page', data, 'get')
	},
	// 提交banner数据源配置表表单 edit为true时为编辑，默认为新增
	cmsCarouselDataSubmitForm(data, edit = false) {
		return requestCarouselData(edit ? 'edit' : 'add', data)
	},
	// 删除banner数据源配置表
	cmsCarouselDataDelete(data) {
		return requestCarouselData('delete', data)
	},
	// 获取banner数据源配置表详情
	cmsCarouselDataDetail(data) {
		return requestCarouselData('detail', data, 'get')
	}
}

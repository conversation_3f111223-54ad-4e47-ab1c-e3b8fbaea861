import { baseRequest } from '@/utils/request'
const requestData = (url, ...arg) => baseRequest(`/api/cmsapp/cms/article/` + url, ...arg)

const requestAudit = (url, ...arg) => baseRequest(`/api/cmsapp/cms/articlegroupreview/` + url, ...arg)

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/articleaudit/` + url, ...arg)
/**
 * 资讯表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/21 15:03
 **/
export default {
	// 获取资讯表分页
	cmsArticlePage(data) {
		return requestData('page', data, 'get')
	},
	cmsReportArticlePage(data) {
		return requestData('articlePage', data, 'get')
	},
	// 获取资讯表审批分页
	cmsArticleExaminePage(data) {
		return requestData('cmsAuditPage', data, 'get')
	},
	// 下架资讯
	cmsArticleShelves(data) {
		return requestData('shelves', data)
	},
	// 咨询详情页
	cmsArticleDetails(data) {
		return requestData('detail', data, 'get')
	},
	// 提交资讯表表单 edit为true时为编辑，默认为新增
	cmsArticleSubmitForm(data, edit = false) {
		return requestData(edit ? 'edit' : 'add', data)
	},
	// 删除资讯表
	cmsArticleDelete(data) {
		return requestData('delete', data)
	},
	//资讯分类
	cmsArticleArticleDict(data) {
		return requestData('articleDict', data, 'get')
	},
	// 审批资讯表
	cmsArticleStartProcess(data) {
		return requestData('startProcess', data)
	},
	groupRevoke(data) {
		return requestData('groupRevoke', data)
	},
	cmsArticleAudit(data) {
		return requestAudit('groupSubmittedReview', data)
	},
	cmsArticleBatchReview(data) {
		return requestAudit('groupSubmittedBatchReview', data)
	},
	// /cms/articlegroupreview/groupSubmittedBatchReview
	// 报送
	cmsArticleGroup(data) {
		return requestAudit('groupSubmitted', data)
	},
	// // 审批资讯表
	// cmsArticleStartProcess(data) {
	// 	return requestData('startProcess', data)
	// },
	// 批量审批
	cmsArticleAdd(data) {
		return request('add', data)
	},
	// 批量审批
	cmsArticleEdit(data) {
		return request('edit', data)
	},
	cmsArticleSubmitVideo(data) {
		return request('submitVideo', data)
	},
	// /cms/articlegroupreview/groupSubmitted
	// 获取资讯文件
	cmsArticleFileList(data) {
		return requestData('fileList', data, 'get')
	}
}

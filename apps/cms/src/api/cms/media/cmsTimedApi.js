import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/articlejob/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/18 09:53
 **/
export default {
	// 获取专题表分页
	cmsTimedPage(data) {
		return request('page', data, 'get')
	},
	//编辑详情
	cmsTimedDetail(data) {
		return request('detail', data, 'get')
	},
	// 提交定时任务 edit为true时为编辑，默认为新增
	cmsTimeSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	//推送
	cmsTimeReceiverForm(data) {
		return request('receiver', data, 'get')
	},
	// 删除专题
	cmsTimeDelete(data) {
		return request('delete', data)
	}
}

import { baseRequest } from '@/utils/request'

const flwRequest = (url, ...arg) => baseRequest(`/api/flwapp/flw/process/` + url, ...arg)

export default {
	getHistoricApprovalRecordsByBussinessId(data) {
		return flwRequest('getHistoricApprovalRecordsByBussinessId', data, 'get')
	},
	getHistoricApprovalRecordsByBussinessIdAndApporvalType(data) {
		return flwRequest('getHistoricApprovalRecordsByBussinessIdAndApporvalType', data, 'get')
	}
}

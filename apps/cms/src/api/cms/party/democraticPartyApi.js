import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/democraticparties/` + url, ...arg)
const requestData = (url, ...arg) => baseRequest(`/api/webapp/sys/democraticpartiesrel/` + url, ...arg)
/**
 * 组织表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/15 14:12
 **/
export default {
	// 获取组织表分页
	partyPage(data) {
		return request('page', data, 'get')
	},
	// 提交组织表表单 edit为true时为编辑，默认为新增
	partySubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除组织表
	partyDelete(data) {
		return request('delete', data)
	},
	// 获取组织表详情
	partyDetail(data) {
		return request('detail', data, 'get')
	},
	//获取组织历史记录
	partyHistory(data) {
		return request('history', data, 'get')
	},
	// ------------------------
	// 获取组织树
	tree(data) {
		return request('tree', data, 'get')
	},
	//党派组织信息
	partyInfoPage(data) {
		return requestData('page', data, 'get')
	},
	//删除党派组织信息
	partyInfoDelete(data) {
		return requestData('delete', data)
	},
	//添加、编辑党派组织信息
	partyInfoSubmitForm(data, edit = false) {
		return requestData(edit ? 'edit' : 'add', data)
	},
	// 党派组织信息导出
	export(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'get', options)
	}
}

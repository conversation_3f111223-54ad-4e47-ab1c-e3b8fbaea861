import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/otheruser/` + url, ...arg)

/**
 * 其他统战人士Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/08 15:59
 **/
export default {
	// 获取其他统战人士分页
	democraticOtherUserPage(data) {
		return request('page', data, 'get')
	},
	// 提交其他统战人士表单 edit为true时为编辑，默认为新增
	democraticOtherUserSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除其他统战人士
	democraticOtherUserDelete(data) {
		return request('delete', data)
	},
	// 获取其他统战人士详情
	democraticOtherUserDetail(data) {
		return request('detail', data, 'get')
	}}

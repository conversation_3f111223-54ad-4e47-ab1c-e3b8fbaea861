import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/cadremanage/` + url, ...arg)

/**
 * 统战干部管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/08 10:44
 **/
export default {
	// 获取统战干部管理分页
	democraticCadreManagePage(data) {
		return request('page', data, 'get')
	},
	// 提交统战干部管理表单 edit为true时为编辑，默认为新增
	democraticCadreManageSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除统战干部管理
	democraticCadreManageDelete(data) {
		return request('delete', data)
	},
	// 获取统战干部管理详情
	democraticCadreManageDetail(data) {
		return request('detail', data, 'get')
	}}

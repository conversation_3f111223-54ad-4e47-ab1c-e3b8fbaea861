import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/democraticpartiesuser/` + url, ...arg)

/**
 * 成员表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/15 14:18
 **/
export default {
	// 获取成员表分页
	democraticPartyUserPage(data) {
		return request('page', data, 'get')
	},
	// 提交成员表表单 edit为true时为编辑，默认为新增
	democraticPartyUserSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除成员表
	democraticPartyUserDelete(data) {
		return request('delete', data)
	},
	// 获取成员表详情
	democraticPartyUserDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取团员变更记录
	history(data) {
		return request('history', data, 'get')
	},
	// 成员信息导出
	export(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'get', options)
	}
}

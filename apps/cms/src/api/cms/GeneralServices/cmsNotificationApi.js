import { baseRequest } from '@/utils/request'

const requestData = (url, ...arg) => baseRequest(`/api/webapp/sys/dissemination/` + url, ...arg)
const request = (url, ...arg) => baseRequest(`/api/webapp/sys/distcollect/` + url, ...arg)
const requestDistnotifycation = (url, ...arg) => baseRequest(`/api/webapp/sys/distnotifycation/` + url, ...arg)
const requestDownload = (url, ...arg) => baseRequest(`/api/webapp/sys/distrecord/` + url, ...arg)
/**
 * banner配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/10 15:44
 **/
export default {
	//获取文件传达列表
	cmsNotificationPage(data) {
		return requestData('page', data, 'get')
	},
	//获取分类
	cmsNotificationCategory(data) {
		return requestData('get/category', data, 'get')
	},
	//删除文件传达列表
	cmsNotificationDelete(data) {
		return requestData('delete', data)
	},
	//文件传达详情
	cmsNotificationDetail(data) {
		return requestData('detail', data, 'get')
	},
	// 提交文件传达表表单 edit为true时为编辑，默认为新增
	cmsNotificationSubmitForm(data, edit = false) {
		return requestData(edit ? 'edit' : 'add', data)
	},
	//添加通知名单
	cmsNotificationGetPersonal(data) {
		return requestData('import/getPersonal', data, 'get')
	},
	//导入自定义名单
	cmsNotificationCustomizePersonal(data) {
		return requestData('import/customizePersonal', data)
	},
	//获取文件传达默认的收集表
	cmsNotificationCustomizeDefault(data) {
		return request('default', data, 'get')
	},
	//下载自定义名单
	cmsNotificationCustomizeTemplate(data) {
		const options = {
			responseType: 'blob'
		}
		return requestData('download/customizeTemplate', data, 'get', options)
	},
	downloadFile(data) {
		const options = {
			responseType: 'blob'
		}
		return requestData('download/file', data, 'get', options)
	},
	batchExport(data) {
		const options = {
			responseType: 'blob'
		}
		return requestDownload('batch/export', data, 'post', options)
	},
	//校验通知名单是否重复
	cmsNotificationCheckCustomNotifyNameIsExist(data) {
		return requestDistnotifycation('checkCustomNotifyNameIsExist', data, 'get')
	},
	getNotifyUserList(data) {
		return requestDistnotifycation('getNotifyUserList', data, 'get')
	},
	sendDocToNewAddUser(data) {
		return requestData('sendDocToNewAddUser', data)
	},
	//通过用户ID和分类查询该用户在各企业中的指定角色分布情况
	getUserRoleDistributionByUserIdAndCategory(data) {
		return requestDistnotifycation('getUserRoleDistributionByUserIdAndCategory', data, 'get')
	},
	//保存自定义通知名单(新建通知名单)
	cmsNotificationCustomNotifylist(data) {
		return requestDistnotifycation('save/customNotifylist', data)
	},
	cmsNotificationCheckCustomNotifyNameEdit(data) {
		return requestDistnotifycation('edit', data)
	},
	//通过通知名单id获取通知名单中用户id列表
	cmsNotificationUserIdListByNotifyId(data) {
		return requestDistnotifycation('get/notifyInfo', data, 'get')
	},
	//通过通知名单id获取通知名单中用户id列表
	grantNotifyUserRole(data) {
		return requestDistnotifycation('grantNotifyUserRole', data, 'get')
	},
	//获取文件传达通知名单分页 (选择通知名单列表)
	cmsNotificationDistnotifycatioPage(data) {
		return requestDistnotifycation('get/notifylist', data)
	},
	//删除文件传达通知名单
	cmsNotificatiDelete(data) {
		return requestDistnotifycation('delete', data)
	},
	downloadImportCollectTableTemplate() {
		return request('downloadImportCollectTableTemplate', {}, 'get', {
			responseType: 'blob'
		})
	},
	importCollectTable(data) {
		return request('importCollectTable', data)
	},
	downloadErrorCollectTable(data) {
		return request('downloadErrorCollectTable', data, 'get', {
			responseType: 'blob'
		})
	},
	cmsNotificationEditDeadline(data) {
		return requestData('editDeadline', data)
	}
}

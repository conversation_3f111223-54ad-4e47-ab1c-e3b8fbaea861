import { baseRequest } from '@/utils/request'

const requestData = (url, ...arg) => baseRequest(`/api/webapp/sys/feedback/` + url, ...arg)
/**
 * banner配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/2 15:44
 **/
export default {
	//获取建言献策列表
	cmsOfferPage(data) {
		return requestData('page', data, 'get')
	},
	//详情建言献策列表
	cmsOfferDetail(data) {
		return requestData('detail', data, 'get')
	},
	//建言献提交
	cmsOfferCommit(data) {
		return requestData('commit', data)
	},
	//建言献左侧树
	cmsOfferGetFeedbackCategory(data) {
		return requestData('getFeedbackCategory', data, 'get')
	}
}

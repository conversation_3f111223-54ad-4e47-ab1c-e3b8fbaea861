import { baseRequest } from '@/utils/request'

const requestData = (url, ...arg) => baseRequest(`/api/webapp/sys/distrecord/` + url, ...arg)
const request = (url, ...arg) => baseRequest(`/api/webapp/sys/todo/distrecord/` + url, ...arg)
/**
 * banner配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/16 15:44
 **/
export default {
	//获取通知
	cmsProcessingNgetRecordCountByDocDisseminationId(data) {
		return requestData('getRecordCountByDocDisseminationId', data, 'get')
	},
	//获取通知列表
	cmsProcessingNPage(data) {
		return requestData('page', data, 'get')
	},
	//回执情况页面中查看全部回执收集表内容
	getAllCollectContent(data) {
		return requestData('getAllCollectContent', data, 'get')
	},
	//获待取通知列表
	cmsProcessingNRequestNPage(data) {
		return request('page', data, 'get')
	},
	//文件传达详情
	cmsProcessingNDetail(data) {
		return requestData('detail', data, 'get')
	},
	// 提交文件传达表表单 edit为true时为编辑，默认为新增
	cmsProcessingNSubmitForm(data, edit = false) {
		return requestData(edit ? 'edit' : 'add', data)
	},
	//导出回执
	cmsProcessingNExport(data) {
		const options = {
			responseType: 'blob'
		}
		return requestData('batch/export', data, 'post', options)
	},
	//导出收集表
	cmsProcessingAllNExport(data) {
		const options = {
			responseType: 'blob'
		}
		return requestData('batch/export/collect', data, 'post', options)
	}
	// //下载自定义名单
	// cmsNotificationCustomizeTemplate(data) {
	// 	const options = {
	// 		responseType: 'blob'
	// 	}
	// 	return requestData('download/customizeTemplate', data, 'get')
	// }
}

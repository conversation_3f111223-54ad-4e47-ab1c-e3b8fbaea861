import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/feedback/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/08 15:35
 **/
export default {
	// 获取建议表分页
	cmsAcceptancePage(data) {
		return request('page', data, 'get')
	},
	// 获取建议详情
	cmsAcceptanceDetail(data) {
		return request('detail', data, 'get')
	},
	// 提交建议表表单 edit为true时为编辑，默认为新增
	cmsAcceptanceSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 驳回建议详情
	cmsAcceptanceRollback(data) {
		return request('rollback', data, 'get')
	},
	// 上报上级工会受理
	cmsAcceptanceReport(data) {
		return request('report', data)
	},
	// 提交并受理
	cmsAcceptanceCommit(data) {
		return request('commit', data, 'get')
	},
	// 删除
	cmsAcceptanceDelete(data) {
		return request('delete', data, 'post')
	}
}

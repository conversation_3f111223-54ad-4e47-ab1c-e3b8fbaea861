import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/category/` + url, ...arg)
const requestData = (url, ...arg) => baseRequest(`/api/cmsapp/cms/article/` + url, ...arg)

/**
 * 福利配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/10/31 11:28
 **/
export default {
	// 获取工作文件左侧树
	workFilesQueryWorkFileBySource(data) {
		return request('queryWorkFileBySource', data, 'get')
	},
	// 获取工作文件列表
	workFilesQueryWorkFilePage(data) {
		return requestData('queryWorkFilePage', data)
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/project/` + url, ...arg)

/**
 * 帮扶项目Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/13 19:53
 **/
export default {
	// 获取帮扶项目分页
	assistanceProjectPage(data) {
		return request('page', data)
	},
	// 提交帮扶项目表单 edit为true时为编辑，默认为新增
	assistanceProjectSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除帮扶项目
	assistanceProjectDelete(data) {
		return request('delete', data)
	},
	// 获取帮扶项目详情
	assistanceProjectDetail(data) {
		return request('detail', data, 'get')
	},
	// 通过条件查询帮扶项目分页列表
	getAssistanceProjectPageByCondition(data) {
		return request('getAssistanceProjectPageByCondition', data)
	},
	// 通过条件查询帮扶项目列表
	getAssistanceProjectListByCondition(data) {
		return request('getAssistanceProjectListByCondition', data)
	},
	todoTask(data) {
		return request('todoTask', data)
	},
	doneTask(data) {
		return request('doneTask', data)
	},
	exportProjectInfo(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportProjectInfo', data, 'post', options)
	},
	reject(data) {
		return request('reject', data, 'get')
	},
	completedPage(data) {
		return request('completed/page', data, 'post')
	},
	getCurrentLevelAssistanceProjectPageByCondition(data) {
		return request('getCurrentLevelAssistanceProjectPageByCondition', data)
	}
}

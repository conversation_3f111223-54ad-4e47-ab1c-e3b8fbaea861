import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/cadremanagement/` + url, ...arg)

/**
 * 帮扶干部管理表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/30 14:24
 **/
export default {
	// 获取帮扶干部管理表分页
	assistanceCadreManagementPage(data) {
		return request('page', data, 'get')
	},
	// 提交帮扶干部管理表表单 edit为true时为编辑，默认为新增
	assistanceCadreManagementSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除帮扶干部管理表
	assistanceCadreManagementDelete(data) {
		return request('delete', data)
	},
	// 获取帮扶干部管理表详情
	assistanceCadreManagementDetail(data) {
		return request('detail', data, 'get')
	},
	getAssistanceCadreByCondition(data) {
		return request('getAssistanceCadreByCondition', data)
	},
	getAssistanceCadrePageByCondition(data) {
		return request('getAssistanceCadrePageByCondition', data)
	}
}

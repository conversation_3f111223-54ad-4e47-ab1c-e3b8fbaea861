import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/cadreleave/` + url, ...arg)

/**
 * 帮扶干部休假Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/04 09:35
 **/
export default {
	// 获取帮扶干部休假分页
	assistanceCadreLeavePage(data) {
		return request('page', data)
	},
	// 提交帮扶干部休假表单 edit为true时为编辑，默认为新增
	assistanceCadreLeaveSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除帮扶干部休假
	assistanceCadreLeaveDelete(data) {
		return request('delete', data)
	},
	// 获取帮扶干部休假详情
	assistanceCadreLeaveDetail(data) {
		return request('detail', data, 'get')
	}
}

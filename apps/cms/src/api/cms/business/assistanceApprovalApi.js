import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/cadre/manage/` + url, ...arg)

const flwRequest = (url, ...arg) => baseRequest(`/api/flwapp/flw/task/batch/` + url, ...arg)

export default {
	assistanceApprovalTodoTask(data) {
		return request('todoTask', data)
	},
	// 驳回
	assistanceApprovalReject(data) {
		return flwRequest('reject', data)
	},
	// 同意
	assistanceApprovalPass(data) {
		return flwRequest('pass', data)
	},
	reject(data) {
		return request('reject', data, 'get')
	}
}

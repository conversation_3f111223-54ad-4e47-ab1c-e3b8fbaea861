import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/casereport/` + url, ...arg)

/**
 * 帮扶案例报送Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/11 11:42
 **/
export default {
	// 获取帮扶案例报送分页
	assistanceCaseReportPage(data) {
		return request('page', data, 'get')
	},
	// 提交帮扶案例报送表单 edit为true时为编辑，默认为新增
	assistanceCaseReportSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除帮扶案例报送
	assistanceCaseReportDelete(data) {
		return request('delete', data)
	},
	// 获取帮扶案例报送详情
	assistanceCaseReportDetail(data) {
		return request('detail', data, 'get')
	}
}

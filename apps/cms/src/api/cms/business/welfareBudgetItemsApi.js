import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/budgetitems/` + url, ...arg)

/**
 * 社会公益预算细项表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/17 17:26
 **/
export default {
	// 获取社会公益预算细项表分页
	welfareBudgetItemsPage(data) {
		return request('page', data, 'get')
	},
	// 提交社会公益预算细项表表单 edit为true时为编辑，默认为新增
	welfareBudgetItemsSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除社会公益预算细项表
	welfareBudgetItemsDelete(data) {
		return request('delete', data)
	},
	// 获取社会公益预算细项表详情
	welfareBudgetItemsDetail(data) {
		return request('detail', data, 'get')
	},
	// 下载社会公益子项导入模板
	downloadImportTemplate(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downloadImportTemplate', data, 'get', options)
	},
	// 导入预算子项接口
	importBudgetItems(data) {
		return request('importBudgetItems', data)
	},
	// 批量导入执行预算子项接口
	importExcutionBudgetItems(data) {
		return request('importExcutionBudgetItems', data)
	},
	// 计划预算
	downImportErrorMessage(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downImportErrorMessage', data, 'get', options)
	},
	// 执行预算
	downImportExcutionErrorMessage(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downImportExcutionErrorMessage', data, 'get', options)
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/region/` + url, ...arg)

/**
 * 帮扶地区Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/11 10:34
 **/
export default {
	// 获取帮扶地区分页
	assistanceRegionPage(data) {
		return request('page', data, 'get')
	},
	// 提交帮扶地区表单 edit为true时为编辑，默认为新增
	assistanceRegionSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除帮扶地区
	assistanceRegionDelete(data) {
		return request('delete', data)
	},
	// 获取帮扶地区详情
	assistanceRegionDetail(data) {
		return request('detail', data, 'get')
	},
	// 上传帮扶点扩展数据模版
	uploadExtendedDataTemplate(data) {
		return request('uploadExtendedDataTemplate', data, 'get')
	},
	downloadExtendedDataTemplate(data) {
		return request('downloadExtendedDataTemplate', data, 'get')
	},
	getAssistanceRegionPageByCondition(data) {
		return request('getAssistanceRegionPageByCondition', data)
	},
	getAssistanceRegionListByCondition(data) {
		return request('getAssistanceRegionListByCondition', data)
	},
	todoTask(data) {
		return request('todoTask', data)
	},
	doneTask(data) {
		return request('doneTask', data)
	},
	exportRegionInfo(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportRegionInfo', data, 'post', options)
	},
	reject(data) {
		return request('reject', data, 'get')
	},
	isFirstLevelOrg(data) {
		return request('isFirstLevelOrg', data, 'get')
	},
	completedPage(data) {
		return request('completed/page', data, 'get')
	}
}

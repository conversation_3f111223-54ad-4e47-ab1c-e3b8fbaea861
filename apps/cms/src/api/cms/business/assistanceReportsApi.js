import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/reports/` + url, ...arg)

/**
 * 帮扶报告Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/03 19:45
 **/
export default {
	// 获取帮扶报告分页
	assistanceReportsPage(data) {
		return request('page', data)
	},
	// 提交帮扶报告表单 edit为true时为编辑，默认为新增
	assistanceReportsSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除帮扶报告
	assistanceReportsDelete(data) {
		return request('delete', data)
	},
	// 获取帮扶报告详情
	assistanceReportsDetail(data) {
		return request('detail', data, 'get')
	}
}

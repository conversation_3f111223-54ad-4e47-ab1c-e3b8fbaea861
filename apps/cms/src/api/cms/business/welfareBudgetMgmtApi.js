import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/budgetmgmt/` + url, ...arg)

/**
 * 社会公益预算管理表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/17 17:25
 **/
export default {
	// 获取社会公益预算管理表分页
	welfareBudgetMgmtPage(data) {
		return request('page', data, 'get')
	},
	// 提交社会公益预算管理表表单 edit为true时为编辑，默认为新增
	welfareBudgetMgmtSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除社会公益预算管理表
	welfareBudgetMgmtDelete(data) {
		return request('delete', data)
	},
	// 获取社会公益预算管理表详情
	welfareBudgetMgmtDetail(data) {
		return request('detail', data, 'get')
	},
	// 检查机构创建的年度预算是否重复
	checkWelfareBudgetMgmtUnique(data) {
		return request('checkWelfareBudgetMgmtUnique', data, 'get')
	},
	// 导出全部已审批通过的社会公益预算子项列表
	exportSubOrgWelfareBudgetItemList(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportSubOrgWelfareBudgetItemList', data, 'get', options)
	},
	exportWelfareBudgetMgmtList(data) {
		const options = {
			responseType: 'blob'
		}
		return request('exportWelfareBudgetMgmtList', data, 'post', options)
	},
	// 获取已通过审批的下级机构的社会公益预算列表
	getSubOrgWelfareBudgetItemList(data) {
		return request('getSubOrgWelfareBudgetItemList', data, 'get')
	},
	todoTask(data) {
		return request('todoTask', data)
	},
	doneTask(data) {
		return request('doneTask', data)
	},
	reject(data) {
		return request('reject', data, 'get')
	},
	filed(data) {
		return request('filed', data, 'post')
	},
	lowerLevelReject(data) {
		return request('lowerLevel/reject', data, 'get')
	}
}

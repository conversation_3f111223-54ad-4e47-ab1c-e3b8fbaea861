import { baseRequest } from '@/utils/request'

const requestData = (url, ...arg) => baseRequest(`/api/webapp/auth/b/` + url, ...arg)
const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionknowledge/` + url, ...arg)
export default {
	getSubstation(data) {
		return requestData('changeCurrSubstation', data, 'get')
	},
	unionknowledgePage(data) {
		return request('page', data, 'get')
	},
	// 随机获取工会知识问答分页
	randPage(data) {
		return request('randPage', data, 'get')
	}
}

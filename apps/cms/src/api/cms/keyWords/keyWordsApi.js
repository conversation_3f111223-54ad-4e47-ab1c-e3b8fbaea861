import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/searchconfig/` + url, ...arg)

export default {
	page(data) {
		return request('page', data, 'get')
	},
	save(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	deleteItem(data) {
		return request('delete', data)
	},
	detail(data) {
		return request('detail', data, 'get')
	}
}

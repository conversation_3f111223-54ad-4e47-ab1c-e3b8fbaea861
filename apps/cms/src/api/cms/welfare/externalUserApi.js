import { baseRequest } from '@/utils/request'
import smCrypto from '@/utils/smCrypto'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/externaluser/` + url, ...arg)

export default {
	// 获取外部用户管理表分页
	page(data) {
		const sm4Phone = data?.phone ? smCrypto.doSm4Encrypt(data.phone) : ''
		return request('page', { ...data, phone: sm4Phone }, 'get')
	},
	detail(data) {
		return request('detail', data, 'get')
	},
	// 添加/编辑外部用户管理表
	save(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	delete(data) {
		return request('delete', data, 'post')
	},
	relUser(data) {
		return request('relUser', data, 'post')
	},
	undoRelUser(data) {
		return request('undoRelUser', data, 'post')
	},
	getUnRelUsers(data) {
		return request('getUnRelUsers', data, 'post')
	}
}

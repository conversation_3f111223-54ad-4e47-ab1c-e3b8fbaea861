import { baseRequest } from '@/utils/request'
import smCrypto from '@/utils/smCrypto'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/welfarepersonlist/` + url, ...arg)

export default {
	// 获取福利活动人员名单分页
	page(data) {
		const sm4Phone = data?.phone ? smCrypto.doSm4Encrypt(data.phone) : ''
		return request('page', { ...data, phone: sm4Phone }, 'get')
	},
	// 获取福利活动白名单列表
	whitelists(data) {
		return request('whitelists', data, 'get')
	},
	// 获取福利活动人员名单详情
	detail(data) {
		return request('detail', data, 'get')
	},
	// 编辑福利活动白名单
	editWhitelists(data) {
		return request('editWhitelists', data, 'post')
	},
	// 通过ID删除白名单信息
	deleteById(data) {
		return request(`deleteById?id=${data}`, data, 'post')
	},
	// 下载活动人员清单
	downloadActUserLists(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downloadActUserLists', data, 'get', options)
	},
	addOnShelveActPerson(data) {
		return request('addOnShelveActPerson', data, 'post')
	},
	verifyUserById(data) {
		return request('verifyUserById', data, 'get')
	},
	// 变更已上架活动的结束时间（支持批量处理）
	updateOnShelveActTime(data) {
		return request('updateOnShelveActTime', data, 'post')
	},
	// 从工会会员导入活动人员名单
	importUnions(data) {
		return request('importUnions', data, 'post')
	},
	// 下载活动人员名单导入模板
	downloadImportTemplate(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downloadImportTemplate', data, 'get', options)
	},
	// 下载活动人员名单导入错误清单
	downloadErrorUserLists(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downloadErrorUserLists', data, 'get', options)
	},
	// 从Excel导入活动人员名单
	importExcel(data) {
		return request('importExcel', data, 'post')
	},
	// 删除单个福利活动全部人员名单
	deleteAll(data) {
		return request('deleteAll', data, 'get')
	},
	// 添加福利活动人员名单
	add(data) {
		return request('add', data, 'post')
	},
	// 删除福利活动人员名单
	deleteItem(data) {
		return request('delete', data, 'post')
	},
	// 编辑福利活动人员名单
	edit(data) {
		return request('edit', data, 'post')
	},
	changeTester(checked, { activityUid, id }){
		const api = checked ? 'openUserTest' : 'cancelUserTest'
		return request(api, { welfareUid: activityUid, personlistId: id }, 'get')
	}
}

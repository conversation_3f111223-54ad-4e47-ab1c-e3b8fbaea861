import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/supplierinfo/` + url, ...arg)

/**
 * 供应商信息表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/31 15:26
 **/
export default {
	// 获取供应商信息表分页
	wcmSupplierInfoPage(data) {
		return request('page', data, 'get')
	},
	wcmSupplierInfoActPage(data) {
		return request('actPage', data, 'get')
	},
	// 提交供应商信息表表单 edit为true时为编辑，默认为新增
	wcmSupplierInfoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除供应商信息表
	wcmSupplierInfoDelete(data) {
		return request('delete', data)
	},
	// 获取供应商信息表详情
	wcmSupplierInfoDetail(data) {
		return request('detail', data, 'get')
	},
	getSpClientId(data) {
		return request('getSpClientId', data, 'get')
	},
	detailsByIds(data) {
		return request('detailsByIds', data)
	}
}

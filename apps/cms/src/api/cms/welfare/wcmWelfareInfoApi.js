import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/welfareinfo/` + url, ...arg)
const requestLog = (url, ...arg) => baseRequest(`/api/webapp/dev/log/` + url, ...arg)

/**
 * 福利信息表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/31 15:28
 **/
export default {
	// 获取福利信息表分页
	wcmWelfareInfoPage(data) {
		return request('page', data, 'get')
	},
	// 提交福利信息表表单 edit为true时为编辑，默认为新增
	wcmWelfareInfoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除福利信息表
	wcmWelfareInfoDelete(data) {
		return request('delete', data)
	},
	// 获取福利信息表详情
	wcmWelfareInfoDetail(data) {
		return request('detail', data, 'get')
	},
	getActId(data) {
		return request('getActId', data, 'get')
	},
	downloadActUserLists(data) {
		const options = {
			responseType: 'blob'
		}
		return request('downloadActUserLists', data, 'get', options)
	},
	getUserNum(data) {
		return request('getUserNum', data, 'get')
	},
	changeRecordPage(data) {
		return requestLog('changeLogPage', data, 'get')
	},
	delUnionById(data) {
		return request('delUnionById', data, 'get')
	},
	delSupplierById(data) {
		return request('delSupplierById', data, 'get')
	},
	// 通过活动UID上架活动
	onShelve(data) {
		return request('onShelve', data, 'get')
	},
	// 通过活动UID下架活动
	unShelve(data) {
		return request('unShelve', data, 'get')
	},
	zeroExec(data) {
		return request('zeroExec', data, 'get')
	},
	zeroRestore(data) {
		return request('zeroRestore', data, 'get')
	},
	goBackStaged(data) {
		return request('goBackStaged', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/welfarereview/` + url, ...arg)

/**
 * 福利发放审核表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/31 15:29
 **/
export default {
	// 获取福利发放审核表分页
	wcmWelfareReviewPage(data) {
		return request('page', data, 'get')
	},
	// 提交福利发放审核表表单 edit为true时为编辑，默认为新增
	wcmWelfareReviewSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除福利发放审核表
	wcmWelfareReviewDelete(data) {
		return request('delete', data)
	},
	// 获取福利发放审核表详情
	wcmWelfareReviewDetail(data) {
		return request('detail', data, 'get')
	},
	reviews(data) {
		return request('reviews', data)
	}
}

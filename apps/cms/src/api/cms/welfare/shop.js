import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/spapp/shop/` + url, ...arg)

export default {
	page(data) {
		return request('pointbook/page', data, 'get')
	},
	downloadUserPointList(data) {
		const options = {
			responseType: 'blob'
		}
		return request('pointbook/downloadUserPointList', data, 'get', options)
	},
	downloadPointDetail(data) {
		const options = {
			responseType: 'blob'
		}
		return request('pointdetail/downloadPointDetail', data, 'get', options)
	}
}

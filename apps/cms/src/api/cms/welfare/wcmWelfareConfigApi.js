import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/welfareconfig/` + url, ...arg)

/**
 * 福利配置表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/31 15:28
 **/
export default {
	// 获取福利配置表分页
	wcmWelfareConfigPage(data) {
		return request('page', data, 'get')
	},
	// 提交福利配置表表单 edit为true时为编辑，默认为新增
	wcmWelfareConfigSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除福利配置表
	wcmWelfareConfigDelete(data) {
		return request('delete', data)
	},
	// 获取福利配置表详情
	wcmWelfareConfigDetail(data) {
		return request('detail', data, 'get')
	},
	list(data) {
		return request('list', data, 'get')
	},
	uploadImage(data) {
		return request('uploadImage', data, 'get')
	},
	previewImage(data) {
		return request('previewImage', data, 'get')
	}
}

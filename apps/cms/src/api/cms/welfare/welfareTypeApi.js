import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/spapp/sp/welfaretype/` + url, ...arg)

export default {
	// 获取福利类别列表
	list(data) {
		return request('list', data, 'get')
	},
	// 添加福利类别表 +编辑福利类别表
	save(data, id) {
		return request(id ? 'edit' : 'add', data, 'post')
	},
	// 上传类别背景图片
	uploadImage(data) {
		return request('uploadImage', data, 'get')
	},
	// 删除福利类别表
	delete(data) {
		return request('delete', data, 'post')
	},
	// 检查类别关键字是否重复
	checkTypeKey(data) {
		return request('checkTypeKey', data, 'get')
	}
}

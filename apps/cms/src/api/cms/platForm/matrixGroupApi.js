import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/matrixgroup/` + url, ...arg)

/**
 * 矩阵分组关联Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/28 11:43
 **/
export default {
	// 获取矩阵分组关联分页
	matrixGroupPage(data) {
		return request('page', data, 'get')
	},
	// 提交矩阵分组关联表单 edit为true时为编辑，默认为新增
	matrixGroupSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除矩阵分组关联
	matrixGroupDelete(data) {
		return request('delete', data)
	},
	// 获取矩阵分组关联详情
	matrixGroupDetail(data) {
		return request('detail', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/applicationgroup/` + url, ...arg)

/**
 * 应用分组Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/21 17:26
 **/
export default {
	// 获取应用分组分页
	applicationGroupPage(data) {
		return request('page', data, 'get')
	},
	// 提交应用分组表单 edit为true时为编辑，默认为新增
	applicationGroupSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除应用分组
	applicationGroupDelete(data) {
		return request('delete', data)
	},
	// 获取应用分组详情
	applicationGroupDetail(data) {
		return request('detail', data, 'get')
	}
}

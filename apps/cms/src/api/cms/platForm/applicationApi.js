import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/application/` + url, ...arg)

/**
 * 应用信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/21 14:13
 **/
export default {
	// 获取应用信息分页
	applicationPage(data) {
		return request('page', data, 'get')
	},
	// 提交应用信息表单 edit为true时为编辑，默认为新增
	applicationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除应用信息
	applicationDelete(data) {
		return request('delete', data)
	},
	// 获取应用信息详情
	applicationDetail(data) {
		return request('detail', data, 'get')
	},
	applicationList(data) {
		return request('list', data, 'get')
	},
	doEnable(data) {
		return request('doEnable', data, 'post')
	},
	checkCodeIsUnique(data) {
		return request('checkCodeIsUnique', data, 'get')
	}
}

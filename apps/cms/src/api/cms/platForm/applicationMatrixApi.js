import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/applicationmatrix/` + url, ...arg)

/**
 * 应用矩阵Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/28 11:36
 **/
export default {
	// 获取应用矩阵分页
	applicationMatrixPage(data) {
		return request('page', data, 'get')
	},
	// 提交应用矩阵表单 edit为true时为编辑，默认为新增
	applicationMatrixSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除应用矩阵
	applicationMatrixDelete(data) {
		return request('delete', data)
	},
	// 获取应用矩阵详情
	applicationMatrixDetail(data) {
		return request('detail', data, 'get')
	},
	//
	getAddedList(data) {
		return request('getAddedList', data, 'get')
	}
}

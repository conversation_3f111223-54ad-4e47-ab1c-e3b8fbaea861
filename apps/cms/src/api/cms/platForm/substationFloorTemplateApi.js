import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/floortemplate/` + url, ...arg)

/**
 * 子站楼层模板Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/03 10:59
 **/
export default {
	// 获取子站楼层模板分页
	substationFloorTemplatePage(data) {
		return request('page', data, 'get')
	},
	// 提交子站楼层模板表单 edit为true时为编辑，默认为新增
	substationFloorTemplateSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除子站楼层模板
	substationFloorTemplateDelete(data) {
		return request('delete', data)
	},
	// 获取子站楼层模板详情
	substationFloorTemplateDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取所有模板
	allList(data) {
		return request('list', data, 'get')
	}
}

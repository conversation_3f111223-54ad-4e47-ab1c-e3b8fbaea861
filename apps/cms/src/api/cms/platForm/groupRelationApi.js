import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/grouprelation/` + url, ...arg)

/**
 * 分组应用关联Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/21 17:27
 **/
export default {
	// 获取分组应用关联分页
	groupRelationPage(data) {
		return request('page', data, 'get')
	},
	// 提交分组应用关联表单 edit为true时为编辑，默认为新增
	groupRelationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除分组应用关联
	groupRelationDelete(data) {
		return request('delete', data)
	},
	// 获取分组应用关联详情
	groupRelationDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取分组应用关联详情
	groupRelationList(data) {
		return request('list', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/substation/` + url, ...arg)

/**
 * 子站信息 Api 接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/04 10:00
 **/
export default {
	// 获取子站信息分页
	substationPage(data) {
		return request('page', data, 'get')
	},
	// 提交子站信息表单 edit 为 true 时为编辑，默认为新增
	substationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除子站信息
	substationDelete(data) {
		return request('delete', data)
	},
	// 获取子站信息详情
	substationDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取当前用户子站全部列表 废弃
	substationList(data) {
		return request('currList', data, 'get')
	},
	// 获取全部子站
	substationAllList(data) {
		return request('list', data, 'get')
	},
	substationTodayCount(data) {
		return request('todayCount', data, 'get')
	},
	enableList(data) {
		return request('stationList', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/substationconfig/` + url, ...arg)

/**
 * 子站模板配置Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/03 11:46
 **/
export default {
	// 获取子站模板配置分页
	subsationConfigPage(data) {
		return request('page', data, 'get')
	},
	// 提交子站模板配置表单 edit为true时为编辑，默认为新增
	subsationConfigSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除子站模板配置
	subsationConfigDelete(data) {
		return request('delete', data)
	},
	// 获取子站模板配置详情
	subsationConfigDetail(data) {
		return request('detail', data, 'get')
	},
	// 锁定解锁
	subsationConfigDolock(data) {
		return request('doLock', data, 'post')
	},
	// 变更记录---所有
	history(data) {
		return request('history', data, 'get')
	},
	// 变更记录---分页
	historyPage(data) {
		return request('historyPage', data, 'get')
	},
	// 数据源
	sourceTree(data) {
		return request('sourceTree', data, 'get')
	}
}

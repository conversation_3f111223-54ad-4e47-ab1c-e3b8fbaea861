import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubsidymanagement/` + url, ...arg)
const requestTre = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubsidy/` + url, ...arg)

/**
 * 工会报表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/12 19:14
 **/
export default {
	// 获取工会报表分页
	sysUnionsManagePage(data) {
		return request('page', data, 'get')
	},
	// 提交工会报表表单 edit为true时为编辑，默认为新增
	sysUnionsManageSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会报表
	sysUnionsManageDelete(data) {
		return request('delete', data)
	},
	commit(data) {
		return requestTre('commit', data)
	},
	revoke(data) {
		return requestTre('revoke', data)
	},
	draft(data) {
		return requestTre('draft', data)
	},
	pass(data) {
		return requestTre('pass', data)
	},
	reject(data) {
		return requestTre('reject', data)
	},
	getInitTreeWithSubsidyStatusByUnionId(data) {
		return requestTre('getInitTreeWithSubsidyStatusBySubsidyManageId', data,'get')
	},
	getChildUnionListWithSubsidyStatusByUnionId(data) {
		return requestTre('getChildUnionListWithSubsidyStatusByUnionId', data, 'get')
	},
	getSubsidyInfoByManageIdAndUniond(data) {
		return requestTre('getSubsidyInfoByManageIdAndUniond', data, 'get')
	},
	// 获取工会报表详情
	sysUnionsManageDetail(data) {
		return request('detail', data, 'get')
	}}

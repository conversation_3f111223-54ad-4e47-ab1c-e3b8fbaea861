import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsuser/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/30 15:35
 **/
export default {
	// 获取工会表分页
	cmsMemberPage(data) {
		return request('page', data, 'get')
	},
	//变更记录
	cmsMemberHistory(data) {
		return request('history', data, 'get')
	},
	// 提交工会表表单 edit为true时为编辑，默认为新增
	cmsMemberSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会表
	cmsMemberDelete(data) {
		return request('delete', data)
	},
	// 获取工会表详情
	cmsMemberDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取组织树
	orgTree(data) {
		return request('tree', data, 'get')
	},
	sysUnionsUserRelList(data) {
		return request('list', data, 'get')
	},
	getListByCondition(data) {
		return request('getListByCondition', data, 'get')
	},
	downloadImportTemplate() {
		return request('downloadImportTemplate', {}, 'get', {
			responseType: 'blob'
		})
	},
	importUser(data) {
		return request('importUser', data)
	},
	downloadErrorUserLists(data) {
		return request('downloadErrorUserLists', data, 'get', {
			responseType: 'blob'
		})
	}
}

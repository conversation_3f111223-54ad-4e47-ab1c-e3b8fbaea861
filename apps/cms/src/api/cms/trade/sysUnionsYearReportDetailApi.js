import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsyearreportdetail/` + url, ...arg)

/**
 * 工会报表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/12 19:14
 **/
export default {
	// 获取工会报表分页
	tree(data) {
		return request('tree', data, 'get')
	},
	merge(data) {
		return request('merge', data, 'get')
	},
	// 提交工会报表表单
	commit(data) {
		return request('commit', data)
	},
	// 暂存工会报表
	pass(data) {
		return request('pass', data)
	},
	rollBack(data) {
		return request('rollBack', data)
	},
	draft(data) {
		return request('draft', data)
	},
	// 获取工会报表详情
	detail(data) {
		return request('detail', data, 'get')
	},
	// 抽取数据
	getNum(data) {
		return request('getNum', data, 'get')
	},

	export(data) {
		return request('export', data, 'get',{
			responseType: 'blob'
		})
	}

}

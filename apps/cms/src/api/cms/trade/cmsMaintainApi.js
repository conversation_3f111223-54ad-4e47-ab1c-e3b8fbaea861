import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/maintain/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/30 15:35
 **/
export default {
	// 获取专题表分页
	cmsMaintainPage(data) {
		return request('page', data, 'get')
	},
	// 提交专题表表单 edit为true时为编辑，默认为新增
	cmsMaintainSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除专题表
	cmsMaintainDelete(data) {
		return request('delete', data)
	},
	// 获取专题表详情
	cmsMaintainDetail(data) {
		return request('detail', data, 'get')
	}
}

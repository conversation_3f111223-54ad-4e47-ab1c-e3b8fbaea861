import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubprotection/` + url, ...arg)
const feedbackRequest = (url, ...arg) => baseRequest(`/api/webapp/sys/feedbackhistory/` + url, ...arg)
const feedbackRequest2 = (url, ...arg) => baseRequest(`/api/webapp/sys/feedbackreview/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/08 16:35
 **/
export default {
	// 获取上报记录
	cmsSubordinateReportingList(data) {
		return request('reportingList', data, 'get')
	},
	// 获取下级维权列表
	cmsSubordinatePage(data) {
		return request('page', data, 'get')
	},

	// 获取上报记录
	historyList(data) {
		return feedbackRequest('reportingList', data, 'get')
	},
	// 获取下级维权列表
	feedbackReviewPage(data) {
		return feedbackRequest2('page', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsApproval/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/11/26 10:35
 **/
export default {
	// 获取工会经费审批分页
	sysUnionsApprovalPage(data) {
		return request('page', data, 'get')
	},
	// 提交工会经费报销表单 edit为true时为编辑，默认为新增
	sysUnionsApprovalSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会经费报销
	sysUnionsApprovalDelete(data) {
		return request('delete', data)
	},
	// 获取工会经费报销详情
	sysUnionsApprovalDetail(data) {
		return request('detail', data, 'get')
	}
}

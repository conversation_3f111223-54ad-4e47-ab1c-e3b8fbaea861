import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsuserrel/` + url, ...arg)

/**
 * 工会委员会成员表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/04 15:50
 **/
export default {
	// 获取工会委员会成员表分页
	sysUnionsUserRelList(data) {
		return request('list', data, 'get')
	},
	// 获取工会委员会成员表分页
	sysUnionsUserRelPage(data) {
		return request('page', data, 'get')
	},
	// 提交工会委员会成员表表单 edit为true时为编辑，默认为新增
	sysUnionsUserRelSubmitForm(data, edit = false) {
		//console.log(edit)
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会委员会成员表
	sysUnionsUserRelDelete(data) {
		return request('delete', data)
	},
	// 获取工会委员会成员表详情
	sysUnionsUserRelDetail(data) {
		return request('detail', data, 'get')
	}
}

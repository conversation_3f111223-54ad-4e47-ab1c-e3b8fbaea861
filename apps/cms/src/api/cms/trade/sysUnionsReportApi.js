import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsreport/` + url, ...arg)

/**
 * 工会报表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/12 19:14
 **/
export default {
	// 获取工会报表分页
	sysUnionsReportPage(data) {
		return request('page', data, 'get')
	},
	// 提交工会报表表单 edit为true时为编辑，默认为新增
	sysUnionsReportSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会报表
	sysUnionsReportDelete(data) {
		return request('delete', data)
	},
	// 获取工会报表详情
	sysUnionsReportDetail(data) {
		return request('detail', data, 'get')
	}}

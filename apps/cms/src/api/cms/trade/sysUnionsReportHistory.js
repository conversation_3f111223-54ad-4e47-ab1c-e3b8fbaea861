import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsreporthistory/` + url, ...arg)
const arequest = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsyearreporthistory/` + url, ...arg)

/**
 * 工会报表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/12 19:14
 **/
export default {
	// 获取工会报表分页
	page(data) {
		return request('page', data, 'get')
	},
	list(data) {
		return request('list', data, 'get')
	},
	lists(data) {
		return arequest('list', data, 'get')
	},
}

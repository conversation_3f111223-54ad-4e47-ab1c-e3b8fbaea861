import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/people/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/30 15:35
 **/
export default {
	// 获取专题表分页
	cmsPeoplePage(data) {
		return request('page', data, 'get')
	},
	// 提交专题表表单 edit为true时为编辑，默认为新增
	cmsPeopleSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除专题表
	cmsPeopleDelete(data) {
		return request('delete', data)
	},
	// 获取专题表详情
	cmsPeopleDetail(data) {
		return request('detail', data, 'get')
	}
}

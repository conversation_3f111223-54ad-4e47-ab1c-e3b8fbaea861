import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubsidyapplication/` + url, ...arg)
/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/14 14:50
 **/
export default {
	// 列表
	getList(data) {
		return request('page', data, 'get')
    },
    // 删除
	delete(data) {
		return request('delete', data, 'post')
    },
    // 详情
	detail(data) {
		return request('detail', data, 'get')
    },
    // 获取工会个人补助申请列表
	list(data) {
		return request('list', data, 'get')
    },
}
import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubsidy/` + url, ...arg)
const requestData = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubsidyuser/` + url, ...arg)
const requestCount = (url, ...arg) => baseRequest(`/api/webapp/sys/unionssubsidycount/` + url, ...arg)
/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/14 14:50
 **/
export default {
	// 补助申请校验
	cmsSubsidyApplicationCheck(data) {
		return request('check', data, 'get')
	},
	// 补助申请联系人校验
	cmsSubsidyApplicationUserCheck(data) {
		return requestData('check', data)
	},
	// 获取补助列表
	cmsSubsidyApplicationPage(data) {
		return request('page', data, 'get')
	},
	page(data) {
		return requestCount('page', data, 'get')
	},
	getCountBySubsidytype(data) {
		return requestCount('getCountBySubsidytype', data, 'get')
	},
	edit(data) {
		return requestCount('edit', data)
	},
	// 补助详情
	cmsSubsidyApplicationDetail(data) {
		return request('detail', data, 'get')
	},
	// 补助提交
	cmsSubsidyApplicationCommit(data) {
		return request('commit', data)
	},
	// 补助暂存
	cmsSubsidyApplicationDraft(data) {
		return request('draft', data)
	},
	// 补助删除
	cmsSubsidyApplicationDelete(data) {
		return request('delete', data)
	},
	// 补助人员添加 edit为true时为编辑，默认为新增
	cmsSubsidyApplicationSubmitForm(data, edit = false) {
		return requestData(edit ? 'edit' : 'add', data)
	},
	// 获取补助人员列表
	cmsSubsidyApplicationList(data) {
		return requestData('list', data, 'get')
	},
	// 补助人员删除
	cmsSubsidyApplicationUserDelete(data) {
		return requestData('delete', data)
	},
	// 补助人员合并
	cmsSubsidyApplicationUserMerge(data) {
		return requestData('merge', data, 'get')
	},
	// 补助审批
	cmsSubsidyApplicationChecks(data) {
		return request('check', data)
	},
	//导入模版
	cmsSubsidyApplicationDownloadImportTemplate() {
		return requestData('downloadImportTemplate', {}, 'get', {
			responseType: 'blob'
		})
	},
	//批量导入
	cmsSubsidyApplicationImportUser(data) {
		return requestData('importUser', data, 'post', {
			responseType: 'blob'
		})
	},
	//导入报错
	cmsSubsidyApplicationDownloadErrorUserLists(data) {
		return requestData('downloadErrorUserLists', data, 'get', {
			responseType: 'blob'
		})
	}
	// 获取补助审批
	// cmsSubsidyApplicationSubPage(data) {
	// 	return requestSub('page', data, 'get')
	// }
}

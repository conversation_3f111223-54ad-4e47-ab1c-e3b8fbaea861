import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsbudget/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/11/22 10:35
 **/
export default {
	// 获取工会经费报销分页
	sysUnionsBudgetPage(data) {
		return request('page', data, 'get')
	},
	// 提交工会经费报销表单 edit为true时为编辑，默认为新增
	sysUnionsBudgetSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会经费报销
	sysUnionsBudgetDelete(data) {
		return request('delete', data)
	},
	// 获取工会经费报销详情
	sysUnionsBudgetDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取工会经费报销详情
	sysUnionsBudgetExport(data) {
		return request('export', data, 'get',{
			responseType: 'blob'
		})
	}
}

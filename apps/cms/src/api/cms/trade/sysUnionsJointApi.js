import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsjoint/` + url, ...arg)

/**
 * 工会报表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/12 19:14
 **/
export default {
	// 获取工会报表分页
	page(data) {
		return request('page', data, 'get')
	},
	// 提交工会报表表单 edit为true时为编辑，默认为新增
	SubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会报表
	delete(data) {
		return request('delete', data)
	},
	// 获取工会报表详情
	detail(data) {
		return request('detail', data, 'get')
	}}

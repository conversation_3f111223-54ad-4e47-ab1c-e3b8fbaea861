import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/useradvanced/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/05 15:35
 **/
export default {
	// 获取个人荣誉表分页
	cmsPersonalPage(data) {
		return request('page', data, 'get')
	},
	// 获取劳模个人荣誉表分页
	cmsPersonalPageLm(data) {
		return request('pageLm', data, 'get')
	},
	// 提交荣誉表表单 edit为true时为编辑，默认为新增
	cmsPersonalSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除个人荣誉
	cmsPersonalDelete(data) {
		return request('delete', data)
	},
	// 导出荣誉
	cmsPersonalExport(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'post', options)
	}
}

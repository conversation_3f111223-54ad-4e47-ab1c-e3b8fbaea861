import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsrequest/` + url, ...arg)

export default {
	page(data) {
		return request('page', data, 'post')
	},
    detail(data) {
		return request('detail', data, 'get')
    },
	edit(data) {
		return request('edit', data, 'post')
	},
	editBatch(data) {
		return request('editBatch', data, 'post')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/cmsapp/cms/collectiveness/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/30 15:35
 **/
export default {
	// 获取专题表分页
	cmsCollectivenessPage(data) {
		return request('page', data, 'get')
	},
	// 提交专题表表单 edit为true时为编辑，默认为新增
	cmsCollectivenessSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除专题表
	cmsCollectivenessDelete(data) {
		return request('delete', data)
	},
	// 获取专题表详情
	cmsCollectivenessDetail(data) {
		return request('detail', data, 'get')
	}
}

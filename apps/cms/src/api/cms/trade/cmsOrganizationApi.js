import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/tradeunions/` + url, ...arg)
const requestNew = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsprotection/` + url, ...arg)
const feedbackRequest = (url, ...arg) => baseRequest(`/api/webapp/sys/feedback/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/30 15:35
 **/
export default {
	sysTradeUnionsPage(data) {
		return request('page', data, 'get')
	},
	//获取变更记录
	sysTradeUnionsHistory(data) {
		return request('history', data, 'get')
	},
	// 提交工会信息表表单 edit为true时为编辑，默认为新增
	sysTradeUnionsSubmitForm(data, edit = false) {
		//console.log(edit)
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除工会信息表
	sysTradeUnionsDelete(data) {
		return request('delete', data)
	},
	// 获取工会信息表详情
	sysTradeUnionsDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取上级工会名称
	sysTradeUnionsGetParentUnionName(data) {
		return request('getParentUnionName', data, 'get')
	},
	// 获取组织树
	orgTree(data) {
		return request('tree', data, 'get')
	},
	// 获取工会的根Id
	getUnionsRootId(data) {
		return request('getRootId', data, 'get')
	},
	//获取本级及下级工会列表
	getChildUnionTree(data) {
		return request('getChildUnionTree', data, 'get')
	},
	getUnionPage(data) {
		return request('getUnionPage', data, 'get')
	},
	buildUnionTreeFromNodes(data) {
		return request('buildUnionTreeFromNodes', data, 'get')
	},
	getUnionInitializeTreeBySubstationUnionId(data) {
		return request('getUnionInitializeTreeBySubstationUnionId', data, 'get')
	},
	getChildUnionListByUnionId(data) {
		return request('getChildUnionListByUnionId', data, 'get')
	},
	//获取本级及下级工会列表新版
	getChildUnionTreeNew(data) {
		return requestNew('subUnions', data, 'get')
	},
	//建言献策获取本级及下级工会列表新版
	getFeedbackChildUnionTreeNew(data) {
		return feedbackRequest('subUnions', data, 'get')
	},
	getSysTradeUnionsByOrgId(data) {
		return request('getSysTradeUnionsByOrgId', data, 'get')
	},
	// 获取工会组织概况
	getUnionOrgOverview(data) {
		return request('getUnionOrgOverview', data, 'get')
	},
	// 获取工会结构信息
	getUnionLevelInfo(data) {
		return request('getUnionLevelInfo', data, 'get')
	},
	// 获取工会地图信息
	getUnionMapInfo(data) {
		return request('getUnionMapInfo', data, 'get')
	},
	// 获取工会组织列表
	getUnionOrgStructure(data) {
	    return request('getUnionOrgStructure', data, 'get')
	}
}

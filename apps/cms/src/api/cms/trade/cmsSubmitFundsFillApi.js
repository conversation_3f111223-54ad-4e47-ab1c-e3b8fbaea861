import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsturned/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/12/03 15:35
 **/
export default {
	// 获取上缴填报
	cmsSubmitFundsFillPage(data) {
		return request('page', data, 'get')
	},
	// 获取上缴填报添加 edit为true时为编辑，默认为新增
	cmsSubmitFundsFillSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},

	cmsSubmitFundsFillDelete(data) {
		return request('delete', data)
	},
	cmsSubmitFundsFillDetail(data) {
		return request('detail', data, 'get')
	},
	//导入模版
	cmsSubmitFundsFillTemplateDownload() {
		return request('templateDownload', {}, 'get', {
			responseType: 'blob'
		})
	},
	//批量导入
	cmsSubmitFundsFillImportTurned(data) {
		return request('importTurned', data, 'post', {
			responseType: 'blob'
		})
	},
	//导入报错
	cmsSubmitFundsFillFailDownload(data) {
		return request('failDownload', data, 'get', {
			responseType: 'blob'
		})
	},
	//下级报送经费
	cmsSubmitFundsChildPage(data) {
		return request('child/page', data, 'get')
	},
	// 驳回
	cmsSubmitFundsReject(data) {
		return request('refuse', data)
	},
	// 同意
	cmsSubmitFundsPass(data) {
		return request('pass', data)
	}
}

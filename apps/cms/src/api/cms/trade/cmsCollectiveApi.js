import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/unionsadvanced/` + url, ...arg)

/**
 * 专题表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/04 15:35
 **/
export default {
	// 获取荣誉表分页
	cmsCollectivePage(data) {
		return request('page', data, 'get')
	},
	// 提交荣誉表表单 edit为true时为编辑，默认为新增
	cmsCollectiveSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除荣誉
	cmsCollectiveDelete(data) {
		return request('delete', data)
	},
	// 导出荣誉
	cmsCollectiveExport(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'post', options)
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/unifieddo/` + url, ...arg)

/**
 * 统一待办表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/06/21 09:53
 **/
export default {
	// 获取统一待办表分页
	bizUnifiedDoPage(data) {
		return request('page', data, 'get')
	},
	bizUnifiedDopageTag(data) {
		return request('pageTag', data, 'get')
	},
	// 提交统一待办表表单 edit为true时为编辑，默认为新增
	bizUnifiedDoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除统一待办表
	bizUnifiedDoDelete(data) {
		return request('delete', data)
	},
	// 获取统一待办表详情
	bizUnifiedDoDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取统一待办状态
	bizUnifiedDoUptByActId(data) {
		return request('uptByActId', data)
	},
	bizUnifiedDoAllRead(data) {
		return request('toRead/allRead', data, 'post')
	},
	bizUnifiedDoSources(data) {
		return request('sources', data, 'get')
	},
	tagCountAll(data) {
		return request('tagCountAll', data, 'get')
	}
}

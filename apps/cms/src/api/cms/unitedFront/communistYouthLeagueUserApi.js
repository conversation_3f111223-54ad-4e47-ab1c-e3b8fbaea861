import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/youthleagueuser/` + url, ...arg)

/**
 * 共青团员表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/15 14:18
 **/
export default {
	automaticExitLeagueManage(data) {
		return request('automaticExitLeagueManage', data, 'get')
	},
	confirmExitLeague(data) {
		return request('confirmExitLeague', data)
	},
	// 获取共青团员表分页
	communistYouthLeagueUserPage(data) {
		return request('page', data, 'get')
	},
	// 提交共青团员表表单 edit为true时为编辑，默认为新增
	communistYouthLeagueUserSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除共青团员表
	communistYouthLeagueUserDelete(data) {
		return request('delete', data)
	},
	// 获取共青团员表详情
	communistYouthLeagueUserDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取团员变更记录
	history(data) {
		return request('history', data, 'get')
	},
	// 获取团员变更记录
	downloadImportTemplate(data) {
		return request('downloadImportTemplate', data, 'get',{
			responseType: 'blob'
		})
	},
	// 获取团员变更记录
	importLeagueUser(data,leagueId) {
		return request(`importLeagueUser?leagueId=${leagueId}`, data)
	},
	// 共青团员信息导出
	export(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'get', options)
	},
	downloadErrorUserLists(data) {
		return request('downloadErrorUserLists', data, 'get', {
			responseType: 'blob'
		})
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/youthleague/` + url, ...arg)
const requestData = (url, ...arg) => baseRequest(`/api/webapp/sys/youthleaguerel/` + url, ...arg)
/**
 * 共青团表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/15 14:12
 **/
export default {
	// 获取共青团表分页
	communistYouthLeaguePage(data) {
		return request('page', data, 'get')
	},
	// 提交共青团表表单 edit为true时为编辑，默认为新增
	communistYouthLeagueSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除共青团表
	communistYouthLeagueDelete(data) {
		return request('delete', data)
	},
	// 获取共青团表详情
	communistYouthLeagueDetail(data) {
		return request('detail', data, 'get')
	},
	//获取共青团历史记录
	communistYouthLeagueHistory(data) {
		return request('history', data, 'get')
	},
	// 获取共青团组织树
	tree(data) {
		return request('tree', data, 'get')
	},
	//团组织信息
	communistYouthLeagueInfo(data) {
		return requestData('info', data, 'get')
	},
	//删除团组织信息
	communistYouthLeagueRelDelete(data) {
		return requestData('delete', data)
	},
	//添加、编辑团组织信息
	communistYouthLeagueRelSubmitForm(data, edit = false) {
		return requestData(edit ? 'edit' : 'add', data)
	},
	// 团组织信息导出
	export(data) {
		const options = {
			responseType: 'blob'
		}
		return request('export', data, 'get', options)
	},
	getCommunistYouthLeaguePage(data) {
		return request('getCommunistYouthLeaguePage', data, 'get')
	},
	buildLeagueTreeFromNodes(data) {
		return request('buildLeagueTreeFromNodes', data, 'get')
	},
	getLeagueInitializeTreeBySubstationLeagueId(data) {
		return request('getLeagueInitializeTreeBySubstationLeagueId', data, 'get')
	},
	getChildLeagueListByLeagueId(data) {
		return request('getChildLeagueListByLeagueId', data, 'get')
	}
}

import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/theorystudygroup/` + url, ...arg)

export default {
	getGroupInfo(data) {
		return request('getGroupInfo', data, 'get')
	},
	page(data) {
		return request('page', data, 'get')
	},
	deleteGroup(data) {
		return request('delete', data, 'post')
	},
	save(data, flag) {
		const api = flag ? 'edit' : 'add'
		return request(api, data, 'post')
	},
	detail(data) {
		return request('detail', data, 'get')
	}
}

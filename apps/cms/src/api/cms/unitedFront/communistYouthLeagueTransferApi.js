import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/webapp/sys/transfermembers/` + url, ...arg)
const requestHistory = (url, ...arg) => baseRequest(`/api/webapp/sys/transfermembershistory/` + url, ...arg)

/**
 * 共青团员表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/07/15 14:18
 **/
export default {
	page(data) {
		return request('page', data, 'get')
	},
	detail(data) {
		return request('detail', data, 'get')
	},
	getLeagueUserDetail(data) {
		return request('getLeagueUserDetail', data, 'get')
	},
	delete(data) {
		return request('delete', data)
	},
	// 获取共青团员表分页
	SubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	pageHistory(data) {
		return requestHistory('page', data, 'get')
	}
}

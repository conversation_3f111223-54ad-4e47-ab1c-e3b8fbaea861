import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/association/` + url, ...arg)
/**
 * 社区表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/11/11 14:03
 **/
export default {
	getAssociationPage(data) {
		return request('page', data, 'get')
	},
	getAssociationDelete(data) {
		return request('delete', data)
	},
	getAssociationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	getAssociationDetail(data) {
		return request('detail', data, 'get')
	}
}

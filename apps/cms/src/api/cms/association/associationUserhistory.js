import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/associationUserhistory/` + url, ...arg)

export default {
	getAssociationPage(data) {
		return request('page', data, 'get')
	},
	getAssociationDelete(data) {
		return request('delete', data)
	},
	getAssociationSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	getAssociationDetail(data) {
		return request('detail', data, 'get')
	},
	getAssociationReview(data) {
		return request('review', data)
	}
}

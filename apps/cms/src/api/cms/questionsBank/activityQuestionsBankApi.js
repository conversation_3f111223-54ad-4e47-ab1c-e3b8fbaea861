import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/question/questionsbank/` + url, ...arg)

/**
 * 题库表Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/05/23 18:22
 **/
export default {
	// 获取题库表分页
	activityQuestionsBankPage(data) {
		return request('page', data, 'get')
	},
	// 提交题库表表单 edit为true时为编辑，默认为新增
	activityQuestionsBankSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除题库表
	activityQuestionsBankDelete(data) {
		return request('delete', data)
	},
	// 获取题库表详情
	activityQuestionsBankDetail(data) {
		return request('detail', data, 'get')
	},
	activityQuestionsChangeStatus(data, edit = false) {
		return request('changeStatus', data)
	}
}

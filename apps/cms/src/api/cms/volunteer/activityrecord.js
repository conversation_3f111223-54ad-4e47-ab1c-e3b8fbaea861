import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/activityrecord/` + url, ...arg)

export default {
	// 删除志愿活动记录表
	deleteActiverecord(data) {
		return request('delete', data, 'post')
	},
	// 获取志愿活动记录表分页
	page(data) {
		return request('page', data, 'get')
	},
	// 获取志愿活动记录表详情
	detail(data) {
		return request('detail', data, 'get')
	},
	// 添加、编辑志愿活动记录
	save(data, flag) {
		const api = flag ? 'edit' : 'add'
		return request(api, data, 'post')
	},
}

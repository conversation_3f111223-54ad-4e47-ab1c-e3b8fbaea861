import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/api/bizapp/biz/volunteerprogram/` + url, ...arg)

export default {
	// 获取青年志愿者信息统计
	getVolunteerInfo(data) {
		return request('getVolunteerInfo', data, 'get')
	},
	// 删除青年志愿服务
	deleterecord(data) {
		return request('delete', data, 'post')
	},
	// 获取详情
	detail(data) {
		return request('detail', data, 'get')
	},
	// 获取分页
	page(data) {
		return request('page', data, 'get')
	},
	// 添加、编辑志愿活动记录
	save(data, flag) {
		const api = flag ? 'edit' : 'add'
		return request(api, data, 'post')
	},
}

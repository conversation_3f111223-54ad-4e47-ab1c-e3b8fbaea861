<template>
	<a-card :title="title" :bordered="false">
		<template #extra v-if="!readonly">
			<div class="min-w-28">
				<a-button block shape="default" type="primary" @click="openModal">
					<template #icon><plus-outlined /></template>
					{{ buttonName ? buttonName : title }}
				</a-button>
			</div>
		</template>
		<div class="user-table">
			<a-table
				ref="tableRef"
				size="small"
				bordered
				:pagination="false"
				:scroll="{
					y: 245,
					scrollToFirstRowOnChange: false,
					...props.scroll
				}"
				:columns="columns"
				:data-source="userObj"
				:loading="pageLoading"
			>
				<template #bodyCell="{ column, index, record, text }">
					<slot
						name="bodyRender"
						:scope="{ column, index, record, text }"
						v-bind="{ column, index, record, text }"
					></slot>
				</template>
			</a-table>
		</div>
	</a-card>

	<!-- 以下是弹窗内容 -->
	<a-modal
		v-model:open="visible"
		:title="`${title}选择`"
		:width="width"
		:mask-closable="false"
		:destroy-on-close="true"
		@ok="handleOk"
		@cancel="handleClose"
	>
		<a-row :gutter="10">
			<a-col :span="7">
				<a-card size="small" :loading="cardLoading" class="selectorTreeDiv">
					<a-tree
						v-if="treeData"
						v-model:expandedKeys="defaultExpandedKeys"
						:tree-data="treeData"
						:field-names="treeFieldNames"
						:selectedKeys="selectedKeys"
						@select="treeSelect"
						:loadData="orgTreeOpen ? onLoadData : undefined"
					>
						<template #title="title">
							<div class="truncate">
								<a-tooltip placement="topLeft" :title="title[treeFieldNames.title]">
									{{ title[treeFieldNames.title] }}
								</a-tooltip>
							</div>
						</template>
					</a-tree>
				</a-card>
			</a-col>
			<a-col :span="11">
				<div class="table-operator xn-mb10">
					<a-form ref="searchFormRef" name="advanced_search" class="ant-advanced-search-form" :model="searchFormState">
						<div class="flex justify-center items-center gap-2.5">
							<a-form-item :name="searchKey" class="flex-1">
								<a-input v-model:value="searchFormState[searchKey]" placeholder="请输入" />
							</a-form-item>
							<a-button type="primary" @click="loadData()"> 查询 </a-button>
							<a-button @click="reset()"> 重置 </a-button>
						</div>
					</a-form>
				</div>
				<div class="user-table">
					<a-table
						ref="tableRef"
						size="small"
						:columns="commons"
						:data-source="tableData"
						:expand-row-by-click="true"
						:loading="pageLoading"
						bordered
						:pagination="false"
						:scroll="{ y: 350 }"
					>
						<template #title>
							<span>待选择列表 {{ tableRecordNum }} 条</span>
							<div v-if="!radioModel" class="xn-fdr">
								<a-button type="dashed" size="small" @click="addAllPageRecord">添加当前数据</a-button>
							</div>
						</template>
						<template #bodyCell="{ column, record }">
							<template v-if="column.dataIndex === 'avatar'">
								<a-avatar :src="record.avatar" style="margin-bottom: -5px; margin-top: -5px" />
							</template>
							<template v-if="column.dataIndex === 'phone'">
								<template v-if="record.phone">
									<mask-phone :phone="record.phone" :userId="record.id" />
								</template>
							</template>
							<template v-if="column.dataIndex === 'action'">
								<a-button style="margin-left: -2px" type="dashed" size="small" @click="addRecord(record)"
									><PlusOutlined
								/></a-button>
							</template>
							<template v-if="column.dataIndex === 'category'">
								{{ $TOOL.dictTypeData('ROLE_CATEGORY', record.category) }}
							</template>
						</template>
					</a-table>
					<div class="mt-2">
						<a-pagination
							v-if="!isEmpty(tableData)"
							v-model:current="current"
							v-model:page-size="pageSize"
							:total="total"
							size="small"
							showSizeChanger
							@change="paginationChange"
						/>
					</div>
				</div>
			</a-col>
			<a-col :span="6" style="padding-top: 41.5px">
				<div class="user-table">
					<a-table
						ref="selectedTable"
						size="small"
						:columns="selectedCommons"
						:data-source="selectedData"
						:expand-row-by-click="true"
						:loading="selectedTableListLoading"
						bordered
						:pagination="false"
						:scroll="{ y: 350 }"
					>
						<template #title>
							<span>已选择: {{ selectedData.length }}</span>
							<div v-if="!radioModel" class="xn-fdr">
								<a-button type="dashed" danger size="small" @click="delAllRecord">全部移除</a-button>
							</div>
						</template>
						<template #bodyCell="{ column, record }">
							<template v-if="column.dataIndex === 'action'">
								<a-button style="margin-left: -2px" type="dashed" danger size="small" @click="delRecord(record)"
									><MinusOutlined
								/></a-button>
							</template>
						</template>
					</a-table>
				</div>
			</a-col>
		</a-row>
	</a-modal>
</template>

<script setup name="userSelector">
	import { message } from 'ant-design-vue'
	import { remove, isEmpty, cloneDeep } from 'lodash-es'
	import { computed } from 'vue'
	import orgApi from '@/api/sys/orgApi'
	// 弹窗是否打开
	const visible = ref(false)

	const props = defineProps({
		isDispatchData: {
			type: Boolean,
			default: false
		},
		transform: {
			type: Object,
			default: () => ({
				inputParamFn: (params) => params,
				inputFn: (params) => params,
				outputFn: (params) => params
			})
		},
		scroll: {
			type: Object,
			default: () => ({
				y: null,
				x: null
			})
		},
		readonly: {
			type: Boolean,
			default: false
		},
		title: {
			type: String,
			default: ''
		},
		buttonName: {
			type: String,
			default: () => ''
		},
		columns: {
			type: Array,
			default: () => []
		},
		defaultSelectedKeys: {
			type: Array,
			default: () => []
		},
		treeValue: {
			type: String,
			default: () => 'orgId'
		},
		radioModel: {
			type: Boolean,
			default: () => false
		},
		dataIsConverterFlw: {
			type: Boolean,
			default: () => false
		},
		treeApi: {
			type: Function,
			default: () => undefined
		},
		onTreeLoadDataApi: {
			type: Function,
			default: () => undefined
		},
		pageApi: {
			type: Function,
			default: () => undefined
		},
		listByIdListApi: {
			type: Function,
			default: () => undefined
		},
		value: {
			type: [String, Array],
			default: () => '' || []
		},
		dataType: {
			type: String,
			default: () => 'string'
		},
		userShow: {
			type: Boolean,
			default: () => true
		},
		addShow: {
			type: Boolean,
			default: () => true
		},
		width: {
			type: Number,
			default: 1000
		},
		orgTreeOpen: {
			type: Boolean,
			default: () => false
		},
		searchKey: {
			type: String,
			default: () => 'searchKey'
		}
	})
	const onLoadData = (treeNode) => {
		return new Promise((resolve) => {
			if (treeNode.dataRef.children) {
				resolve()
				return
			}
			typeof props.onTreeLoadDataApi === 'function' &&
				props
					.onTreeLoadDataApi({ parentId: treeNode.dataRef.id })
					.then((res) => {
						treeNode.dataRef.children = res
						treeData.value = [...treeData.value]
						resolve()
					})
					.finally(() => {
						resolve()
					})
		})
	}
	const transformFn = {
		inputParamFn: (params) => params,
		inputFn: (params) => params,
		outputFn: (params) => params,
		...props.transform
	}

	const viewportHeight = window.innerHeight
	const tabScrollY = computed(() => viewportHeight / 2 - 42 - 78 - 34.5)
	const selecteDTabScrollY = computed(() => viewportHeight / 2 - 42 - 78 - 34.5)
	// 主表格的ref 名称
	const tableRef = ref()
	// 主表格common
	const commons = computed(() => [
		{
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: 80
		},
		...props.columns.filter((item) => item.dataIndex !== 'action' && !item.columnHidden)
	])

	// 选中表格的表格common
	const selectedCommons = [
		{
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: 80
		},
		...props.columns.filter((item) => item.dataIndex !== 'action' && !!item.selected)
	]

	// 选中表格的ref 名称
	const selectedTable = ref()
	const tableRecordNum = ref()
	const searchFormState = ref({})
	const searchFormRef = ref()
	const cardLoading = ref(true)
	const pageLoading = ref(false)
	const selectedTableListLoading = ref(false)
	// 替换treeNode 中 title,key,children
	const treeFieldNames = { children: 'children', title: 'name', key: 'id' }
	// 获取机构树数据
	const treeData = ref()
	const selectedKeys = ref([])
	//  默认展开二级树的节点id
	const defaultExpandedKeys = ref([])
	const emit = defineEmits(['update:value', 'onBack', 'setTreeSelect'])
	const tableData = ref([])
	const selectedData = ref([])
	const recordIds = ref([])
	// 分页相关
	const current = ref(1) // 当前页数
	const pageSize = ref(10) // 每页条数
	const total = ref(0) // 数据总数
	// 打开弹框
	const showUserPlusModal = (ids = []) => {
		const data = goDataConverter(ids)
		recordIds.value = data
		getUserAvatarById(data)
		openModal()
	}

	const openModal = () => {
		if (typeof props.treeApi !== 'function') {
			message.warning('未配置选择器需要的treeApi接口')
			return
		}
		if (typeof props.pageApi !== 'function') {
			message.warning('未配置选择器需要的pageApi接口')
			return
		}
		if (!props.isDispatchData && typeof props.listByIdListApi !== 'function') {
			message.warning('未配置选择器需要的listByIdListApi接口')
			return
		}
		selectedKeys.value = props.defaultSelectedKeys
		visible.value = true
		// 获取机构树
		props
			.treeApi()
			.then((data) => {
				if (data !== null) {
					treeData.value = data
					// 默认展开2级
					treeData.value.forEach((item) => {
						// 因为0的顶级
						if (item.parentId === '0') {
							defaultExpandedKeys.value.push(item.id)
							// 取到下级ID
							// if (item.children) {
							// 	item.children.forEach((items) => {
							// 		defaultExpandedKeys.value.push(items.id)
							// 	})
							// }
						}
					})
				}
			})
			.finally(() => {
				cardLoading.value = false
			})
		searchFormState.value.current = current.value
		searchFormState.value.size = pageSize.value
		searchFormState.value[props.treeValue] = props.defaultSelectedKeys.toString()
		loadData()
		if (props.checkedListApi) {
			if (isEmpty(recordIds.value)) {
				return
			}
			const param = {
				idList: recordIds.value
			}
			selectedTableListLoading.value = true
			props
				.checkedListApi(param)
				.then((data) => {
					selectedData.value = data
				})
				.finally(() => {
					selectedTableListLoading.value = false
				})
		}
	}

	// 查询主表格数据
	const loadData = () => {
		pageLoading.value = true
		props
			.pageApi(searchFormState.value)
			.then((data) => {
				current.value = data.current
				// pageSize.value = data.size
				total.value = data.total
				// 重置、赋值
				tableData.value = []
				tableRecordNum.value = 0
				tableData.value = data.records
				if (data.records) {
					tableRecordNum.value = data.records.length
				} else {
					tableRecordNum.value = 0
				}
			})
			.finally(() => {
				pageLoading.value = false
			})
	}
	// pageSize改变回调分页事件
	const paginationChange = (page, pageSize) => {
		searchFormState.value.current = page
		searchFormState.value.size = pageSize
		loadData()
	}
	const judge = () => {
		return !(props.radioModel && selectedData.value.length > 0)
	}
	// 添加记录
	const addRecord = (record) => {
		if (!judge()) {
			message.warning('只可选择一条')
			return
		}
		const selectedRecord = selectedData.value.filter((item) => item.id === record.id)
		if (selectedRecord.length === 0) {
			selectedData.value.push(record)
		} else {
			message.warning('该记录已存在')
		}
	}
	// 添加全部
	const addAllPageRecord = () => {
		let newArray = selectedData.value.concat(tableData.value)
		let list = []
		for (let item1 of newArray) {
			let flag = true
			for (let item2 of list) {
				if (item1.id === item2.id) {
					flag = false
				}
			}
			if (flag) {
				list.push(item1)
			}
		}
		selectedData.value = list
	}
	// 删减记录
	const delRecord = (record) => {
		remove(selectedData.value, (item) => item.id === record.id)
	}
	// 删减记录
	const delAllRecord = () => {
		selectedData.value = []
	}
	// 点击树查询
	const treeSelect = (keys) => {
		searchFormState.value.current = 1
		if (keys.length > 0) {
			searchFormState.value[props.treeValue] = keys.toString()
		} else {
			delete searchFormState.value[props.treeValue]
		}
		selectedKeys.value = keys
		emit('setTreeSelect', keys)
		loadData()
	}
	const userObj = ref([])
	// 确定
	const handleOk = () => {
		userObj.value = []
		const value = []
		const showUser = []
		selectedData.value.forEach((item) => {
			const obj = {
				id: item.id,
				name: item.name
			}
			value.push(item.id)
			// 拷贝一份obj数据
			const objClone = cloneDeep(obj)
			objClone.avatar = item.avatar
			showUser.push(objClone)
		})

		if (props.isDispatchData) {
			const resultData = transformFn.outputFn(selectedData.value)
			userObj.value = resultData
			emit('update:value', resultData)
			emit('onBack', resultData)
			handleClose()
			return false
		}

		// 判断是否做数据的转换为工作流需要的
		const resultData = outDataConverter(value)
		emit('update:value', resultData)
		emit('onBack', resultData)
		handleClose()
	}
	// 重置
	const reset = () => {
		delete searchFormState.value[props.searchKey]
		loadData()
	}
	const handleClose = () => {
		searchFormState.value = {}
		tableRecordNum.value = 0
		tableData.value = []
		current.value = 1
		pageSize.value = 10
		total.value = 0
		if (props.isDispatchData) {
			selectedData.value = []
		}
		// userObj.value = []
		visible.value = false
	}
	// 数据进入后转换
	const goDataConverter = (data) => {
		if (props.dataIsConverterFlw) {
			const resultData = []
			// 处理对象
			if (!isEmpty(data.value)) {
				const values = data.value.split(',')
				if (values.length > 0) {
					values.forEach((id) => {
						resultData.push(id)
					})
				} else {
					resultData.push(data.value)
				}
			} else {
				// 处理数组
				if (!isEmpty(data) && !isEmpty(data[0]) && !isEmpty(data[0].value)) {
					const values = data[0].value.split(',')
					for (let i = 0; i < values.length; i++) {
						resultData.push(values[i])
					}
				}
			}
			return resultData
		} else {
			if (getValueType() !== 'string') {
				return data
			}
			if (data.length > 1) {
				const resultData = []
				data.split(',').forEach((id) => {
					resultData.push(id)
				})
				return resultData
			} else {
				return data
			}
		}
	}

	// 获取数据类型
	const getValueType = () => {
		if (props.dataType) {
			return props.dataType
		} else {
			if (props.radioModel) {
				return 'string'
			}
			return typeof typeof props.value
		}
	}
	const getUserAvatarById = (ids) => {
		if (!isEmpty(ids)) {
			const param = {
				idList: recordIds.value
			}
			// 这里必须转为数组类型的
			props.listByIdListApi(param).then((data) => {
				userObj.value = data
			})
		}
	}
	// 数据出口转换器
	const outDataConverter = (data) => {
		if (props.dataIsConverterFlw) {
			data = userObj.value
			const obj = {}
			let label = ''
			let value = ''
			for (let i = 0; i < data.length; i++) {
				if (data.length === i + 1) {
					label = label + data[i].name
					value = value + data[i].id
				} else {
					label = label + data[i].name + ','
					value = value + data[i].id + ','
				}
			}
			obj.key = 'USER'
			obj.label = label
			obj.value = value
			obj.extJson = ''
			return obj
		} else {
			if (getValueType() !== 'string') {
				return data
			}
			let resultData = ''
			data.forEach((id) => {
				resultData = resultData + ',' + id
			})
			resultData = resultData.substring(1, resultData.length)
			return resultData
		}
	}

	watch(
		() => props.value,
		async (newValue) => {
			if (!isEmpty(props.value)) {
				if (props.isDispatchData) {
					const ids = await transformFn.inputParamFn(newValue)
					recordIds.value = ids
					const result = await transformFn.inputFn(ids)
					userObj.value = result
					// selectedData.value = result
					return false
				}

				const ids = goDataConverter(newValue)
				recordIds.value = ids
				getUserAvatarById(ids)
			} else {
				recordIds.value = []
				userObj.value = []
			}
		},
		{
			immediate: true // 立即执行
		}
	)
	defineExpose({
		showUserPlusModal,
		pageLoading
	})
</script>
<style lang="less" scoped>
	.xn-mr-5 {
		margin-right: 5px;
	}
	.xn-mr-10 {
		margin-right: 10px;
	}
	.selectorTreeDiv {
		height: 478px;
		overflow-y: auto;
		overflow-x: hidden;
	}
	:deep(.ant-table-body) {
		height: 350px !important;
	}
	:deep(.ant-empty) {
		margin: 131px 0;
	}
	:deep(.ant-table-wrapper) {
		border-bottom: 1px solid #eee;
		border-radius: 4px;
	}
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	// .user-table {
	// 	overflow: auto;
	// 	max-height: 450px;
	// }

	.user-container {
		display: flex;
		align-items: center; /* 垂直居中 */
		flex-direction: column;
		margin-right: 10px;
		text-align: center;
	}
	.user-avatar {
		width: 30px;
		border-radius: 50%; /* 设置为50%以创建圆形头像 */
	}
	.user-name {
		font-size: 12px;
		max-width: 50px;
		white-space: nowrap;
		overflow: hidden;
	}
	.user-delete {
		z-index: 99;
		color: rgba(0, 0, 0, 0.25);
		position: relative;
		display: flex;
		flex-direction: column;
	}
	.delete-icon {
		position: absolute;
		right: -2px;
		z-index: 5;
		top: -3px;
		cursor: pointer;
		visibility: hidden;
	}
	.show-delete-icon {
		visibility: visible;
	}
</style>

<style lang="less" scoped>
	@import '@/components/Tree/tree.less';
	.ant-card {
		:deep(.ant-card-body) {
			height: 100%;
			flex: 1;
			display: flex;
			flex-flow: column nowrap;
			.ant-input-affix-wrapper {
				height: auto;
			}
			.ant-tree {
				flex: 1;
				overflow: hidden;
				overflow-y: auto;

				.ant-tree-node-content-wrapper {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}
</style>

<template>
	<xn-form-container title="变更记录" :width="800" :visible="visible" :destroy-on-close="true" @close="onClose">
		<a-table
			:data-source="TableData"
			ref="tableRef"
			:columns="props.columns"
			:row-key="(record) => record.id"
			:loading="pageLoading"
			class="custom-table"
			:pagination="false"
			:scroll="{ y: 245 }"
			align="center"
			:rowClassName="addRowColor"
		>
		</a-table>
		<a-pagination class="mt-3 text-center" v-if="showPagination" v-bind="pagination" @change="handleChange">
			<template #itemRender="{ type, originalElement }">
				<a v-if="type === 'prev'" class="mx-3">上一页</a>
				<a v-else-if="type === 'next'" class="mx-3">下一页</a>
				<component :is="originalElement" v-else></component>
			</template>
		</a-pagination>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="ChangeRecord">
	import { cloneDeep } from 'lodash'

	const props = defineProps({
		columns: {
			type: Array,
			default: () => []
		},
		api: {
			type: Function,
			default: () => Promise.resolve()
		},
		//分页
		showPagination: {
			type: Boolean,
			default: false
		}
	})
	// 定义emit事件
	const emit = defineEmits({ successful: null })
	const pageLoading = ref(false)
	// 默认是关闭状态
	const visible = ref(false)
	const pagination = ref({
		size: 'small',
		showSizeChanger: true,
		showQuickJumper: true,
		showTotal: (total) => `共 ${total} 条 `,
		current: 1,
		pageSize: 10,
		total: 0
	})
	const handleChange = (page, pageSize) => {
		pagination.value.current = page
		pagination.value.pageSize = pageSize
		loadData()
	}
	let searchInfo = ref()
	// 打开抽屉
	const onOpen = (record) => {
		searchInfo.value = cloneDeep(record)
		visible.value = true
		loadData()
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
	}
	// 机构分类字典
	let TableData = ref([])
	const loadData = () => {
		pageLoading.value = true
		const params = props.showPagination
			? {
					size: pagination.value.pageSize,
					current: pagination.value.current,
					...searchInfo.value
			  }
			: { ...searchInfo.value }

		return props
			.api(params)
			.then((res) => {
				if (props.showPagination) {
					const { records, current, size, total } = res
					pagination.value.current = current
					pagination.value.pageSize = size
					pagination.value.total = total
					TableData.value = records
					return false
				}
				TableData.value = res
			})
			.finally(() => {
				pageLoading.value = false
			})
	}
	const addRowColor = (_record, index) => {
		return index % 2 === 0 ? 'oddRow' : 'evenRow'
	}
	defineExpose({
		onOpen
	})
</script>
<style lang="less" scoped>
	:deep(.ant-table-thead > tr > th) {
		background: #f5f5f5;
		color: #000;
		border-radius: 0 !important;
		border: 0 !important;
	}
	:deep(.ant-table-body > tr > td) {
		color: #616872 !important; /* 将#your-color-here替换为你想要的颜色代码 */
	}

	:deep(.evenRow) {
		background: #f5f5f5 !important;
	}
</style>

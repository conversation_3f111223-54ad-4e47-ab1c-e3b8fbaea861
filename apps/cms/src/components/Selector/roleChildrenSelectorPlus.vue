<template>
	<a-modal
		v-model:open="visible"
		title="角色选择"
		:width="1200"
		:mask-closable="false"
		:destroy-on-close="true"
		@ok="handleOk"
		@cancel="handleClose"
	>
		<a-row :gutter="10">
			<a-col :span="4" v-if="treeData?.length > 1">
				<a-card size="small" :loading="cardLoading" class="selectorTreeDiv">
					<a-tree
						v-if="treeData"
						v-model:expandedKeys="defaultExpandedKeys"
						:tree-data="treeData"
						:field-names="treeFieldNames"
						@select="treeSelect"
					>
						<template #title="title">
							<div class="truncate">
								<a-tooltip placement="topLeft" :title="title[treeFieldNames.title]">
									{{ title[treeFieldNames.title] }}
								</a-tooltip>
							</div>
						</template>
					</a-tree>
				</a-card>
			</a-col>
			<a-col :span="treeData?.length > 1 ? 10 : 12">
				<div class="table-operator xn-mb10">
					<a-form ref="searchFormRef" name="advanced_search" class="ant-advanced-search-form" :model="searchFormState">
						<div class="flex justify-center items-center gap-2.5">
							<a-form-item name="searchKey" class="flex-1">
								<a-input v-model:value="searchFormState.searchKey" placeholder="请输入角色名" />
							</a-form-item>
							<a-button type="primary" @click="loadData()"> 查询 </a-button>
							<a-button @click="reset()"> 重置 </a-button>
						</div>
					</a-form>
				</div>
				<div class="role-table">
					<a-table
						ref="tableRef"
						size="small"
						:columns="commons"
						:data-source="tableData"
						:expand-row-by-click="true"
						:loading="pageLoading"
						bordered
						:pagination="false"
						:scroll="{ y: 350 }"
					>
						<template #title>
							<span>待选择列表 {{ tableRecordNum }} 条</span>
							<div v-if="!radioModel" class="xn-fdr">
								<a-button type="dashed" size="small" @click="addAllPageRecord">添加当前数据</a-button>
							</div>
						</template>
						<template #bodyCell="{ column, record }">
							<template v-if="column.dataIndex === 'action'">
								<a-button style="margin-left: -2px" type="dashed" size="small" @click="addRecord(record)"
									><PlusOutlined
								/></a-button>
							</template>
							<template v-if="column.dataIndex === 'category'">
								{{ $TOOL.dictTypeData('ROLE_CATEGORY', record.category) }}
							</template>
						</template>
					</a-table>
					<div class="mt-2">
						<a-pagination
							v-if="!isEmpty(tableData)"
							v-model:current="current"
							v-model:page-size="pageSize"
							:total="total"
							size="small"
							showSizeChanger
							@change="paginationChange"
						/>
					</div>
				</div>
			</a-col>
			<a-col :span="treeData?.length > 1 ? 9 : 11" style="padding-top: 41.5px">
				<div class="role-table">
					<a-form ref="formRef" :model="formData">
						<a-table
							ref="selectedTable"
							size="small"
							:columns="selectedCommons"
							:data-source="selectedData"
							:expand-row-by-click="true"
							:loading="selectedTableListLoading"
							:pagination="false"
							bordered
							:scroll="{ y: 350 }"
						>
							<template #title>
								<span>已选择: {{ selectedData.length }}</span>
								<div v-if="!radioModel" class="xn-fdr">
									<a-button type="dashed" danger size="small" @click="delAllRecord">全部移除</a-button>
								</div>
							</template>
							<template #bodyCell="{ column, record }">
								<template v-if="column.dataIndex === 'action'">
									<a-button style="margin-left: -2px" type="dashed" danger size="small" @click="delRecord(record)"
										><MinusOutlined
									/></a-button>
								</template>
								<template v-if="column.dataIndex === 'substationParamList'">
									<a-form-item
										:name="`substationParamList-${record.id}`"
										:rules="[{ required: true, message: '请选择企业' }]"
									>
										<a-select
											:disabled="selectDisabled"
											:options="substationOptions"
											v-model:value="record.substationParamList"
											:filter-option="(input, option) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
											:max-tag-count="1"
											size="middle"
											mode="multiple"
											style="width: 100%"
										></a-select>
									</a-form-item>
								</template>
							</template>
						</a-table>
					</a-form>
				</div>
			</a-col>
		</a-row>
	</a-modal>
</template>

<script setup name="roleSelectorPlus">
	import { message } from 'ant-design-vue'
	import { remove, isEmpty, map } from 'lodash-es'
	import { computed, watch } from 'vue'
	// 弹窗是否打开
	const visible = ref(false)
	// 主表格common
	const commons = [
		{
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: 80
		},
		{
			title: '角色名',
			dataIndex: 'name',
			ellipsis: true
		},
		{
			title: '分类',
			dataIndex: 'category'
		}
	]
	// 选中表格的表格common
	const selectedCommons = computed(() => {
		const c = [
			{
				title: '操作',
				dataIndex: 'action',
				align: 'center',
				width: 80
			},
			{
				title: '角色名',
				dataIndex: 'name',
				ellipsis: true
			}
		]

		// if (props.showSelect) {
		// 	c.push({
		// 		title: '企业',
		// 		dataIndex: 'substationParamList'
		// 	})
		// }

		return c
	})
	// 主表格的ref 名称
	const tableRef = ref()
	// 选中表格的ref 名称
	const selectedTable = ref()
	const tableRecordNum = ref()
	const searchFormState = ref({})
	const searchFormRef = ref()
	const cardLoading = ref(true)
	const pageLoading = ref(false)
	const selectedTableListLoading = ref(false)
	// 替换treeNode 中 title,key,children
	// const treeFieldNames = { children: 'children', title: 'name', key: 'id' }
	// 获取机构树数据
	const treeData = ref()
	//  默认展开二级树的节点id
	const defaultExpandedKeys = ref([])
	const emit = defineEmits({ onBack: null })
	const tableData = ref([])
	const selectedData = ref([])
	const recordIds = ref()
	const props = defineProps({
		rolePageApi: {
			type: Function
		},
		orgTreeApi: {
			type: Function
		},
		showSelect: {
			type: Boolean,
			default: true
		},
		selectOptionsApi: {
			type: Function
		},
		// 是否是单选
		radioModel: {
			type: Boolean,
			default: false,
			required: false
		},
		// 数据是否转换成工作流格式
		dataIsConverterFlw: {
			type: Boolean,
			default: false,
			required: false
		},
		// 是否展示‘全局’这个节点
		roleGlobal: {
			type: Boolean,
			default: true,
			required: false
		},
		checkedRoleListApi: {
			type: Function
		},
		selectDefaultValue: {
			type: [String, Array],
			default: () => '' || []
		},
		selectDisabled: {
			type: Boolean,
			default: false
		},
		treeFieldNames: {
			type: Object,
			default: () => ({ children: 'children', title: 'name', key: 'id' })
		},
		sysParams: {
			type: Boolean,
			default: false
		}
	})
	// 是否是单选
	// eslint-disable-next-line vue/no-setup-props-destructure
	const radioModel = props.radioModel
	// 数据是否转换成工作流格式
	// eslint-disable-next-line vue/no-setup-props-destructure
	const dataIsConverterFlw = props.dataIsConverterFlw
	// 是否展示‘全局’这个节点
	// eslint-disable-next-line vue/no-setup-props-destructure
	const roleGlobal = props.roleGlobal
	// 分页相关
	const current = ref(1) // 当前页数
	const pageSize = ref(20) // 每页条数
	const total = ref(0) // 数据总数
	const substationOptions = ref([])
	const formRef = ref()
	const formData = ref({})
	const viewportHeight = window.innerHeight

	const tabScrollY = computed(() => viewportHeight / 2 - 42 - 78 - 34.5)
	const selecteDTabScrollY = computed(() => viewportHeight / 2 - 42 - 78 - 34.5)
	// 打开弹框
	const showRolePlusModal = (ids) => {
		visible.value = true
		if (dataIsConverterFlw) {
			ids = goDataConverter(ids)
		}
		recordIds.value = ids
		if (props.orgTreeApi) {
			// 获取机构树
			props.orgTreeApi().then((data) => {
				cardLoading.value = false
				if (data !== null) {
					treeData.value = data
					// 树中插入全局角色类型
					if (roleGlobal && !props.sysParams) {
						const globalRoleType = [
							{
								id: 'GLOBAL',
								parentId: '-1',
								name: '全局'
							}
						]
						treeData.value = globalRoleType.concat(data)
					}
					// 默认展开2级
					treeData.value.forEach((item) => {
						// 因为0的顶级
						if (item.parentId === '0') {
							defaultExpandedKeys.value.push(item.id)
							// 取到下级ID
							if (item.children) {
								item.children.forEach((items) => {
									defaultExpandedKeys.value.push(items.id)
								})
							}
						}
					})
				}
			})
		}

		if (props.selectOptionsApi) {
			props.selectOptionsApi().then((data) => {
				substationOptions.value = data
				console.log("=>(roleSelectorPlus.vue:322) data", data);
			})
		}

		searchFormState.value.size = pageSize.value
		loadData()

		if (props.showSelect) {
			if (isEmpty(ids)) {
				return
			}
			selectedData.value = ids
			formData.value = ids.reduce((pre, cur) => {
				return {
					...pre,
					[`substationParamList-${cur.id}`]: cur.substationParamList
				}
			}, {})
		} else if (props.checkedRoleListApi) {
			if (isEmpty(recordIds.value)) {
				return
			}
			const param = {
				idList: recordIds.value
			}
			selectedTableListLoading.value = true
			props
				.checkedRoleListApi(param)
				.then((data) => {
					selectedData.value = data
				})
				.finally(() => {
					selectedTableListLoading.value = false
				})
		}
	}
	// 查询主表格数据
	const loadData = (num = 1, size = 20) => {
		// 如果不是用全局的，我们每次查询加上这个条件
		if (!roleGlobal) {
			searchFormState.value.category = 'ORG'
		}
		searchFormState.value.current = num
		searchFormState.value.size = size
		pageLoading.value = true
		props
			.rolePageApi(searchFormState.value)
			.then((data) => {
				current.value = data.current
				total.value = data.total
				// 重置、赋值
				tableData.value = []
				tableRecordNum.value = 0
				tableData.value = data.records
				if (data.records) {
					tableRecordNum.value = data.records.length
				} else {
					tableRecordNum.value = 0
				}
			})
			.finally(() => {
				pageLoading.value = false
			})
	}
	// pageSize改变回调分页事件
	const paginationChange = (page, pageSize) => {
		// searchFormState.value.current = page
		// searchFormState.value.size = pageSize
		loadData(page, pageSize)
	}
	const judge = () => {
		return !(radioModel && selectedData.value.length > 0)
	}
	// 添加记录
	const addRecord = (record) => {
		console.log("=>(roleSelectorPlus.vue:396) record", record);
		const [first] = substationOptions.value
		if (!judge()) {
			message.warning('只可选择一条')
			return
		}
		const selectedRecord = selectedData.value.filter((item) => item.id === record.id)
		if (selectedRecord.length === 0) {
			selectedData.value.unshift({ ...record, substationParamList: [first.value] || props.selectDefaultValue })
		} else {
			message.warning('该记录已存在')
		}
	}
	// 添加全部
	const addAllPageRecord = () => {
		let newArray = selectedData.value.concat(tableData.value)
		let list = []
		for (let item1 of newArray) {
			let flag = true
			for (let item2 of list) {
				if (item1.id === item2.id) {
					flag = false
				}
			}
			if (flag) {
				list.push(item1)
			}
		}
		selectedData.value = list
	}
	// 删减记录
	const delRecord = (record) => {
		remove(selectedData.value, (item) => item.id === record.id)
	}
	// 删减记录
	const delAllRecord = () => {
		selectedData.value = []
	}
	// 点击树查询
	const treeSelect = (selectedKeys) => {
		searchFormState.value.current = 1
		if (selectedKeys.length > 0) {
			if (props.sysParams) {
				searchFormState.value.category = selectedKeys.toString()
			} else {
				if (selectedKeys[0] === 'GLOBAL') {
					searchFormState.value.category = selectedKeys[0]
					delete searchFormState.value.orgId
				} else {
					searchFormState.value.orgId = selectedKeys.toString()
					delete searchFormState.value.category
				}
			}
		} else {
			delete searchFormState.value.category
			delete searchFormState.value.orgId
		}
		loadData()
	}

	watch(
		() => selectedData,
		() => {
			if (props.showSelect) {
				formData.value = {}
				selectedData.value.forEach((item) => {
					formData.value[`substationParamList-${item.id}`] = item.substationParamList
				})
			}
		},
		{
			deep: true
		}
	)

	const handleOk = async () => {
		try {
			await formRef.value.validate()
			const value = []
			selectedData.value.forEach((item) => {
				const obj = props.showSelect
					? {
							roleId: item.id,
							roleName: item.name,
							substationParamList: map(item.substationParamList, (id) => ({ id: `${id}` }))
					  }
					: {
							id: item.id,
							name: item.name
					  }
				value.push(obj)
			})
			// 判断是否做数据的转换为工作流需要的
			if (dataIsConverterFlw) {
				emit('onBack', outDataConverter(value))
			} else {
				emit('onBack', value)
			}
			handleClose()
		} catch (error) {
			console.log(error)
		}
	}
	// 重置
	const reset = () => {
		delete searchFormState.value.searchKey
		loadData()
	}
	const handleClose = () => {
		searchFormState.value = {}
		tableRecordNum.value = 0
		tableData.value = []
		current.value = 1
		pageSize.value = 20
		total.value = 0
		selectedData.value = []
		visible.value = false
	}

	// 数据进入后转换
	const goDataConverter = (data) => {
		const resultData = []
		if (data.length > 0) {
			const values = data[0].value.split(',')
			if (JSON.stringify(values) !== '[""]') {
				for (let i = 0; i < values.length; i++) {
					resultData.push(values[i])
				}
			}
		}
		return resultData
	}
	// 数据出口转换器
	const outDataConverter = (data) => {
		const obj = {}
		let label = ''
		let value = ''
		for (let i = 0; i < data.length; i++) {
			if (data.length === i + 1) {
				label = label + data[i].name
				value = value + data[i].id
			} else {
				label = label + data[i].name + ','
				value = value + data[i].id + ','
			}
		}
		obj.key = 'ROLE'
		obj.label = label
		obj.value = value
		obj.extJson = ''
		return obj
	}
	defineExpose({
		showRolePlusModal
	})
</script>

<style lang="less" scoped>
	.selectorTreeDiv {
		height: 480px;
		overflow-y: auto;
		overflow-x: hidden;
	}
	:deep(.ant-table-body) {
		height: 350px !important;
	}
	:deep(.ant-table-wrapper) {
		border-bottom: 1px solid #eee;
		border-radius: 4px;
	}
	:deep(.ant-empty) {
		margin: 132px 0;
	}
	.cardTag {
		margin-left: 20px;
	}
	.primarySele {
		margin-right: 20px;
	}
	.ant-form-item {
		margin-bottom: 0 !important;
	}
	.role-table {
		overflow: auto;
		max-height: 550px;
	}
</style>
<style lang="less" scoped>
	@import '@/components/Tree/tree.less';
	.ant-card {
		:deep(.ant-card-body) {
			height: 100%;
			flex: 1;
			display: flex;
			flex-flow: column nowrap;
			.ant-input-affix-wrapper {
				height: auto;
			}
			.ant-tree {
				flex: 1;
				overflow: hidden;
				overflow-y: auto;

				.ant-tree-node-content-wrapper {
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}
</style>

<template>
	<!-- 封装了 统一loading效果的按钮
        使用方式：<QtButton type="primary" @click="(next) => saveUpdate(0, next)">保存</QtButton>
                  在saveUpdate方法的接口请求完成后，手动调用next() 即可取消loading状态
                  simplePost(api, params,(data) => {
                    next()
                  })
    -->
	<a-button v-bind="$attrs" @click="handleClick($event)" :loading="loading">
		<!-- <slot></slot> -->
		<template v-for="slotKey in slotKeys" #[slotKey]>
			<slot :name="slotKey" />
		</template>
	</a-button>
</template>

<script setup name="QtButton">
	const props = defineProps({
		isPrevent: {
			type: Boolean
		}
	})
	const slots = useSlots()
	const slotKeys = computed(() => {
		return Object.keys(slots)
	})
	const $emit = defineEmits(['click'])
	const loading = ref(false)
	const handleClick = (event) => {
		if (props.isPrevent) {
			event.preventDefault()
		}
		loading.value = true
		new Promise((resolve) => {
			// 向父组件暴露一个resolve，用于父组件中调用 next()取消loading状态
			$emit('click', resolve)
		}).then(() => {
			loading.value = false
		})
	}
</script>
